import 'user.dart';
import 'business_category.dart';

class Post {
  final String id;
  final User author;
  final String content;
  final List<String> images;
  final BusinessCategory? category;
  final DateTime createdAt;
  final int likesCount;
  final int commentsCount;
  final int sharesCount;
  final bool isLiked;
  final PostType type;
  final double? price;
  final String? location;
  final List<String> tags;

  Post({
    required this.id,
    required this.author,
    required this.content,
    this.images = const [],
    this.category,
    required this.createdAt,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.sharesCount = 0,
    this.isLiked = false,
    this.type = PostType.regular,
    this.price,
    this.location,
    this.tags = const [],
  });

  static List<Post> getDemoPosts() {
    final user = User.demo();
    final categories = BusinessCategory.getAllCategories();
    
    return [
      Post(
        id: '1',
        author: user,
        content: 'هاتف iPhone 15 Pro Max جديد للبيع! حالة ممتازة مع جميع الملحقات الأصلية. السعر قابل للتفاوض.',
        images: ['https://via.placeholder.com/400x300'],
        category: categories.firstWhere((c) => c.id == 'electronics'),
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        likesCount: 45,
        commentsCount: 12,
        sharesCount: 3,
        type: PostType.product,
        price: 1200.0,
        location: 'بغداد - الكرادة',
        tags: ['هاتف', 'آيفون', 'جديد'],
      ),
      Post(
        id: '2',
        author: User(
          id: '2',
          name: 'فاطمة أحمد',
          email: '<EMAIL>',
          profileImage: 'https://via.placeholder.com/150',
          userType: UserType.seller,
          isPremium: true,
          joinDate: DateTime.now().subtract(const Duration(days: 60)),
        ),
        content: 'مطعم جديد في منطقة الجادرية! نقدم أشهى الأطباق العراقية والعربية. افتتاح خاص مع خصم 20% على جميع الوجبات.',
        images: ['https://via.placeholder.com/400x300', 'https://via.placeholder.com/400x300'],
        category: categories.firstWhere((c) => c.id == 'restaurants'),
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
        likesCount: 89,
        commentsCount: 23,
        sharesCount: 15,
        type: PostType.service,
        location: 'بغداد - الجادرية',
        tags: ['مطعم', 'طعام', 'افتتاح'],
      ),
      Post(
        id: '3',
        author: User(
          id: '3',
          name: 'محمد علي',
          email: '<EMAIL>',
          profileImage: 'https://via.placeholder.com/150',
          userType: UserType.teacher,
          isPremium: true,
          joinDate: DateTime.now().subtract(const Duration(days: 90)),
        ),
        content: 'كورس جديد في البرمجة والتطوير! تعلم Flutter وطور تطبيقاتك الخاصة. محاضرات مباشرة ومسجلة مع شهادة معتمدة.',
        images: ['https://via.placeholder.com/400x300'],
        category: categories.firstWhere((c) => c.id == 'education'),
        createdAt: DateTime.now().subtract(const Duration(hours: 8)),
        likesCount: 156,
        commentsCount: 34,
        sharesCount: 28,
        type: PostType.educational,
        price: 50.0,
        tags: ['برمجة', 'تعليم', 'كورس'],
      ),
      Post(
        id: '4',
        author: user,
        content: 'شقة للإيجار في منطقة راقية! 3 غرف نوم، 2 حمام، مطبخ مجهز، موقف سيارة. إطلالة رائعة ومنطقة هادئة.',
        images: [
          'https://via.placeholder.com/400x300',
          'https://via.placeholder.com/400x300',
          'https://via.placeholder.com/400x300'
        ],
        category: categories.firstWhere((c) => c.id == 'real_estate'),
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        likesCount: 67,
        commentsCount: 18,
        sharesCount: 9,
        type: PostType.product,
        price: 800.0,
        location: 'بغداد - المنصور',
        tags: ['شقة', 'إيجار', 'عقار'],
      ),
    ];
  }
}

enum PostType {
  regular,
  product,
  service,
  educational,
  promotional,
  ad,
}

extension PostTypeExtension on PostType {
  String get displayName {
    switch (this) {
      case PostType.regular:
        return 'منشور عادي';
      case PostType.product:
        return 'منتج';
      case PostType.service:
        return 'خدمة';
      case PostType.educational:
        return 'تعليمي';
      case PostType.promotional:
        return 'ترويجي';
      case PostType.ad:
        return 'إعلان';
    }
  }
}
