<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - تطبيق الخدمات المحلية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }

        /* الشريط العلوي */
        .header {
            background: linear-gradient(90deg, #16CCC8, #227FCC);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .app-name {
            font-size: 40px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
            max-width: 600px;
            margin: 0 30px;
        }

        .search-input {
            width: 100%;
            height: 40px;
            padding: 12px 20px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            background: rgba(255,255,255,0.9);
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
        }

        .search-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: white;
            font-size: 24px;
            padding: 10px;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .menu-icon {
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid #28a745;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.6);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-name {
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        /* شريط التنقل */
        .nav-bar {
            background: white;
            position: fixed;
            top: 70px;
            left: 0;
            right: 0;
            z-index: 999;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .nav-items {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 25px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 18px;
            cursor: pointer;
            transition: all 0.3s;
            color: #495057;
            font-weight: 500;
            position: relative;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-item i {
            font-size: 24px;
        }

        .nav-item.home-item {
            margin-left: 80px;
        }

        .nav-item.has-notification i {
            color: #ff0000 !important;
            -webkit-text-fill-color: #ff0000 !important;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #ff0000;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 130px;
            padding: 20px;
        }

        /* شريط الخدمات */
        .services-section {
            margin-bottom: 30px;
            position: relative;
        }

        .services-container {
            position: relative;
            overflow: hidden;
            padding: 10px 0;
        }

        .services-wrapper {
            display: flex;
            gap: 15px;
            transition: transform 1s ease;
            width: fit-content;
        }

        .nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 35px;
            height: 35px;
            border: none;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            z-index: 10;
        }

        .nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .nav-arrow.prev {
            right: 10px;
        }

        .nav-arrow.next {
            left: 10px;
        }

        .service-card {
            width: 180px;
            height: 260px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: transform 0.3s;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
        }

        .service-card:hover {
            transform: translateY(-5px);
        }

        .service-image {
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .service-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .service-image-placeholder {
            font-size: 40px;
            color: #666;
        }

        .service-name {
            height: 60px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 15px;
            font-weight: bold;
            text-align: center;
        }

        /* شريط العرض المميز */
        .premium-offers-section {
            margin-bottom: 30px;
            position: relative;
        }

        .premium-offers-container {
            position: relative;
            overflow: hidden;
            padding: 10px 0;
        }

        .premium-offers-wrapper {
            display: flex;
            gap: 20px;
            transition: transform 1s ease;
            width: fit-content;
        }

        .premium-offer-card {
            width: 380px;
            height: 200px;
            border-radius: 15px;
            border: 3px solid #FFD700;
            background: linear-gradient(135deg, #FFF8DC, #FFFACD);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            flex-shrink: 0;
            overflow: hidden;
        }

        .premium-offer-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.2);
        }

        .premium-offer-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 215, 0, 0.1) 50%, transparent 70%);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .premium-offer-content {
            width: 100%;
            height: 100%;
            position: relative;
            z-index: 2;
            overflow: hidden;
            border-radius: 12px;
        }

        .premium-offer-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 12px;
        }

        .premium-offer-image-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
            border-radius: 12px;
        }

        .premium-offer-overlay {
            position: absolute;
            top: 0;
            right: 0;
            background: linear-gradient(135deg, rgba(0,0,0,0.5), rgba(0,0,0,0.3));
            color: white;
            padding: 15px;
            z-index: 3;
            border-radius: 0 12px 0 15px;
            max-width: 70%;
        }

        .premium-offer-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.9);
            line-height: 1.2;
        }

        .premium-offer-subtitle {
            font-size: 11px;
            opacity: 0.95;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.9);
            line-height: 1.3;
        }

        .premium-offer-footer {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 35px;
            z-index: 10;
        }

        .premium-offer-footer::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 23px;
            z-index: -1;
        }

        .premium-badge {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            box-shadow:
                0 4px 15px rgba(255, 165, 0, 0.4),
                0 0 20px rgba(255, 255, 0, 0.6),
                0 0 40px rgba(255, 255, 0, 0.3);
            animation: badge-glow 2s ease-in-out infinite alternate;
        }

        .premium-nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            border: none;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            z-index: 10;
            box-shadow: 0 4px 15px rgba(255, 165, 0, 0.4);
        }

        .premium-nav-arrow:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(255, 165, 0, 0.6);
        }

        .premium-nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: translateY(-50%);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .premium-nav-arrow.prev {
            right: 10px;
        }

        .premium-nav-arrow.next {
            left: 10px;
        }

        /* المحتوى */
        .content-area {
            display: flex;
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px 0;
        }

        .left-sidebar {
            flex: 1;
            order: 1;
            min-width: 600px;
        }

        .right-sidebar {
            width: 350px;
            order: 2;
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-close-btn {
            background: none;
            border: none;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
        }

        .section-close-btn:hover {
            background: #f0f0f0;
            color: #333;
        }



        /* إعلانات الفيديو - الشريط الأيسر */
        .video-ads-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .video-ads-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .video-ad-item {
            background: white;
            border-radius: 15px;
            padding: 0;
            color: #333;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            position: relative;
        }

        .video-close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            border: none;
            color: white;
            font-size: 14px;
            cursor: pointer;
            padding: 6px;
            border-radius: 50%;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            z-index: 10;
        }

        .video-close-btn:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }

        .video-ad-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .video-thumbnail {
            width: 100%;
            height: 180px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15px 15px 0 0;
        }

        .video-thumbnail::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shimmer 3s infinite;
        }

        .play-button {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #667eea;
            z-index: 2;
            position: relative;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .video-duration {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .video-ad-content {
            padding: 15px;
        }

        .video-ad-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .video-ad-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .video-ad-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            line-height: 1.3;
        }

        .video-ad-description {
            font-size: 12px;
            color: #666;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .video-ad-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .video-ad-reward {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(245, 87, 108, 0.3);
        }

        .video-ad-views {
            font-size: 11px;
            color: #999;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .earnings-info {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .earnings-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .earnings-amount {
            font-size: 24px;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 8px;
        }

        .earnings-description {
            font-size: 12px;
            opacity: 0.9;
            line-height: 1.4;
        }

        .earnings-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.2);
        }

        .earnings-stat {
            text-align: center;
            flex: 1;
        }

        .earnings-stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #FFD700;
        }

        .earnings-stat-label {
            font-size: 10px;
            opacity: 0.8;
            margin-top: 2px;
        }

        /* المنشورات المميزة - الشريط الأيمن */
        .trending-posts-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .trending-posts-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .trending-post {
            border: 1px solid #e1e8ed;
            border-radius: 20px;
            padding: 25px;
            transition: all 0.3s;
            cursor: pointer;
            margin-bottom: 25px;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .trending-post:hover {
            border-color: #16CCC8;
            box-shadow: 0 8px 25px rgba(22, 204, 200, 0.15);
        }

        .trending-post-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
            position: relative;
        }

        .trending-post-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 6px 20px rgba(22, 204, 200, 0.4);
        }

        .trending-post-info {
            flex: 1;
        }

        .trending-post-author {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .trending-post-category {
            font-size: 14px;
            color: #666;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 6px 12px;
            border-radius: 15px;
            display: inline-block;
            font-weight: 500;
        }

        .seller-rating {
            margin-top: 8px;
        }

        .rating-stars {
            display: flex;
            align-items: center;
            gap: 2px;
            margin-bottom: 4px;
        }

        .star {
            font-size: 16px;
            color: #ddd;
            transition: color 0.3s;
        }

        .star.filled {
            color: #FFD700;
        }

        .rating-info {
            font-size: 12px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .membership-duration {
            font-size: 11px;
            color: #888;
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 8px;
        }

        .trending-post-content {
            font-size: 17px;
            color: #333;
            line-height: 1.7;
            margin-bottom: 20px;
        }

        .trending-post-images {
            margin-bottom: 20px;
        }

        /* صورة واحدة - عرض كامل */
        .trending-post-images.single-image .trending-post-image {
            width: 100%;
            height: 500px;
            border-radius: 15px;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid #e1e8ed;
        }

        /* صورتان - جنباً إلى جنب */
        .trending-post-images.two-images {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .trending-post-images.two-images .trending-post-image {
            width: 100%;
            height: 300px;
            border-radius: 12px;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid #e1e8ed;
        }

        /* ثلاث صور أو أكثر - تخطيط شبكي */
        .trending-post-images.multiple-images {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .trending-post-images.multiple-images .trending-post-image:first-child {
            grid-row: span 2;
            height: 400px;
        }

        .trending-post-images.multiple-images .trending-post-image {
            width: 100%;
            height: 196px;
            border-radius: 12px;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid #e1e8ed;
        }

        /* تخطيط خاص للمطعم - صورة عريضة في الأعلى وصورتان تحتها */
        .trending-post-images.restaurant-layout {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
        }

        .trending-post-images.restaurant-layout .trending-post-image:first-child {
            width: 100%;
            height: 250px;
            border-radius: 12px;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid #e1e8ed;
        }

        .trending-post-images.restaurant-layout .food-images {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .trending-post-images.restaurant-layout .food-images .trending-post-image {
            width: 100%;
            height: 200px;
            border-radius: 12px;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid #e1e8ed;
        }

        .trending-post-image:hover {
            border-color: #16CCC8;
        }

        .trending-post-image-placeholder {
            width: 100%;
            height: 300px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 48px;
            border: 1px solid #e1e8ed;
        }

        /* عداد الصور الإضافية */
        .more-images-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            border-radius: 12px;
            cursor: pointer;
        }

        .trending-post-stats {
            display: flex;
            align-items: center;
            justify-content: space-around;
            font-size: 14px;
            color: #666;
            padding: 15px 0;
            border-top: 1px solid #f0f0f0;
            border-bottom: 1px solid #f0f0f0;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            flex: 1;
        }

        .stat-icon {
            font-size: 18px;
        }

        .stat-number {
            font-weight: 600;
            font-size: 16px;
        }

        .likes-stat .stat-icon {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .comments-stat .stat-icon {
            color: #1877f2;
        }

        .shares-stat .stat-icon {
            color: #42b883;
        }

        .trending-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .crown-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: bold;
            box-shadow:
                0 3px 12px rgba(255, 215, 0, 0.6),
                0 0 16px rgba(255, 215, 0, 0.4),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
            border: 2px solid #FFD700;
        }

        .crown-text {
            color: #FFD700;
            font-size: 13px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }



        /* أزرار التحكم في الأعلى */
        .post-controls {
            position: absolute;
            top: 15px;
            left: 15px;
            display: flex;
            gap: 15px;
            z-index: 10;
        }

        .close-post-btn {
            background: none;
            border: none;
            color: #333;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-post-btn:hover {
            color: #666;
        }

        .trending-post-actions {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin-top: 15px;
            padding: 15px 0;
            border-top: 1px solid #f0f0f0;
            gap: 10px;
        }

        .action-btn {
            background: none;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 18px;
            border-radius: 25px;
            transition: all 0.3s;
            font-size: 16px;
            font-weight: 600;
            flex: 1;
            justify-content: center;
            min-width: 0;
            color: #666;
        }

        .like-btn.liked i {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .favorite-btn.favorited i {
            color: #FFD700;
        }

        /* نافذة المشاركة المحسنة */
        .share-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .share-modal.show {
            display: flex;
        }

        .share-modal-content {
            background: white;
            border-radius: 15px;
            padding: 25px;
            max-width: 500px;
            width: 90%;
            position: relative;
        }

        .share-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e1e8ed;
        }

        .share-modal-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .share-close-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: #666;
            cursor: pointer;
            padding: 5px;
        }

        .share-close-btn:hover {
            color: #333;
        }

        .share-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .share-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            padding: 20px;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .share-option:hover {
            background: #f8f9fa;
            border-color: #16CCC8;
        }

        .share-option i {
            font-size: 32px;
        }

        .share-option span {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        /* نافذة تأكيد المشاركة */
        .share-confirm {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            display: none;
            z-index: 1001;
        }

        .share-confirm.show {
            display: block;
        }

        .share-confirm-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 15px;
        }

        .share-confirm-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .share-cancel-btn {
            background: #f8f9fa;
            color: #666;
        }

        .share-cancel-btn:hover {
            background: #e9ecef;
        }

        .share-submit-btn {
            background: #16CCC8;
            color: white;
        }

        .share-submit-btn:hover {
            background: #14b3af;
        }

        /* قسم التعليقات */
        .comments-section {
            margin-top: 15px;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            display: none;
        }

        .comments-section.show {
            display: block;
        }

        .comments-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #e1e8ed;
            margin-bottom: 15px;
        }

        .comments-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .comments-toggle {
            background: none;
            border: none;
            color: #16CCC8;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
        }

        .comments-toggle:hover {
            text-decoration: underline;
        }

        .comments-list {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 10px;
        }

        .comment-item {
            display: flex;
            gap: 8px;
            margin-bottom: 4px;
            padding: 0;
        }

        .comment-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            flex-shrink: 0;
        }

        .comment-avatar.user-avatar {
            background: url('images/Hussein Nihad.png') center/cover;
            border: 2px solid #FFD700;
        }

        .add-comment-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: url('images/Hussein Nihad.png') center/cover;
            border: 2px solid #FFD700;
            flex-shrink: 0;
        }

        .comment-content {
            flex: 1;
        }

        .comment-bubble {
            background: white;
            border-radius: 18px;
            padding: 6px 10px;
            margin-bottom: 1px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .comment-author {
            font-size: 13px;
            font-weight: bold;
            color: #333;
            margin-bottom: 0px;
        }

        .comment-text {
            font-size: 14px;
            color: #333;
            line-height: 1.2;
        }

        .comment-time {
            font-size: 12px;
            color: #666;
            margin-left: 8px;
        }

        .comment-actions {
            display: flex;
            gap: 10px;
            margin-left: 8px;
            margin-top: 0px;
        }

        .comment-action {
            font-size: 12px;
            color: #666;
            cursor: pointer;
            font-weight: 600;
        }

        .comment-action:hover {
            text-decoration: underline;
        }

        .comment-action.edit {
            color: #16CCC8;
        }

        .comment-action.delete {
            color: #e74c3c;
        }

        .comment-menu {
            position: relative;
            display: inline-block;
            margin-left: 8px;
        }

        .comment-menu-btn {
            background: #f0f0f0;
            border: 1px solid #ddd;
            color: #666;
            cursor: pointer;
            padding: 6px 8px;
            border-radius: 6px;
            font-size: 12px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .comment-menu-btn:hover {
            background: #e0e0e0;
            color: #333;
            border-color: #bbb;
        }

        .comment-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            padding: 8px 0;
            display: none;
            z-index: 9999;
            min-width: 140px;
            margin-top: 5px;
        }

        .comment-dropdown.show {
            display: block;
            animation: fadeIn 0.2s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .comment-dropdown-item {
            padding: 10px 15px;
            cursor: pointer;
            font-size: 13px;
            color: #333;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .comment-dropdown-item:hover {
            background: #f8f9fa;
        }

        .comment-dropdown-item.edit {
            color: #16CCC8;
        }

        .comment-dropdown-item.edit:hover {
            background: rgba(22, 204, 200, 0.1);
        }

        .comment-dropdown-item.delete {
            color: #e74c3c;
        }

        .comment-dropdown-item.delete:hover {
            background: rgba(231, 76, 60, 0.1);
        }

        .comment-dropdown-item i {
            font-size: 12px;
            width: 14px;
        }

        /* نافذة الإشعارات */
        .notifications-modal {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 380px;
            height: 500px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            display: none;
            flex-direction: column;
            z-index: 2000;
            overflow: hidden;
        }

        .notifications-modal.show {
            display: flex;
        }

        .notifications-content {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .notifications-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e8ed;
            background: #f8f9fa;
        }

        .notifications-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .notifications-close {
            background: none;
            border: none;
            font-size: 18px;
            color: #666;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s;
        }

        .notifications-close:hover {
            background: #e9ecef;
            color: #333;
        }

        /* شريط التبويب */
        .notifications-tabs {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            border-bottom: 1px solid #e1e8ed;
            background: white;
        }

        .tabs-left {
            display: flex;
            gap: 20px;
        }

        .tab-item {
            padding: 8px 0;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-item.active {
            color: #16CCC8;
            border-bottom-color: #16CCC8;
        }

        .tab-item:hover {
            color: #16CCC8;
        }

        .notifications-menu {
            position: relative;
        }

        .notifications-menu-btn {
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s;
        }

        .notifications-menu-btn:hover {
            background: #f0f0f0;
        }

        .notifications-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 5px 0;
            display: none;
            z-index: 100;
            min-width: 200px;
        }

        .notifications-dropdown.show {
            display: block;
        }

        .notifications-dropdown-item {
            padding: 10px 15px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notifications-dropdown-item:hover {
            background: #f8f9fa;
        }

        .notifications-list {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        .notification-item {
            display: flex;
            gap: 12px;
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.3s;
            cursor: pointer;
            position: relative;
        }

        .notification-item:hover {
            background: #f8f9fa;
        }

        .notification-item.unread {
            background: rgba(22, 204, 200, 0.03);
        }

        .notification-item.unread::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: #16CCC8;
        }

        .notification-checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #ccc;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .notification-checkbox.selected {
            background: #16CCC8;
            border-color: #16CCC8;
        }

        .notification-checkbox.selected::after {
            content: '✓';
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        .notification-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            flex-shrink: 0;
            overflow: hidden;
        }

        .notification-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .notification-avatar.system {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .notification-avatar.offer {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .notification-content {
            flex: 1;
            min-width: 0;
        }

        .notification-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }

        .notification-author {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }

        .notification-action {
            font-size: 14px;
            color: #666;
        }

        .notification-text {
            font-size: 13px;
            color: #666;
            line-height: 1.3;
            margin-bottom: 4px;
        }

        .notification-time {
            font-size: 12px;
            color: #999;
        }

        /* أزرار التحكم */
        .notifications-footer {
            padding: 15px 20px;
            border-top: 1px solid #e1e8ed;
            background: #f8f9fa;
        }

        .delete-selected-btn {
            width: 100%;
            padding: 10px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 10px;
            display: none;
        }

        .delete-selected-btn:hover {
            background: #c0392b;
        }

        .delete-selected-btn.show {
            display: block;
        }

        .show-old-notifications-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .show-old-notifications-btn:hover {
            background: linear-gradient(135deg, #14b3af, #1e6bb8);
        }

        /* نظام الرسائل */
        .messages-modal {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 380px;
            height: 500px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            display: none;
            flex-direction: column;
            z-index: 2000;
            overflow: hidden;
        }

        .messages-modal.show {
            display: flex;
        }

        .messages-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e8ed;
            background: #f8f9fa;
        }

        .messages-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .messages-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .messages-btn {
            background: none;
            border: none;
            font-size: 16px;
            color: #666;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .messages-btn:hover {
            background: #e9ecef;
            color: #333;
        }

        .messages-menu {
            position: relative;
        }

        .messages-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            padding: 8px 0;
            display: none;
            z-index: 2100;
            min-width: 250px;
            max-height: 300px;
            overflow-y: auto;
        }

        .messages-dropdown.show {
            display: block;
        }

        .messages-dropdown-item {
            padding: 14px 18px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 15px;
            white-space: nowrap;
            border-bottom: 1px solid #f0f0f0;
        }

        .messages-dropdown-item:last-child {
            border-bottom: none;
        }

        .messages-dropdown-item:hover {
            background: #f8f9fa;
        }

        .messages-dropdown-item i {
            color: #666;
            width: 18px;
            font-size: 14px;
            text-align: center;
        }

        /* البحث والتبويبات */
        .messages-search {
            padding: 15px 20px;
            border-bottom: 1px solid #e1e8ed;
        }

        .messages-search-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .messages-search-input {
            width: 100%;
            padding: 10px 45px 10px 15px;
            border: 1px solid #e1e8ed;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
            transition: all 0.3s;
        }

        .messages-search-input:focus {
            border-color: #16CCC8;
            box-shadow: 0 0 0 2px rgba(22, 204, 200, 0.2);
        }

        .messages-search-icon {
            position: absolute;
            right: 15px;
            color: #e1e8ed;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .messages-search-input:focus + .messages-search-icon {
            color: #16CCC8;
        }

        .messages-tabs {
            display: flex;
            padding: 10px 20px;
            border-bottom: 1px solid #e1e8ed;
            background: white;
        }

        .messages-tab {
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: #666;
            border-radius: 20px;
            transition: all 0.3s;
            margin-right: 10px;
        }

        .messages-tab.active {
            background: #16CCC8;
            color: white;
        }

        .messages-tab:hover {
            background: #f0f0f0;
        }

        .messages-tab.active:hover {
            background: #14b3af;
        }

        .messages-list {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        .message-item {
            display: flex;
            gap: 12px;
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.3s;
        }

        .message-item:hover {
            background: #f8f9fa;
        }

        .message-item.unread {
            background: rgba(22, 204, 200, 0.03);
        }

        .message-item.unread::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: #16CCC8;
        }

        .message-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            flex-shrink: 0;
        }

        .message-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .message-content {
            flex: 1;
            min-width: 0;
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .message-name {
            font-size: 15px;
            font-weight: bold;
            color: #333;
        }

        .message-time {
            font-size: 12px;
            color: #999;
        }

        .message-preview {
            font-size: 14px;
            color: #666;
            line-height: 1.3;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .message-preview.unread {
            font-weight: 600;
            color: #333;
        }

        .message-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #16CCC8;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        /* أزرار التحكم */
        .messages-footer {
            padding: 15px 20px;
            border-top: 1px solid #e1e8ed;
            background: #f8f9fa;
        }

        .messages-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .delete-selected-messages-btn {
            flex: 1;
            padding: 8px 12px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: none;
        }

        .delete-selected-messages-btn:hover {
            background: #c0392b;
        }

        .delete-selected-messages-btn.show {
            display: block;
        }

        .show-old-messages-btn {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .show-old-messages-btn:hover {
            background: linear-gradient(135deg, #14b3af, #1e6bb8);
        }

        /* النافذة المكبرة */
        .messages-fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            display: none;
            flex-direction: column;
            z-index: 3000;
        }

        .messages-fullscreen.show {
            display: flex;
        }

        .messages-fullscreen-header {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .messages-fullscreen-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .messages-fullscreen-menu {
            position: relative;
        }

        .messages-fullscreen-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            padding: 8px 0;
            display: none;
            z-index: 3100;
            min-width: 220px;
            color: #333;
        }

        .messages-fullscreen-dropdown.show {
            display: block;
        }

        .messages-fullscreen-dropdown-item {
            padding: 12px 15px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .messages-fullscreen-dropdown-item:hover {
            background: #f8f9fa;
        }

        .messages-fullscreen-dropdown-item i {
            color: #666;
            width: 16px;
        }

        .messages-fullscreen-title {
            font-size: 20px;
            font-weight: bold;
        }

        .messages-fullscreen-search {
            flex: 1;
            max-width: 400px;
            margin: 0 20px;
        }

        .messages-fullscreen-search {
            position: relative;
        }

        .messages-fullscreen-search input {
            width: 100%;
            padding: 8px 40px 8px 15px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
        }

        .messages-fullscreen-search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 14px;
            cursor: pointer;
        }

        .messages-fullscreen-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }



        .messages-sidebar {
            width: 380px;
            border-right: 1px solid #e1e8ed;
            display: flex;
            flex-direction: column;
        }

        .messages-chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
            border-radius: 20px;
            margin: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border: 1px solid #e1e8ed;
            overflow: hidden;
            max-width: 600px;
        }

        /* بانل بروفايل المستخدم النشط */
        .user-profile-panel {
            width: 380px;
            background: white;
            border-radius: 20px;
            margin: 20px 0 20px 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border: 1px solid #e1e8ed;
            overflow-y: auto;
            overflow-x: hidden;
            display: none;
            height: calc(100vh - 80px);
            min-height: 700px;
            position: relative;
            z-index: 100;
        }

        /* المحتوى الداخلي للبانل */
        .profile-content {
            display: flex;
            flex-direction: column;
            min-height: 100%;
        }

        .user-profile-panel.show {
            display: flex;
        }

        /* رأس البروفايل */
        .profile-header {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            padding: 20px;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 10;
            transition: all 0.4s ease;
            transform: translateY(0);
        }

        .profile-header.hidden {
            transform: translateY(-100%);
            position: absolute;
            width: 100%;
        }



        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 15px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            overflow: hidden;
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .profile-status {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 15px;
        }

        /* تقييم النجوم */
        .profile-rating {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .stars {
            display: flex;
            gap: 2px;
        }

        .star {
            color: #ffd700;
            font-size: 16px;
        }

        .star.empty {
            color: rgba(255, 255, 255, 0.3);
        }

        .rating-text {
            font-size: 14px;
            opacity: 0.9;
        }

        /* زر المتابعة */
        .follow-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }

        .follow-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .follow-btn.following {
            background: rgba(46, 204, 113, 0.8);
            border-color: rgba(46, 204, 113, 0.9);
        }

        .profile-stats {
            display: flex;
            justify-content: space-around;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            padding: 10px;
            backdrop-filter: blur(10px);
        }

        .profile-stat {
            text-align: center;
        }

        .profile-stat-number {
            font-size: 16px;
            font-weight: bold;
            display: block;
        }

        .profile-stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        /* شريط البحث */
        .posts-search {
            padding: 15px 20px;
            border-bottom: 1px solid #e1e8ed;
            background: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 9;
            transition: all 0.4s ease;
            transform: translateY(0);
        }

        .posts-search.hidden {
            transform: translateY(-100%);
            position: absolute;
            width: 100%;
        }

        .search-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-input {
            width: 100%;
            padding: 10px 40px 10px 15px;
            border: 1px solid #e1e8ed;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
            transition: all 0.3s;
            background: white;
        }

        .search-input:focus {
            border-color: #16CCC8;
            box-shadow: 0 0 0 2px rgba(22, 204, 200, 0.2);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .search-input:focus + .search-icon {
            color: #16CCC8;
        }

        /* قسم المنشورات */
        .posts-section {
            flex: 1;
            padding: 0;
        }

        .posts-header {
            padding: 10px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e8ed;
            font-weight: bold;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
            flex-shrink: 0;
            font-size: 14px;
        }

        .posts-count {
            background: #16CCC8;
            color: white;
            padding: 3px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
        }

        /* تبويبات المحتوى */
        .content-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e8ed;
            position: sticky;
            top: 0;
            z-index: 8;
            transition: all 0.4s ease;
            transform: translateY(0);
        }

        .content-tabs.hidden {
            transform: translateY(-100%);
            position: absolute;
            width: 100%;
        }

        .content-tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            color: #666;
            transition: all 0.3s;
            border-bottom: 2px solid transparent;
        }

        .content-tab.active {
            color: #16CCC8;
            border-bottom-color: #16CCC8;
            background: white;
        }

        .content-tab:hover {
            color: #16CCC8;
            background: rgba(22, 204, 200, 0.1);
        }

        /* منشور واحد */
        .user-post {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            margin-bottom: 5px;
        }

        .user-post:hover {
            background: #f8f9fa;
            transform: translateX(-2px);
        }

        .user-post:last-child {
            border-bottom: none;
        }

        .post-content {
            font-size: 15px;
            color: #333;
            line-height: 1.6;
            margin-bottom: 12px;
            font-weight: 400;
        }

        .post-media {
            width: 100%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .post-media:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .post-video {
            position: relative;
        }

        .video-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            pointer-events: none;
        }

        .media-gallery {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 5px;
            margin-bottom: 10px;
        }

        .media-gallery.single {
            grid-template-columns: 1fr;
        }

        .media-gallery.triple {
            grid-template-columns: 2fr 1fr;
        }

        .media-gallery.triple .media-item:first-child {
            grid-row: span 2;
        }

        .media-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s;
        }

        .media-item:hover {
            transform: scale(1.02);
        }

        .media-item img,
        .media-item video {
            width: 100%;
            height: 140px;
            object-fit: cover;
        }

        .media-item.single img,
        .media-item.single video {
            height: 250px;
        }

        .media-count {
            position: absolute;
            bottom: 5px;
            right: 5px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: bold;
        }

        .post-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .post-time {
            color: #999;
        }

        .post-category {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
        }

        .post-stats {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .post-stat {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #666;
        }

        .post-stat i {
            font-size: 14px;
        }

        .post-stat.likes i {
            color: #e74c3c;
        }

        .post-stat.comments i {
            color: #3498db;
        }

        .post-stat.shares i {
            color: #2ecc71;
        }

        /* أزرار التفاعل للمنشورات */
        .post-actions {
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            border-top: 1px solid #f0f0f0;
            margin-top: 10px;
        }

        .post-action-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            border: none;
            background: transparent;
            cursor: pointer;
            border-radius: 20px;
            transition: all 0.3s;
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }

        .post-action-btn:hover {
            background: rgba(22, 204, 200, 0.1);
            color: #16CCC8;
            transform: scale(1.05);
        }

        .post-action-btn.liked {
            color: #16CCC8;
        }

        .post-action-btn.liked i {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .post-action-btn.commented {
            color: #3498db;
        }

        .post-action-btn.commented i {
            background: linear-gradient(135deg, #3498db, #2980b9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .post-action-btn.shared {
            color: #2ecc71;
        }

        .post-action-btn.shared i {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* تأثير النقر */
        .post-action-btn:active {
            transform: scale(0.95);
        }

        /* عداد التفاعلات */
        .action-count {
            font-size: 12px;
            font-weight: bold;
        }

        /* معرض الوسائط الجديد */
        .media-gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
            padding: 15px;
        }

        .media-gallery-item {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .media-gallery-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .media-image-container,
        .media-video-container {
            position: relative;
            width: 100%;
            height: 150px;
            overflow: hidden;
        }

        .media-image-container img,
        .media-video-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .media-gallery-item:hover img {
            transform: scale(1.05);
        }

        .video-play-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.7);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .media-gallery-item:hover .video-play-overlay {
            background: rgba(22, 204, 200, 0.9);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .media-type-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 6px;
            border-radius: 8px;
            font-size: 10px;
            backdrop-filter: blur(10px);
        }

        .media-info {
            padding: 8px;
            background: white;
        }

        .media-category {
            font-size: 11px;
            font-weight: bold;
            color: #16CCC8;
            margin-bottom: 2px;
        }

        .media-time {
            font-size: 10px;
            color: #666;
        }

        /* مودال البروفايل الشخصي */
        .my-profile-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f0f2f5;
            z-index: 5000;
            display: none;
            overflow-y: auto;
        }

        .my-profile-modal.show {
            display: block;
        }

        .my-profile-content {
            width: 100%;
            min-height: 100vh;
            background: #f0f2f5;
            overflow-y: auto;
        }

        /* رأس البروفايل */
        .my-profile-header {
            position: relative;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            text-align: center;
            border-radius: 0 0 30px 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            overflow: hidden;
            padding: 30px 20px;
            max-height: 400px;
        }

        /* صورة البروفايل في الأعلى */
        .profile-avatar-top-center {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
            padding: 10px;
        }

        .profile-avatar-extra-large {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            overflow: hidden;
            position: relative;
            cursor: pointer;
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            z-index: 10;
        }

        .profile-avatar-extra-large.online {
            border: 8px solid #4CAF50;
            box-shadow: 0 12px 35px rgba(0,0,0,0.4), 0 0 20px rgba(76, 175, 80, 0.5);
        }

        .profile-avatar-extra-large.offline {
            border: 8px solid #9E9E9E;
            box-shadow: 0 12px 35px rgba(0,0,0,0.4), 0 0 15px rgba(158, 158, 158, 0.3);
        }

        .profile-avatar-extra-large:hover {
            transform: scale(1.05);
        }

        .profile-avatar-extra-large:hover .avatar-edit-btn-large {
            opacity: 1;
            transform: scale(1.1);
            animation: pulse-camera 1.5s infinite;
        }

        @keyframes pulse-camera {
            0% { box-shadow: 0 6px 20px rgba(0,0,0,0.4); }
            50% { box-shadow: 0 8px 25px rgba(22, 204, 200, 0.6); }
            100% { box-shadow: 0 6px 20px rgba(0,0,0,0.4); }
        }

        .profile-avatar-extra-large img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-edit-btn-large {
            position: absolute;
            bottom: 15px;
            right: 15px;
            width: 45px;
            height: 45px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            border: 3px solid white;
            transition: all 0.3s ease;
            opacity: 1;
            z-index: 25;
            cursor: pointer;
            backdrop-filter: blur(10px);
        }

        .avatar-edit-btn-large:hover {
            transform: scale(1.1);
            background: rgba(0, 0, 0, 0.9);
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
        }

        .avatar-edit-btn-large:active {
            transform: scale(0.95);
        }

        /* معلومات البروفايل في الجزء الملون الكامل */
        .profile-info-colored-full {
            padding: 40px 30px;
            text-align: center;
            color: white;
            position: relative;
            z-index: 5;
        }

        .profile-name-white {
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin: 0 0 15px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* النجوم الصفراء في الجزء الملون */
        .profile-rating-stars-colored {
            margin: 15px 0;
        }

        .stars-container-white {
            display: inline-flex;
            gap: 15px;
        }

        .star-yellow-large {
            color: #FFD700;
            font-size: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }

        .star-yellow-large:hover {
            transform: scale(1.2);
            text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
        }

        .star-yellow-large.filled {
            color: #FFD700;
        }

        .star-yellow-large.empty {
            color: #E0E0E0;
        }

        .rating-text-white {
            color: white;
            font-size: 16px;
            font-weight: 500;
            margin-top: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        /* الإحصائيات في الجزء الملون */
        .profile-stats-colored {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            padding: 15px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-item-colored {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 90px;
        }

        .stat-number-white {
            font-size: 26px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .stat-label-white {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        /* العرض المتجاوب للبروفايل */
        @media (max-width: 768px) {
            .my-profile-header {
                padding: 30px 20px;
            }

            .profile-avatar-extra-large {
                width: 140px;
                height: 140px;
                border: 6px solid #4CAF50;
            }

            .profile-avatar-extra-large.offline {
                border: 6px solid #9E9E9E;
            }

            .profile-info-colored-full {
                padding: 30px 20px;
            }

            .profile-avatar-top-center {
                margin-bottom: 25px;
                padding: 12px;
            }

            .profile-name-white {
                font-size: 24px;
            }

            .star-yellow-large {
                font-size: 30px;
            }

            .stars-container-white {
                gap: 20px;
            }

            .rating-text-white {
                font-size: 14px;
            }

            .stat-number-white {
                font-size: 22px;
            }

            .stat-label-white {
                font-size: 14px;
            }

            .profile-stats-colored {
                padding: 20px 15px;
                margin-top: 20px;
            }

            .stat-item-colored {
                min-width: 70px;
            }

            .avatar-edit-btn-large {
                width: 40px;
                height: 40px;
                font-size: 16px;
                bottom: 12px;
                right: 12px;
                border: 2px solid white;
            }
        }

        /* أنماط معلومات الشركة المحدثة */
        .company-info-container {
            padding: 20px;
        }

        .company-section {
            background: white;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px 25px;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-header h3 {
            margin: 0;
            color: #333;
            font-size: 18px;
            font-weight: bold;
        }

        .section-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .privacy-select {
            padding: 8px 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            background: white;
            color: #666;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .privacy-select:focus {
            outline: none;
            border-color: #16CCC8;
        }

        .add-content-btn {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .add-content-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(22, 204, 200, 0.3);
        }

        .section-content {
            padding: 25px;
        }

        .content-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #16CCC8;
            transition: all 0.3s;
        }

        .content-item:hover {
            background: #f1f3f4;
            transform: translateX(5px);
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .content-date {
            color: #16CCC8;
            font-size: 12px;
            font-weight: 600;
        }

        .content-actions {
            display: flex;
            gap: 10px;
        }

        .content-action-btn {
            background: none;
            border: 1px solid #e1e8ed;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .content-action-btn:hover {
            background: #16CCC8;
            color: white;
            border-color: #16CCC8;
        }

        .content-text {
            color: #333;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .content-media {
            margin-top: 15px;
            border-radius: 8px;
            overflow: hidden;
        }

        .content-media img {
            width: 100%;
            max-height: 300px;
            object-fit: cover;
        }

        .content-media video {
            width: 100%;
            max-height: 300px;
        }

        .empty-section {
            text-align: center;
            color: #999;
            padding: 40px 20px;
            font-style: italic;
        }

        .basic-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            padding: 20px;
        }

        /* أنماط الألبومات الجديدة مثل الخدمات */
        .album-service-card {
            position: relative;
            cursor: pointer;
        }

        .add-album-service {
            border: 2px dashed #16CCC8;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .add-album-service:hover {
            border-color: #227FCC;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
        }

        .add-album-service:hover .add-album-icon-large {
            color: white;
        }

        .add-album-service:hover .add-album-info h3 {
            color: white;
        }

        .add-album-service.video-add {
            border-color: #16CCC8;
        }

        .add-album-service.video-add:hover {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            border-color: #227FCC;
        }

        .add-album-icon-large {
            font-size: 48px;
            color: #16CCC8;
            transition: all 0.3s;
        }

        .add-album-icon-large.video-icon {
            color: #16CCC8;
        }

        .add-album-info {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
        }

        .add-album-info.video-info {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
        }

        .add-album-info h3 {
            color: white;
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        /* تحسينات للتخطيط الأفقي */
        .services-grid .service-card h3 {
            font-size: 18px;
            font-weight: bold;
            color: white;
            margin: 0 0 5px 0;
        }

        .services-grid .album-service-card h3 {
            color: #333;
        }

        .album-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            display: flex;
            align-items: flex-end;
            justify-content: space-between;
            padding: 10px;
        }

        .album-count-badge {
            background: rgba(22, 204, 200, 0.9);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .album-count-badge.video-badge {
            background: rgba(22, 204, 200, 0.9);
        }

        /* أيقونة تشغيل الفيديو - نمط Facebook */
        .video-play-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;
        }

        .video-play-icon {
            width: 48px;
            height: 48px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            transition: all 0.2s;
        }

        .video-play-icon:hover {
            background: rgba(0, 0, 0, 0.8);
            transform: scale(1.1);
        }

        .album-controls {
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: center;
            margin-top: 5px;
        }

        .album-menu, .privacy-control {
            position: relative;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 12px;
            color: white;
        }

        .album-menu:hover, .privacy-control:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .album-dropdown, .privacy-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #dadde1;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            z-index: 1000;
            display: none;
            overflow: hidden;
        }

        .album-dropdown.show, .privacy-dropdown.show {
            display: block;
        }

        .album-dropdown-item, .privacy-dropdown-item {
            padding: 8px 16px;
            cursor: pointer;
            transition: background-color 0.1s;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 15px;
            color: #1c1e21;
            font-weight: 400;
        }

        .album-dropdown-item:hover, .privacy-dropdown-item:hover {
            background: #f2f3f5;
        }

        .privacy-dropdown-item i {
            width: 20px;
            text-align: center;
            color: #65676b;
        }

        /* تجاوب بسيط */
        @media (max-width: 1200px) {
            .simple-album-card {
                width: 220px;
                height: 260px;
            }

            .simple-album-image {
                height: 160px;
            }
        }

        @media (max-width: 768px) {
            .simple-albums-grid {
                gap: 15px;
                padding: 15px;
            }

            .simple-album-card {
                width: 200px;
                height: 240px;
            }

            .simple-album-image {
                height: 150px;
            }
        }

        @media (max-width: 480px) {
            .simple-albums-grid {
                gap: 10px;
                padding: 10px;
            }

            .simple-album-card {
                width: 160px;
                height: 200px;
            }

            .simple-album-image {
                height: 120px;
            }

            .simple-album-info {
                padding: 10px;
            }

            .simple-album-info h3 {
                font-size: 14px;
            }

            .add-icon {
                font-size: 32px;
            }
        }

        /* تحسينات أنماط الألبومات */
        .albums-grid .service-card h3 {
            font-size: 16px;
            font-weight: bold;
            color: white;
            margin: 0;
            text-align: center;
        }

        .albums-grid .add-album-service h3 {
            color: white;
        }

        .albums-grid .album-service-card h3 {
            color: white;
        }

        .profile-info-section {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            width: 100%;
        }

        .profile-avatar-large {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 5px solid rgba(255, 255, 255, 0.3);
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s;
            margin: 0 auto;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .profile-avatar-large:hover {
            transform: scale(1.05);
            border-color: rgba(255, 255, 255, 0.6);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .profile-avatar-large img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* الصورة الدائرية الكبيرة الجديدة */
        .profile-avatar-extra-large {
            width: 250px;
            height: 250px;
            border-radius: 50%;
            border: 8px solid #ffffff;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s;
            margin: 20px auto 30px;
            box-shadow: 0 12px 35px rgba(0,0,0,0.3);
            position: relative;
        }

        .profile-avatar-extra-large:hover {
            transform: scale(1.05);
            border-color: #227FCC;
            box-shadow: 0 12px 35px rgba(0,0,0,0.3);
        }

        .profile-avatar-extra-large img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .edit-avatar-btn {
            position: absolute;
            bottom: 10px;
            right: 10px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #16CCC8;
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .edit-avatar-btn:hover {
            background: #227FCC;
            transform: scale(1.1);
        }

        /* أنماط الألبومات والشهادات */
        .albums-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            padding: 20px;
        }

        .album-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border: 1px solid #e1e8ed;
        }

        .album-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .album-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .album-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .album-count {
            background: #16CCC8;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .album-preview {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            margin-bottom: 15px;
            border-radius: 10px;
            overflow: hidden;
        }

        .album-preview img {
            width: 100%;
            height: 80px;
            object-fit: cover;
        }

        .album-actions {
            display: flex;
            gap: 10px;
        }

        .album-btn {
            flex: 1;
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }

        .album-btn.primary {
            background: #16CCC8;
            color: white;
        }

        .album-btn.primary:hover {
            background: #227FCC;
        }

        .album-btn.secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e1e8ed;
        }

        .album-btn.secondary:hover {
            background: #e9ecef;
        }

        .add-album-btn {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .add-album-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(22, 204, 200, 0.3);
        }

        /* أنماط الشهادات */
        .certificates-container {
            padding: 20px;
        }

        .certificate-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #16CCC8;
        }

        .certificate-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .certificate-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .certificate-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }

        .certificate-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .certificate-date {
            color: #16CCC8;
            font-weight: 600;
            font-size: 14px;
        }

        .add-certificate-btn {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .add-certificate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(22, 204, 200, 0.3);
        }

        /* أنماط رأس الألبومات */
        .albums-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 0 5px;
        }

        .albums-header h3 {
            margin: 0;
            color: #333;
            font-size: 22px;
            font-weight: bold;
        }

        .view-all-btn {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .view-all-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(22, 204, 200, 0.3);
        }

        /* الألبومات البسيطة الأفقية */
        .simple-albums-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            padding: 20px;
            justify-content: flex-start;
        }

        /* الألبومات البسيطة */
        .simple-album-card {
            display: flex;
            flex-direction: column;
            width: 250px;
            height: 280px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            background: white;
            cursor: pointer;
        }

        .simple-album-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .simple-album-image {
            width: 100%;
            height: 180px;
            position: relative;
            overflow: hidden;
        }

        .simple-album-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .simple-album-card:hover .simple-album-image img {
            transform: scale(1.05);
        }

        .simple-album-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 15px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            position: relative;
        }

        .simple-album-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1), rgba(255,255,255,0.3));
        }

        .simple-album-info h3 {
            font-size: 16px;
            font-weight: bold;
            color: white;
            margin: 0 0 5px 0;
            text-align: center;
        }

        .simple-album-count {
            font-size: 13px;
            color: rgba(255,255,255,0.9);
            text-align: center;
            margin-bottom: 8px;
        }

        .simple-album-controls {
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: center;
        }

        /* مربع الإضافة */
        .simple-album-card.add-new {
            border: 2px dashed #16CCC8;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .simple-album-card.add-new:hover {
            border-color: #227FCC;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
        }

        .simple-album-card.add-new:hover .add-icon {
            color: white;
        }

        .simple-album-card.add-new:hover .simple-album-info h3 {
            color: white;
        }

        .simple-album-card.add-new .simple-album-image {
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }

        .add-icon {
            font-size: 48px;
            color: #16CCC8;
            transition: all 0.3s;
        }

        /* أنماط البروفايل المحسنة */
        .profile-name-extra-large {
            font-size: 32px !important;
            font-weight: bold;
            color: #1c1e21;
            margin: 0 0 15px 0;
            text-align: center;
        }

        .profile-bio-large {
            font-size: 18px !important;
            color: #65676b;
            text-align: center;
            margin: 0 0 20px 0;
            line-height: 1.5;
        }



        /* النجوم الكبيرة في الوسط */
        .profile-rating-center {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }

        .stars-extra-large {
            display: flex;
            gap: 8px;
        }

        .stars-extra-large .star-filled {
            color: #ffc107;
            font-size: 28px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stars-extra-large .star-half {
            color: #ffc107;
            font-size: 28px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .rating-text-extra-large {
            font-size: 24px;
            font-weight: bold;
            color: #16CCC8;
        }

        /* الإحصائيات الكبيرة */
        .profile-stats-extra-large {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin: 25px 0;
        }

        .stat-item-extra-large {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .stat-number-extra-large {
            font-size: 28px;
            font-weight: bold;
            color: #16CCC8;
        }

        .stat-label-extra-large {
            font-size: 16px;
            color: #65676b;
            font-weight: 500;
        }

        /* شريط المتابعين مثل Instagram */
        .instagram-followers-bar {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .instagram-followers-bar.expanded {
            max-height: none;
        }

        .followers-bar-header {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
        }

        .followers-filter-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 1px solid #e4e6ea;
        }

        .followers-filter-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }



        .filter-btn {
            background: #f0f2f5;
            color: #65676b;
            border: none;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .filter-btn:hover {
            background: #e4e6ea;
            transform: translateY(-1px);
        }

        .filter-btn.active {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            box-shadow: 0 2px 8px rgba(22, 204, 200, 0.3);
        }

        .filter-btn.active:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(22, 204, 200, 0.4);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .followers-bar-header {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .followers-filter-buttons {
                justify-content: center;
            }

            .filter-btn {
                flex: 1;
                min-width: 0;
                justify-content: center;
            }

            .expand-followers-btn {
                align-self: center;
            }

            .instagram-followers-scroll {
                gap: 8px;
                padding: 10px;
            }

            .instagram-follower-card {
                min-width: 75px;
                width: 75px;
            }

            .instagram-follower-avatar {
                width: 60px;
                height: 60px;
            }

            .instagram-follower-name {
                font-size: 11px;
                max-width: 75px;
            }


        }

        .followers-bar-header h3 {
            font-size: 18px;
            font-weight: bold;
            color: #1c1e21;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .expand-followers-btn {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s;
        }

        .expand-followers-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(22, 204, 200, 0.3);
        }

        .instagram-followers-scroll {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 15px;
            max-height: 120px;
            transition: all 0.3s ease;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .instagram-followers-scroll::-webkit-scrollbar {
            display: none; /* Chrome, Safari and Opera */
        }



        .instagram-followers-scroll.has-followers {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            max-height: 150px;
            overflow: hidden;
            justify-items: center;
            min-height: auto;
            padding: 10px;
        }

        .instagram-followers-scroll.expanded {
            max-height: 400px;
            overflow-y: auto;
            flex-wrap: wrap;
            justify-content: flex-start;
        }

        .instagram-follower-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 90px;
            width: 90px;
            padding: 8px;
            transition: all 0.3s;
            cursor: pointer;
            position: relative;
        }

        .instagram-follower-card:hover {
            transform: scale(1.1);
        }

        .instagram-follower-card:hover .instagram-follower-name {
            color: #16CCC8;
            font-weight: bold;
        }

        .instagram-follower-avatar {
            width: 75px;
            height: 75px;
            border-radius: 50%;
            overflow: hidden;
            margin-bottom: 8px;
            position: relative;
            padding: 3px;
            transition: all 0.3s;
        }

        .instagram-follower-avatar.online {
            background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39);
            box-shadow: 0 0 15px rgba(76, 175, 80, 0.4);
        }

        .instagram-follower-avatar.offline {
            background: linear-gradient(45deg, #9E9E9E, #757575, #616161);
            box-shadow: 0 0 10px rgba(158, 158, 158, 0.3);
        }

        .instagram-follower-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            background: white;
        }

        .instagram-follower-info {
            text-align: center;
            width: 100%;
        }

        .instagram-follower-name {
            font-size: 12px;
            font-weight: 500;
            color: #262626;
            text-align: center;
            line-height: 1.2;
            transition: all 0.3s;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 90px;
        }

        .follower-status {
            display: none; /* إخفاء حالة المتابعة في العرض العادي */
        }

        .mutual-status {
            background: linear-gradient(135deg, #e91e63, #f06292);
            color: white;
        }

        .following-status {
            background: linear-gradient(135deg, #2196f3, #64b5f6);
            color: white;
        }

        .follower-status {
            background: linear-gradient(135deg, #4caf50, #81c784);
            color: white;
        }

        /* تأثير النبضة للمتابعين الجدد */
        .instagram-follower-card.new-follower::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #16CCC8, #227FCC);
            border-radius: 15px;
            z-index: -1;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* شارات المتابعين */
        .mutual-badge, .following-badge, .follower-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .mutual-badge {
            background: linear-gradient(135deg, #e91e63, #f06292);
        }

        .following-badge {
            background: linear-gradient(135deg, #2196f3, #64b5f6);
        }

        .follower-badge {
            background: linear-gradient(135deg, #4caf50, #81c784);
        }

        /* قسم الصورة */
        .album-image-section {
            width: 100%;
            height: 180px;
            position: relative;
            overflow: hidden;
        }

        .album-image-section img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .album-card:hover .album-image-section img {
            transform: scale(1.05);
        }

        /* قسم المعلومات الملون */
        .album-info-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 20px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            position: relative;
        }

        .album-info-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1), rgba(255,255,255,0.3));
        }

        .album-info-section h3 {
            font-size: 16px;
            font-weight: bold;
            color: white;
            margin: 0 0 8px 0;
            text-align: center;
        }

        .album-count {
            font-size: 13px;
            color: rgba(255,255,255,0.9);
            text-align: center;
            margin-bottom: 10px;
        }

        /* مربع إضافة ألبوم جديد */
        .album-card-new {
            border: 2px dashed #16CCC8;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .album-card-new:hover {
            border-color: #227FCC;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
        }

        .album-card-new:hover .add-album-icon {
            color: white;
        }

        .album-card-new:hover .album-info-section h3 {
            color: white;
        }

        .album-card-new .album-image-section {
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }

        .add-album-icon {
            font-size: 48px;
            color: #16CCC8;
            transition: all 0.3s;
        }

        .albums-grid .service-card .service-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .albums-grid .service-card:hover .service-image img {
            transform: scale(1.05);
        }

        .albums-grid .service-card .service-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 15px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            position: relative;
        }

        .albums-grid .service-card .service-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1), rgba(255,255,255,0.3));
        }

        .albums-grid .service-card .service-info h3 {
            font-size: 16px;
            font-weight: bold;
            color: white;
            margin: 0 0 5px 0;
            text-align: center;
        }

        .albums-grid .service-card .album-count-info {
            font-size: 13px;
            color: rgba(255,255,255,0.9);
            text-align: center;
        }

        /* مربع إضافة ألبوم */
        .albums-grid .add-album-service {
            border: 2px dashed #16CCC8;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .albums-grid .add-album-service:hover {
            border-color: #227FCC;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
        }

        .albums-grid .add-album-service:hover .add-album-icon-large {
            color: white;
        }

        .albums-grid .add-album-service:hover .service-info h3 {
            color: white;
        }

        .albums-grid .add-album-service .service-image {
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }

        .albums-grid .add-album-service .add-album-icon-large {
            font-size: 48px;
            color: #16CCC8;
            transition: all 0.3s;
        }

        .albums-grid .add-album-service .service-info {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
        }

        /* مربع إضافة ألبوم جديد */
        .add-album-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px dashed #16CCC8;
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .add-album-card:hover {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            border-color: #227FCC;
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(22, 204, 200, 0.3);
        }

        .add-album-card:hover .add-album-icon,
        .add-album-card:hover .add-album-text {
            color: white;
        }

        .add-album-card.video-album {
            border-color: #e74c3c;
        }

        .add-album-card.video-album:hover {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border-color: #c0392b;
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
        }

        .add-album-icon {
            font-size: 48px;
            color: #16CCC8;
            margin-bottom: 15px;
            transition: all 0.3s;
        }

        .add-album-card.video-album .add-album-icon {
            color: #e74c3c;
        }

        .add-album-text {
            font-size: 16px;
            font-weight: 600;
            color: #666;
            transition: all 0.3s;
        }

        /* تحديث أنماط بطاقات الألبوم */
        .album-card {
            cursor: pointer;
            transition: all 0.3s;
        }

        .album-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.2);
        }

        .video-count {
            background: #e74c3c !important;
        }

        /* نافذة إنشاء الألبوم */
        .create-album-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }

        .create-album-modal.show {
            display: flex;
        }

        .create-album-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .create-album-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f3f4;
        }

        .create-album-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-create-album {
            background: none;
            border: none;
            font-size: 24px;
            color: #666;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s;
        }

        .close-create-album:hover {
            background: #f1f3f4;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #16CCC8;
            box-shadow: 0 0 0 3px rgba(22, 204, 200, 0.1);
        }

        .upload-area {
            border: 2px dashed #16CCC8;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s;
        }

        .upload-area:hover {
            background: #e9ecef;
            border-color: #227FCC;
        }

        .upload-icon {
            font-size: 48px;
            color: #16CCC8;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 14px;
            color: #999;
        }

        .create-album-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e1e8ed;
        }

        .btn-cancel {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e1e8ed;
            padding: 12px 25px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-cancel:hover {
            background: #e9ecef;
        }

        .btn-create {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-create:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(22, 204, 200, 0.3);
        }

        .btn-create.video {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .btn-create.video:hover {
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
        }

        /* معاينة الملفات المحددة */
        .selected-files-preview {
            margin-top: 15px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
        }

        .selected-file-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            background: #f8f9fa;
            border: 2px solid #e1e8ed;
        }

        .selected-file-item img {
            width: 100%;
            height: 80px;
            object-fit: cover;
        }

        .selected-file-name {
            padding: 8px;
            font-size: 12px;
            color: #666;
            text-align: center;
            background: white;
        }

        .remove-file-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(231, 76, 60, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .profile-main-info {
            text-align: center;
        }

        .profile-name-large {
            font-size: 28px;
            font-weight: bold;
            color: white;
            margin: 0 0 8px 0;
        }

        .profile-bio {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            margin: 0 0 15px 0;
            line-height: 1.5;
        }

        .profile-rating-large {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .profile-rating-large .stars {
            display: flex;
            gap: 2px;
        }

        .profile-rating-large .star {
            color: #ffd700;
            font-size: 16px;
        }

        .profile-rating-large .star.empty {
            color: rgba(255, 255, 255, 0.3);
        }

        .profile-rating-large .rating-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        .profile-stats-large {
            display: flex;
            gap: 30px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 18px;
            font-weight: bold;
            color: white;
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
        }

        .close-profile-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s;
            backdrop-filter: blur(10px);
        }

        .close-profile-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* شريط التنقل */
        .profile-nav {
            display: flex;
            background: white;
            border-bottom: 1px solid #e1e8ed;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .profile-nav-item {
            padding: 15px 25px;
            cursor: pointer;
            font-weight: 600;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 15px;
        }

        .profile-nav-item:hover {
            color: #16CCC8;
            background: rgba(22, 204, 200, 0.05);
        }

        .profile-nav-item.active {
            color: #16CCC8;
            border-bottom-color: #16CCC8;
            background: rgba(22, 204, 200, 0.1);
        }

        /* محتوى البروفايل */
        .profile-content-area {
            display: flex;
            gap: 20px;
            padding: 20px;
            background: #f0f2f5;
            min-height: calc(100vh - 200px);
            flex: 1;
        }

        .profile-sidebar {
            width: 350px;
            flex-shrink: 0;
        }

        .profile-main-content {
            flex: 1;
        }

        /* بطاقات الشريط الجانبي */
        .info-card, .photos-card, .followers-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .info-card h3, .photos-card h3, .followers-card h3 {
            margin: 0 0 15px 0;
            font-size: 18px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            color: #666;
        }

        .info-item i {
            width: 20px;
            color: #16CCC8;
        }

        /* شبكة الصور */
        .photos-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 15px;
        }

        .photo-item {
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .photo-item:hover {
            transform: scale(1.05);
        }

        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* شبكة المتابعين */
        .followers-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .follower-item {
            text-align: center;
            cursor: pointer;
            padding: 10px;
            border-radius: 8px;
            transition: background 0.3s;
        }

        .follower-item:hover {
            background: #f0f2f5;
        }

        .follower-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 8px;
            overflow: hidden;
        }

        .follower-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .follower-name {
            font-size: 12px;
            font-weight: 600;
            color: #333;
        }

        .see-all-btn {
            width: 100%;
            padding: 10px;
            background: #f0f2f5;
            border: none;
            border-radius: 8px;
            color: #16CCC8;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .see-all-btn:hover {
            background: rgba(22, 204, 200, 0.1);
        }

        /* منطقة إنشاء منشور */
        .create-post-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .create-post-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .create-post-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .create-post-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #16CCC8;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
        }

        .create-post-input:hover {
            border-color: #227FCC;
            box-shadow: 0 0 0 3px rgba(22, 204, 200, 0.1);
        }

        .create-post-actions {
            display: flex;
            justify-content: space-around;
            border-top: 1px solid #e1e8ed;
            padding-top: 15px;
        }

        .create-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            color: #666;
            transition: all 0.3s;
        }

        .create-action:hover {
            background: #f0f2f5;
        }

        .create-action i {
            font-size: 20px;
        }

        .create-action:nth-child(1) i { color: #45bd62; }
        .create-action:nth-child(2) i { color: #f7b928; }
        .create-action:nth-child(3) i { color: #f02849; }

        /* منطقة إنشاء منشور */
        .create-post-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .create-post-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .create-post-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .create-post-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e1e8ed;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            background: #f0f2f5;
            cursor: pointer;
            transition: all 0.3s;
        }

        .create-post-input:hover {
            background: #e4e6ea;
        }

        .create-post-actions {
            display: flex;
            justify-content: space-around;
            border-top: 1px solid #e1e8ed;
            padding-top: 15px;
        }

        .create-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            color: #666;
            transition: all 0.3s;
        }

        .create-action:hover {
            background: #f0f2f5;
        }

        .create-action i {
            font-size: 20px;
        }

        .create-action:nth-child(1) i { color: #45bd62; }
        .create-action:nth-child(2) i { color: #f7b928; }
        .create-action:nth-child(3) i { color: #f02849; }

        /* منشورات البروفايل */
        .my-posts-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .my-post-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .my-post-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .my-post-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .my-post-info {
            flex: 1;
        }

        .my-post-author {
            font-weight: bold;
            color: #333;
            margin-bottom: 2px;
        }

        .my-post-time {
            font-size: 12px;
            color: #666;
        }

        .my-post-content {
            font-size: 16px;
            line-height: 1.5;
            color: #333;
            margin-bottom: 15px;
        }

        .my-post-media {
            margin-bottom: 15px;
            border-radius: 8px;
            overflow: hidden;
        }

        .my-post-media img {
            width: 100%;
            height: auto;
            display: block;
        }

        .my-post-actions {
            display: flex;
            justify-content: space-around;
            border-top: 1px solid #e1e8ed;
            padding-top: 12px;
        }

        .my-post-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            color: #666;
            font-weight: 600;
            transition: all 0.3s;
        }

        .my-post-action:hover {
            background: #f0f2f5;
        }

        .my-post-action.liked {
            color: #16CCC8;
        }

        .my-post-action.commented {
            color: #3498db;
        }

        .my-post-action.shared {
            color: #2ecc71;
        }

        /* التصميم المتجاوب للبروفايل */
        @media (max-width: 768px) {
            .my-profile-content {
                margin: 0;
                border-radius: 0;
                height: 100vh;
            }

            .profile-cover {
                height: 200px;
            }

            .profile-avatar-large {
                width: 120px;
                height: 120px;
                top: -60px;
            }

            .profile-main-info {
                margin-left: 140px;
                padding-top: 10px;
            }

            .profile-name-large {
                font-size: 24px;
            }

            .profile-content-area {
                flex-direction: column;
                padding: 15px;
            }

            .profile-sidebar {
                width: 100%;
            }

            .profile-nav {
                overflow-x: auto;
            }

            .profile-nav-item {
                padding: 12px 16px;
                font-size: 14px;
            }
        }

        /* رسالة عدم وجود نتائج */
        .no-results {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .no-results i {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 15px;
        }

        /* مودال عرض الوسائط */
        .media-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 5000;
            backdrop-filter: blur(10px);
        }

        .media-modal.show {
            display: flex;
        }

        .media-modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
        }

        .media-modal-header {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .media-modal-title {
            font-size: 18px;
            font-weight: bold;
        }

        .media-modal-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .media-modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .media-modal-body {
            padding: 0;
            max-height: 70vh;
            overflow-y: auto;
        }

        .media-modal img,
        .media-modal video {
            width: 100%;
            height: auto;
            display: block;
        }

        .media-gallery-modal {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            padding: 20px;
        }

        .media-gallery-modal .media-item {
            border-radius: 10px;
            overflow: hidden;
        }

        .media-gallery-modal img,
        .media-gallery-modal video {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        /* تحسينات متجاوبة */
        @media (max-width: 1400px) {
            .user-profile-panel {
                width: 350px;
                height: calc(100vh - 60px);
            }
        }

        @media (max-width: 1200px) {
            .user-profile-panel {
                width: 320px;
                height: calc(100vh - 40px);
            }
        }

        @media (max-width: 768px) {
            .user-profile-panel {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                z-index: 4000;
            }

            .media-modal-content {
                max-width: 95%;
                max-height: 95%;
            }

            .media-gallery-modal {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                padding: 15px;
            }
        }





        .user-info-panel {
            width: 320px;
            background: white;
            border-radius: 20px;
            margin: 20px 0 20px 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border: 1px solid #e1e8ed;
            overflow: hidden;
            height: 550px;
            display: none;
            flex-direction: column;
        }

        .user-info-panel.show {
            display: flex;
        }

        /* مربعات التحديد للرسائل */
        .message-checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            flex-shrink: 0;
            margin-left: 10px;
            display: none;
        }

        .message-checkbox.selected {
            background: #16CCC8;
            border-color: #16CCC8;
        }

        .message-checkbox.selected::after {
            content: '✓';
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        .messages-selection-mode .message-checkbox {
            display: block;
        }

        /* الرسائل السريعة */
        .quick-replies-modal {
            position: fixed;
            bottom: 100px;
            right: 20px;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            padding: 15px;
            display: none;
            z-index: 3000;
            min-width: 250px;
        }

        .quick-replies-modal.show {
            display: block;
        }

        .quick-replies-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .quick-reply-item {
            padding: 10px 15px;
            background: #f8f9fa;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }

        .quick-reply-item:hover {
            background: #16CCC8;
            color: white;
            border-color: #16CCC8;
        }

        /* أزرار المرفقات */
        .attachment-options {
            position: fixed;
            bottom: 100px;
            right: 20px;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            padding: 10px;
            display: none;
            z-index: 3000;
        }

        .attachment-options.show {
            display: block;
        }

        .attachment-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            cursor: pointer;
            border-radius: 8px;
            transition: background 0.3s;
            font-size: 14px;
        }

        .attachment-option:hover {
            background: #f8f9fa;
        }

        .attachment-option i {
            width: 20px;
            text-align: center;
        }

        /* أزرار الدردشة المحسنة */
        .chat-input {
            display: flex;
            gap: 10px;
            align-items: center;
            background: #f8f9fa;
            border-radius: 25px;
            padding: 8px 15px;
            border: 1px solid #e1e8ed;
        }

        .chat-input:focus-within {
            border-color: #16CCC8;
            box-shadow: 0 0 0 2px rgba(22, 204, 200, 0.2);
        }

        .chat-attachment-btn, .chat-voice-btn, .chat-image-btn {
            background: none;
            border: none;
            color: #666;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            font-size: 16px;
        }

        .chat-attachment-btn:hover, .chat-voice-btn:hover, .chat-image-btn:hover {
            background: #e0e0e0;
            color: #333;
        }

        /* تحسين تصميم الدردشة */
        .chat-input {
            display: flex;
            gap: 8px;
            align-items: center;
            padding: 10px;
        }

        .chat-input-field {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #e1e8ed;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
            transition: all 0.3s;
            resize: none;
            min-height: 20px;
            max-height: 100px;
        }

        .chat-input-field:focus {
            border-color: #16CCC8;
            box-shadow: 0 0 0 2px rgba(22, 204, 200, 0.2);
        }

        /* تحسين الرسائل السريعة */
        .quick-replies-modal {
            position: fixed;
            bottom: 120px;
            right: 20px;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            padding: 15px;
            display: none;
            z-index: 3000;
            min-width: 320px;
            max-width: 400px;
            max-height: 400px;
            overflow-y: auto;
        }

        .quick-reply-item {
            padding: 10px 15px;
            background: #f8f9fa;
            border: 1px solid #e1e8ed;
            border-radius: 18px;
            margin-bottom: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;
            text-align: center;
            display: inline-block;
            margin-right: 8px;
            white-space: nowrap;
        }

        .quick-reply-item:hover {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-color: #16CCC8;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(22, 204, 200, 0.3);
        }

        .quick-replies-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
            border-bottom: 1px solid #e1e8ed;
            padding-bottom: 10px;
        }

        /* تحسين خيارات المرفقات */
        .attachment-options {
            position: fixed;
            bottom: 120px;
            right: 20px;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            padding: 12px;
            display: none;
            z-index: 3000;
            min-width: 200px;
        }

        .attachment-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 14px;
        }

        .attachment-option:hover {
            background: #f8f9fa;
            transform: translateX(5px);
        }



        /* نافذة المحادثة */
        .chat-modal {
            position: fixed;
            bottom: 80px;
            right: 420px;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            display: none;
            flex-direction: column;
            z-index: 2001;
            overflow: hidden;
        }

        .chat-modal.show {
            display: flex;
        }

        .chat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e8ed;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-radius: 20px 20px 0 0;
        }

        .chat-header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chat-header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .chat-action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            font-size: 16px;
        }

        .chat-action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .chat-back-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s;
        }

        .chat-back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .chat-user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
        }

        .chat-user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .chat-user-info {
            flex: 1;
        }

        .chat-user-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .chat-user-status {
            font-size: 12px;
            opacity: 0.8;
        }

        .chat-close-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s;
        }

        .chat-close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background: #f8f9fa;
        }

        .chat-message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }

        .chat-message.sent {
            flex-direction: row-reverse;
        }

        .chat-message-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            overflow: hidden;
            flex-shrink: 0;
        }

        .chat-message-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .chat-message-bubble {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 18px;
            position: relative;
        }

        .chat-message.received .chat-message-bubble {
            background: white;
            color: #333;
        }

        .chat-message.sent .chat-message-bubble {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
        }

        .chat-message-text {
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 4px;
        }

        .chat-message-time {
            font-size: 11px;
            opacity: 0.7;
        }

        .chat-input-container {
            padding: 15px 20px;
            border-top: 1px solid #e1e8ed;
            background: white;
            border-radius: 0 0 20px 20px;
        }

        .chat-input {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input-field {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #e1e8ed;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
            transition: all 0.3s;
        }

        .chat-input-field:focus {
            border-color: #16CCC8;
            box-shadow: 0 0 0 2px rgba(22, 204, 200, 0.2);
        }

        .chat-send-btn {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .chat-send-btn:hover {
            transform: scale(1.05);
        }

        /* إضافة تعليق جديد */
        .add-comment {
            display: flex;
            gap: 10px;
            align-items: flex-start;
            border-top: 1px solid #e1e8ed;
            padding-top: 15px;
        }

        .add-comment-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            flex-shrink: 0;
        }

        .add-comment-input {
            flex: 1;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 14px;
            outline: none;
            cursor: pointer;
            transition: all 0.3s;
        }

        .add-comment-input:hover {
            border-color: #16CCC8;
        }

        .add-comment-input:focus {
            border-color: #16CCC8;
            box-shadow: 0 0 0 2px rgba(22, 204, 200, 0.2);
        }

        /* نافذة المشاركة المتقدمة */
        .share-preview-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1001;
        }

        .share-preview-modal.show {
            display: flex;
        }

        .share-preview-content {
            background: white;
            border-radius: 15px;
            padding: 25px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .share-preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e1e8ed;
        }

        .share-preview-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .share-preview-close {
            background: none;
            border: none;
            font-size: 20px;
            color: #666;
            cursor: pointer;
            padding: 5px;
        }

        .share-preview-close:hover {
            color: #333;
        }

        .share-post-preview {
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
            background: #f8f9fa;
        }

        .share-post-author {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .share-post-content {
            margin-bottom: 15px;
            color: #333;
            line-height: 1.4;
        }

        .share-post-image {
            width: 100%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }

        .share-actions {
            display: flex;
            gap: 10px;
            justify-content: space-between;
        }

        .share-back-btn {
            padding: 10px 20px;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            background: white;
            color: #666;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .share-back-btn:hover {
            background: #f8f9fa;
        }

        .share-confirm-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            background: #16CCC8;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .share-confirm-btn:hover {
            background: #14b3af;
        }

        /* قسم التعليقات */
        .comments-section {
            margin-top: 15px;
            display: none;
        }

        .comments-section.show {
            display: block;
        }

        .comments-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 15px;
        }

        .comments-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .comments-toggle {
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 14px;
        }

        .comments-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .comment-item {
            display: flex;
            gap: 12px;
            margin-bottom: 15px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        .comment-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
        }

        .comment-content {
            flex: 1;
        }

        .comment-author {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 3px;
        }

        .comment-text {
            font-size: 14px;
            color: #333;
            line-height: 1.4;
            margin-bottom: 5px;
        }

        .comment-time {
            font-size: 12px;
            color: #666;
        }

        .facebook { color: #1877f2; }
        .instagram { color: #E4405F; }
        .tiktok { color: #000; }
        .whatsapp { color: #25D366; }
        .viber { color: #665CAC; }
        .profile { color: #16CCC8; }

        /* قائمة النقاط الثلاث */
        .post-menu-btn {
            background: none;
            border: none;
            color: #333;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .post-menu-btn:hover {
            color: #666;
        }

        .post-menu {
            position: absolute;
            top: 35px;
            left: 0;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            padding: 8px 0;
            display: none;
            z-index: 20;
            min-width: 280px;
        }

        .post-menu.show {
            display: block;
        }

        .menu-option {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 14px 18px;
            cursor: pointer;
            transition: background 0.3s;
            font-size: 16px;
            font-weight: 600;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
            color: #4a4a4a;
        }

        .menu-option:hover {
            background: #f8f9fa;
        }

        .menu-option i {
            width: 22px;
            text-align: center;
            font-size: 18px;
            color: #4a4a4a;
        }

        .menu-divider {
            height: 1px;
            background: #e1e8ed;
            margin: 8px 0;
        }

        /* نافذة التعليق المحسنة */
        .comment-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .comment-modal.show {
            display: flex;
        }

        .comment-modal-content {
            background: white;
            border-radius: 15px;
            padding: 25px;
            max-width: 400px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .comment-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e1e8ed;
        }

        .comment-modal-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .comment-close-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: #666;
            cursor: pointer;
            padding: 5px;
        }

        .comment-close-btn:hover {
            color: #333;
        }

        .comment-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .comment-option {
            padding: 12px;
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
            font-size: 14px;
        }

        .comment-option:hover {
            background: #f8f9fa;
            border-color: #16CCC8;
        }

        .comment-option.selected {
            background: #16CCC8;
            color: white;
            border-color: #16CCC8;
        }

        .comment-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .comment-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .comment-ok-btn {
            background: #16CCC8;
            color: white;
        }

        .comment-ok-btn:hover {
            background: #14b3af;
        }

        /* القصص */
        .stories-section {
            margin-bottom: 30px;
        }

        .stories-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .stories-container::-webkit-scrollbar {
            display: none;
        }

        .story-item {
            flex-shrink: 0;
            text-align: center;
            cursor: pointer;
        }

        .story-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .story-avatar.has-story {
            border: 3px solid #16CCC8;
        }

        .story-avatar.no-story {
            border: 3px solid #ccc;
        }

        .story-inner {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 20px;
        }

        .story-name {
            font-size: 12px;
            color: #333;
            max-width: 70px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* البث المباشر */
        .live-section {
            margin-bottom: 30px;
        }

        .live-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .live-container::-webkit-scrollbar {
            display: none;
        }

        .live-item {
            flex-shrink: 0;
            text-align: center;
            cursor: pointer;
        }

        .live-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 20px;
        }

        .live-avatar.is-live {
            border: 3px solid;
            border-image: linear-gradient(45deg, #ff0000, #ff8c00, #ffd700, #9932cc) 1;
            animation: live-pulse 2s ease-in-out infinite;
        }

        .live-avatar.no-live {
            border: 3px solid #ccc;
        }

        @keyframes live-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes premium-glow {
            0% {
                box-shadow:
                    0 0 30px rgba(255, 215, 0, 0.6),
                    0 0 60px rgba(255, 215, 0, 0.4),
                    0 8px 25px rgba(0,0,0,0.15),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
            100% {
                box-shadow:
                    0 0 40px rgba(255, 215, 0, 0.8),
                    0 0 80px rgba(255, 215, 0, 0.6),
                    0 12px 35px rgba(0,0,0,0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.4);
            }
        }

        @keyframes badge-glow {
            0% {
                box-shadow:
                    0 4px 15px rgba(255, 165, 0, 0.4),
                    0 0 20px rgba(255, 255, 0, 0.6),
                    0 0 40px rgba(255, 255, 0, 0.3);
            }
            100% {
                box-shadow:
                    0 4px 15px rgba(255, 165, 0, 0.6),
                    0 0 30px rgba(255, 255, 0, 0.8),
                    0 0 60px rgba(255, 255, 0, 0.5);
            }
        }

        .live-indicator {
            position: absolute;
            bottom: -2px;
            right: -2px;
            background: #ff0000;
            color: white;
            font-size: 8px;
            padding: 2px 4px;
            border-radius: 8px;
            font-weight: bold;
        }

        /* الإعلانات */
        .ads-section {
            margin-bottom: 30px;
        }

        .ads-container {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            border-radius: 15px;
            padding: 20px;
            color: white;
            text-align: center;
            cursor: pointer;
        }

        .ad-text {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .ad-subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        /* المنشورات المميزة */
        .featured-posts {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .featured-content {
            text-align: center;
            color: #666;
            padding: 40px 20px;
        }

        .featured-content i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <div class="header">
        <div class="user-section">
            <div class="menu-icon">
                <i class="fas fa-bars"></i>
            </div>
            <div class="user-avatar" onclick="openMyProfile()" style="cursor: pointer;">
                <img src="images/Hussein Nihad.png" alt="Hussein Nihad">
            </div>
            <div class="user-name" onclick="openMyProfile()" style="cursor: pointer;">Hussein Nihad</div>
        </div>

        <div class="search-section">
            <input type="text" class="search-input" placeholder="اختر نوع الخدمة">
            <button class="search-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>

        <div class="app-name">Get Me</div>
    </div>

    <!-- شريط التنقل -->
    <div class="nav-bar">
        <div class="nav-items">
            <div class="nav-item home-item active" onclick="showFeed()">
                <i class="fas fa-home"></i>
                <span>الرئيسية</span>
            </div>
            <div class="nav-item has-notification" onclick="showNotifications()">
                <i class="fas fa-bell"></i>
                <span>الإشعارات</span>
                <div class="notification-badge">5</div>
            </div>
            <div class="nav-item has-notification" onclick="showMessages()">
                <i class="fas fa-envelope"></i>
                <span>الرسائل</span>
                <div class="notification-badge">3</div>
            </div>
            <div class="nav-item" onclick="showMyProfile()">
                <i class="fas fa-user"></i>
                <span>الملف الشخصي</span>
            </div>

        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط الخدمات -->
        <div class="services-section">
            <div class="services-container">
                <button class="nav-arrow prev" id="prevBtn">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button class="nav-arrow next" id="nextBtn">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="services-wrapper" id="servicesWrapper">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- شريط العرض المميز -->
        <div class="premium-offers-section">
            <div class="premium-offers-container">
                <button class="premium-nav-arrow prev" id="premiumPrevBtn">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button class="premium-nav-arrow next" id="premiumNextBtn">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="premium-offers-wrapper" id="premiumOffersWrapper">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <div class="content-area">
            <!-- الشريط الجانبي الأيسر - المنشورات المميزة -->
            <div class="left-sidebar">
                <div class="trending-posts-section" id="trendingPostsSection">
                    <div class="section-title">
                        <i class="fas fa-fire"></i>
                        المنشورات المميزة
                    </div>
                    <div class="trending-posts-container" id="trendingPostsContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي الأيمن - وظفني -->
            <div class="right-sidebar">
                <div class="video-ads-section" id="videoAdsSection">
                    <div class="section-title">
                        <i class="fas fa-briefcase"></i>
                        وظفني - شاهد واربح
                    </div>
                    <div class="video-ads-container" id="videoAdsContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                    <div class="earnings-info">
                        <div class="earnings-title">
                            <i class="fas fa-coins"></i>
                            أرباحك اليوم
                        </div>
                        <div class="earnings-amount" id="totalEarnings">$0.00</div>
                        <div class="earnings-description">
                            شاهد الإعلانات واحصل على أموال حقيقية!<br>
                            كل مشاهدة كاملة = مكافأة فورية
                        </div>
                        <div class="earnings-stats">
                            <div class="earnings-stat">
                                <div class="earnings-stat-number" id="videosWatched">0</div>
                                <div class="earnings-stat-label">فيديو</div>
                            </div>
                            <div class="earnings-stat">
                                <div class="earnings-stat-number" id="totalViews">0</div>
                                <div class="earnings-stat-label">مشاهدة</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة التعليق -->
    <div class="comment-modal" id="commentModal">
        <div class="comment-modal-content">
            <div class="comment-modal-header">
                <div class="comment-modal-title">اختر تعليقك</div>
                <button class="comment-close-btn" onclick="closeCommentModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="comment-options" id="commentOptions">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="comment-actions">
                <button class="comment-btn comment-ok-btn" onclick="submitComment()">موافق</button>
            </div>
        </div>
    </div>

    <!-- نافذة المشاركة -->
    <div class="share-modal" id="shareModal">
        <div class="share-modal-content">
            <div class="share-modal-header">
                <div class="share-modal-title">مشاركة المنشور</div>
                <button class="share-close-btn" onclick="closeShareModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="share-options">
                <div class="share-option" onclick="selectShareOption('facebook')">
                    <i class="fab fa-facebook" style="color: #1877f2;"></i>
                    <span>فيسبوك</span>
                </div>
                <div class="share-option" onclick="selectShareOption('instagram')">
                    <i class="fab fa-instagram" style="color: #E4405F;"></i>
                    <span>انستجرام</span>
                </div>
                <div class="share-option" onclick="selectShareOption('tiktok')">
                    <i class="fab fa-tiktok" style="color: #000;"></i>
                    <span>تيك توك</span>
                </div>
                <div class="share-option" onclick="selectShareOption('whatsapp')">
                    <i class="fab fa-whatsapp" style="color: #25D366;"></i>
                    <span>واتساب</span>
                </div>
                <div class="share-option" onclick="selectShareOption('viber')">
                    <i class="fab fa-viber" style="color: #665CAC;"></i>
                    <span>فايبر</span>
                </div>
                <div class="share-option" onclick="selectShareOption('profile')">
                    <i class="fas fa-user" style="color: #16CCC8;"></i>
                    <span>صفحتي</span>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد المشاركة -->
    <div class="share-confirm" id="shareConfirm">
        <div style="text-align: center; margin-bottom: 15px;">
            <div style="font-weight: bold; margin-bottom: 5px;">تأكيد المشاركة</div>
            <div style="color: #666; font-size: 14px;" id="shareConfirmText">هل تريد مشاركة هذا المنشور؟</div>
        </div>
        <div class="share-confirm-actions">
            <button class="share-confirm-btn share-cancel-btn" onclick="cancelShare()">إلغاء</button>
            <button class="share-confirm-btn share-submit-btn" onclick="confirmShare()">مشاركة</button>
        </div>
    </div>

    <!-- نافذة معاينة المشاركة -->
    <div class="share-preview-modal" id="sharePreviewModal">
        <div class="share-preview-content">
            <div class="share-preview-header">
                <div class="share-preview-title" id="sharePreviewTitle">
                    <i class="fab fa-instagram"></i>
                    <span>مشاركة في انستجرام</span>
                </div>
                <button class="share-preview-close" onclick="closeSharePreview()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="share-post-preview" id="sharePostPreview">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="share-actions">
                <button class="share-back-btn" onclick="backToShareOptions()">رجوع</button>
                <button class="share-confirm-btn" onclick="confirmFinalShare()">مشاركة</button>
            </div>
        </div>
    </div>

    <!-- نافذة الإشعارات -->
    <div class="notifications-modal" id="notificationsModal">
        <div class="notifications-content">
            <div class="notifications-header">
                <div class="notifications-title">الإشعارات</div>
                <button class="notifications-close" onclick="closeNotifications()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notifications-tabs">
                <div class="tabs-left">
                    <div class="tab-item active" onclick="switchTab('all')">الكل</div>
                    <div class="tab-item" onclick="switchTab('unread')">غير مقروءة</div>
                </div>
                <div class="notifications-menu">
                    <button class="notifications-menu-btn" onclick="toggleNotificationsMenu()">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="notifications-dropdown" id="notificationsDropdown">
                        <div class="notifications-dropdown-item" onclick="markAllAsRead()">
                            <i class="fas fa-check-double"></i>
                            تعيين الكل كمقروء
                        </div>
                        <div class="notifications-dropdown-item" onclick="openNotificationSettings()">
                            <i class="fas fa-cog"></i>
                            إعدادات الإشعارات
                        </div>
                        <div class="notifications-dropdown-item" onclick="openNotificationsWindow()">
                            <i class="fas fa-external-link-alt"></i>
                            فتح نافذة الإشعارات
                        </div>
                    </div>
                </div>
            </div>
            <div class="notifications-list" id="notificationsList">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="notifications-footer">
                <button class="delete-selected-btn" id="deleteSelectedBtn" onclick="deleteSelectedNotifications()">
                    مسح المحدد
                </button>
                <button class="show-old-notifications-btn" onclick="showOldNotifications()">
                    إظهار الإشعارات القديمة
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة قائمة الرسائل -->
    <div class="messages-modal" id="messagesModal">
        <div class="messages-header">
            <div class="messages-title">الرسائل</div>
            <div class="messages-controls">
                <div class="messages-menu">
                    <button class="messages-btn" onclick="toggleMessagesMenu()">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="messages-dropdown" id="messagesDropdown">
                        <div class="messages-dropdown-item" onclick="toggleMessageSounds()">
                            <i class="fas fa-volume-up"></i>
                            أصوات الرسائل
                        </div>
                        <div class="messages-dropdown-item" onclick="toggleCallSounds()">
                            <i class="fas fa-phone"></i>
                            أصوات المكالمات
                        </div>
                        <div class="messages-dropdown-item" onclick="toggleActiveStatus()">
                            <i class="fas fa-circle"></i>
                            حالة النشاط
                        </div>
                        <div class="messages-dropdown-item" onclick="showRestrictedAccounts()">
                            <i class="fas fa-user-slash"></i>
                            الحسابات المقيدة
                        </div>
                        <div class="messages-dropdown-item" onclick="showBlockedAccounts()">
                            <i class="fas fa-ban"></i>
                            الحسابات المحظورة
                        </div>
                        <div class="messages-dropdown-item" onclick="showPrivacySettings()">
                            <i class="fas fa-shield-alt"></i>
                            خصوصية الرسائل
                        </div>
                    </div>
                </div>
                <button class="messages-btn" onclick="expandMessages()">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="messages-btn" onclick="closeMessages()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="messages-search">
            <div class="messages-search-container">
                <input type="text" class="messages-search-input" placeholder="البحث في الرسائل..." onkeyup="searchMessages(this.value)" id="messagesSearchInput">
                <i class="fas fa-search messages-search-icon" onclick="performSearch()"></i>
            </div>
        </div>
        <div class="messages-tabs">
            <div class="messages-tab active" onclick="switchMessagesTab('all')">الكل</div>
            <div class="messages-tab" onclick="switchMessagesTab('unread')">غير مقروءة</div>
        </div>
        <div class="messages-list" id="messagesList">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>
        <div class="messages-footer">
            <div class="messages-actions">
                <button class="delete-selected-messages-btn" id="deleteSelectedMessagesBtn" onclick="deleteSelectedMessages()">
                    مسح المحدد
                </button>
            </div>
            <button class="show-old-messages-btn" onclick="showOldMessages()">
                إظهار الرسائل السابقة
            </button>
        </div>
    </div>

    <!-- نافذة المحادثة -->
    <div class="chat-modal" id="chatModal">
        <div class="chat-header">
            <button class="chat-back-btn" onclick="backToMessages()">
                <i class="fas fa-arrow-right"></i>
            </button>
            <div class="chat-user-avatar">
                <img src="" alt="" id="chatUserAvatar">
            </div>
            <div class="chat-user-info">
                <div class="chat-user-name" id="chatUserName" onclick="openUserProfileById(currentChatId)" style="cursor: pointer;">اسم المستخدم</div>
                <div class="chat-user-status" id="chatUserStatus">متصل الآن</div>
            </div>
            <button class="chat-close-btn" onclick="closeChat()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="chat-messages" id="chatMessages">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>
        <div class="chat-input-container">
            <div class="chat-input">
                <input type="text" class="chat-input-field" id="chatInputField" placeholder="اكتب رسالة..." onkeypress="handleChatKeyPress(event)">
                <button class="chat-send-btn" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- النافذة المكبرة للرسائل -->
    <div class="messages-fullscreen" id="messagesFullscreen">
        <div class="messages-fullscreen-header">
            <div class="messages-fullscreen-title">Get Me - الرسائل</div>
            <div class="messages-fullscreen-search">
                <input type="text" placeholder="البحث في الرسائل..." onkeyup="searchMessagesFullscreen(this.value)" id="messagesSearchInputFullscreen">
                <i class="fas fa-search messages-fullscreen-search-icon" onclick="performSearchFullscreen()"></i>
            </div>
            <div class="messages-fullscreen-controls">
                <div class="messages-fullscreen-menu">
                    <button class="messages-btn" onclick="toggleFullscreenMessagesMenu()" style="color: white;">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="messages-fullscreen-dropdown" id="messagesFullscreenDropdown">
                        <div class="messages-fullscreen-dropdown-item" onclick="toggleSelectionMode()">
                            <i class="fas fa-check-square"></i>
                            تحديد الرسائل
                        </div>
                        <div class="messages-fullscreen-dropdown-item" onclick="deleteAllMessages()">
                            <i class="fas fa-trash"></i>
                            مسح جميع الرسائل
                        </div>
                        <div class="messages-fullscreen-dropdown-item" onclick="showSellers()">
                            <i class="fas fa-store"></i>
                            البائعين
                        </div>
                        <div class="messages-fullscreen-dropdown-item" onclick="toggleMessageSounds()">
                            <i class="fas fa-volume-up"></i>
                            أصوات الرسائل
                        </div>
                        <div class="messages-fullscreen-dropdown-item" onclick="showPrivacySettings()">
                            <i class="fas fa-shield-alt"></i>
                            إعدادات الخصوصية
                        </div>
                    </div>
                </div>
                <button class="messages-btn" onclick="closeFullscreenMessages()" style="color: white;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="messages-fullscreen-content">
            <div class="messages-sidebar">
                <div class="messages-tabs">
                    <div class="messages-tab active" onclick="switchMessagesTabFullscreen('all')">الكل</div>
                    <div class="messages-tab" onclick="switchMessagesTabFullscreen('unread')">غير مقروءة</div>
                </div>
                <div class="messages-list" id="messagesListFullscreen">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
                <div class="messages-footer">
                    <button class="show-old-messages-btn" onclick="showOldMessagesFullscreen()">
                        إظهار الرسائل السابقة
                    </button>
                </div>
            </div>
            <div class="messages-chat-area" id="messagesChatArea">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666; font-size: 16px;">
                    اختر محادثة لبدء المراسلة
                </div>
            </div>
            <div class="user-profile-panel" id="userProfilePanel">
                <div class="profile-content">
                    <!-- رأس البروفايل -->
                    <div class="profile-header">
                        <div class="profile-avatar" id="profileAvatar">
                            <img src="images/Hussein Nihad.png" alt="صورة المستخدم">
                        </div>
                        <div class="profile-name" id="profileName">اختر محادثة لعرض البروفايل</div>
                        <div class="profile-status" id="profileStatus">غير متصل</div>

                        <!-- تقييم النجوم -->
                        <div class="profile-rating">
                            <div class="stars" id="profileStars">
                                <i class="fas fa-star star empty"></i>
                                <i class="fas fa-star star empty"></i>
                                <i class="fas fa-star star empty"></i>
                                <i class="fas fa-star star empty"></i>
                                <i class="fas fa-star star empty"></i>
                            </div>
                            <span class="rating-text" id="ratingText">(0.0)</span>
                        </div>

                        <!-- زر المتابعة -->
                        <button class="follow-btn" id="followBtn" onclick="toggleFollow()">
                            <i class="fas fa-user-plus"></i> متابعة
                        </button>

                        <div class="profile-stats">
                            <div class="profile-stat">
                                <span class="profile-stat-number" id="profileFollowers">0</span>
                                <span class="profile-stat-label">متابع</span>
                            </div>
                            <div class="profile-stat">
                                <span class="profile-stat-number" id="profileFollowing">0</span>
                                <span class="profile-stat-label">يتابع</span>
                            </div>
                            <div class="profile-stat">
                                <span class="profile-stat-number" id="profilePosts">0</span>
                                <span class="profile-stat-label">منشور</span>
                            </div>
                        </div>
                    </div>

                    <!-- شريط البحث -->
                    <div class="posts-search">
                        <div class="search-container">
                            <input type="text" class="search-input" id="postsSearchInput" placeholder="ابحث في المنشورات..." onkeyup="searchUserPosts(this.value)">
                            <i class="fas fa-search search-icon" onclick="performPostsSearch()"></i>
                        </div>
                    </div>

                    <!-- تبويبات المحتوى -->
                    <div class="content-tabs">
                        <div class="content-tab active" onclick="switchTab('posts')">
                            <i class="fas fa-newspaper"></i> المنشورات
                        </div>
                        <div class="content-tab" onclick="switchTab('images')">
                            <i class="fas fa-images"></i> الصور
                        </div>
                        <div class="content-tab" onclick="switchTab('videos')">
                            <i class="fas fa-video"></i> الفيديوهات
                        </div>
                    </div>

                    <!-- قسم المنشورات -->
                    <div class="posts-section">
                        <div class="posts-header" id="postsHeader">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-newspaper"></i>
                                <span id="sectionTitle">المنشورات</span>
                            </div>
                            <div class="posts-count" id="postsCount">0</div>
                        </div>
                        <div id="userPostsList">
                            <div class="no-results">
                                <i class="fas fa-comments"></i>
                                <div>اختر محادثة لعرض منشورات المستخدم</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- مودال عرض الوسائط -->
    <div class="media-modal" id="mediaModal">
        <div class="media-modal-content">
            <div class="media-modal-header">
                <div class="media-modal-title" id="mediaModalTitle">عرض الوسائط</div>
                <button class="media-modal-close" onclick="closeMediaModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="media-modal-body" id="mediaModalBody">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>

    <!-- نافذة إنشاء ألبوم صور -->
    <div id="createPhotoAlbumModal" class="create-album-modal">
        <div class="create-album-content">
            <div class="create-album-header">
                <h3 class="create-album-title">
                    <i class="fas fa-images"></i> إنشاء ألبوم صور جديد
                </h3>
                <button class="close-create-album" onclick="closeCreatePhotoAlbumModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="form-group">
                <label class="form-label">اسم الألبوم</label>
                <input type="text" class="form-input" id="photoAlbumName" placeholder="أدخل اسم الألبوم...">
            </div>

            <div class="form-group">
                <label class="form-label">رفع صور</label>
                <div class="upload-area" onclick="selectPhotosForAlbum()">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="upload-text">اضغط لاختيار الصور</div>
                    <div class="upload-hint">يمكنك اختيار عدة صور معاً</div>
                </div>
                <input type="file" id="photoAlbumFiles" multiple accept="image/*" style="display: none;">
                <div id="selectedPhotosPreview" class="selected-files-preview"></div>
            </div>

            <div class="create-album-actions">
                <button class="btn-cancel" onclick="closeCreatePhotoAlbumModal()">إلغاء</button>
                <button class="btn-create" onclick="createPhotoAlbum()">إنشاء الألبوم</button>
            </div>
        </div>
    </div>

    <!-- نافذة إنشاء ألبوم فيديوهات -->
    <div id="createVideoAlbumModal" class="create-album-modal">
        <div class="create-album-content">
            <div class="create-album-header">
                <h3 class="create-album-title">
                    <i class="fas fa-video"></i> إنشاء ألبوم فيديوهات جديد
                </h3>
                <button class="close-create-album" onclick="closeCreateVideoAlbumModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="form-group">
                <label class="form-label">اسم الألبوم</label>
                <input type="text" class="form-input" id="videoAlbumName" placeholder="أدخل اسم ألبوم الفيديوهات...">
            </div>

            <div class="form-group">
                <label class="form-label">رفع فيديوهات</label>
                <div class="upload-area" onclick="selectVideosForAlbum()">
                    <div class="upload-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <div class="upload-text">اضغط لاختيار الفيديوهات</div>
                    <div class="upload-hint">يمكنك اختيار عدة فيديوهات معاً</div>
                </div>
                <input type="file" id="videoAlbumFiles" multiple accept="video/*" style="display: none;">
                <div id="selectedVideosPreview" class="selected-files-preview"></div>
            </div>

            <div class="create-album-actions">
                <button class="btn-cancel" onclick="closeCreateVideoAlbumModal()">إلغاء</button>
                <button class="btn-create video" onclick="createVideoAlbum()">إنشاء الألبوم</button>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة محتوى لمعلومات الشركة -->
    <div id="addContentModal" class="create-album-modal">
        <div class="create-album-content">
            <div class="create-album-header">
                <h3 class="create-album-title" id="addContentTitle">
                    <i class="fas fa-plus"></i> إضافة محتوى جديد
                </h3>
                <button class="close-create-album" onclick="closeAddContentModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="form-group">
                <label class="form-label">نوع المحتوى</label>
                <select class="form-input" id="contentType" onchange="toggleMediaUpload()">
                    <option value="text">نص فقط</option>
                    <option value="media">نص مع صورة/فيديو</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label">المحتوى</label>
                <textarea class="form-input" id="contentText" placeholder="اكتب المحتوى هنا..." rows="4"></textarea>
            </div>

            <div class="form-group" id="mediaUploadGroup" style="display: none;">
                <label class="form-label">رفع ملف</label>
                <div class="upload-area" onclick="selectContentMedia()">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="upload-text">اضغط لاختيار صورة أو فيديو</div>
                    <div class="upload-hint">يمكنك رفع صورة أو فيديو واحد</div>
                </div>
                <input type="file" id="contentMediaFile" accept="image/*,video/*" style="display: none;">
                <div id="selectedContentPreview" class="selected-files-preview"></div>
            </div>

            <div class="create-album-actions">
                <button class="btn-cancel" onclick="closeAddContentModal()">إلغاء</button>
                <button class="btn-create" onclick="addContentToSection()">إضافة المحتوى</button>
            </div>
        </div>
    </div>

    <!-- مودال البروفايل الشخصي -->
    <div class="my-profile-modal" id="myProfileModal">
        <div class="my-profile-content">
            <!-- رأس البروفايل -->
            <div class="my-profile-header">
                <!-- معلومات البروفايل في الجزء الملون -->
                <div class="profile-info-colored-full">
                    <!-- صورة البروفايل في الأعلى -->
                    <div class="profile-avatar-top-center">
                        <div class="profile-avatar-extra-large online">
                            <img src="images/Hussein Nihad.png" alt="صورة الملف الشخصي" id="myProfileAvatar">
                            <!-- أيقونة الكاميرا على إطار الصورة مثل الفيسبوك -->
                            <div class="avatar-edit-btn-large" onclick="editAvatar()">
                                <i class="fas fa-camera"></i>
                            </div>
                        </div>
                    </div>

                    <h1 class="profile-name-white" id="myProfileName">حسين نهاد</h1>

                    <!-- النجوم الصفراء - تقييم المستخدم -->
                    <div class="profile-rating-stars-colored" id="myProfileRating">
                        <div class="stars-container-white">
                            <i class="fas fa-star star-yellow-large filled" onclick="setRating(1)"></i>
                            <i class="fas fa-star star-yellow-large filled" onclick="setRating(2)"></i>
                            <i class="fas fa-star star-yellow-large filled" onclick="setRating(3)"></i>
                            <i class="fas fa-star star-yellow-large filled" onclick="setRating(4)"></i>
                            <i class="fas fa-star-half-alt star-yellow-large filled" onclick="setRating(5)"></i>
                        </div>
                        <div class="rating-text-white">(4.5 من 5)</div>
                    </div>

                    <!-- الإحصائيات المتباعدة -->
                    <div class="profile-stats-colored">
                        <div class="stat-item-colored">
                            <span class="stat-number-white" id="myFollowers">1.3K</span>
                            <span class="stat-label-white">متابع</span>
                        </div>
                        <div class="stat-item-colored">
                            <span class="stat-number-white" id="myFollowing">365</span>
                            <span class="stat-label-white">يتابع</span>
                        </div>
                        <div class="stat-item-colored">
                            <span class="stat-number-white" id="myPosts">18</span>
                            <span class="stat-label-white">منشور</span>
                        </div>
                    </div>
                </div>
                    <button class="close-profile-btn" onclick="closeMyProfile()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- شريط التنقل -->
            <div class="profile-nav">
                <div class="profile-nav-item active" onclick="switchProfileTab('posts')">
                    <i class="fas fa-newspaper"></i> المنشورات
                </div>
                <div class="profile-nav-item" onclick="switchProfileTab('info')">
                    <i class="fas fa-info-circle"></i> معلومات الشركة
                </div>
                <div class="profile-nav-item" onclick="switchProfileTab('certificates')">
                    <i class="fas fa-certificate"></i> الشهادات
                </div>
                <div class="profile-nav-item" onclick="switchProfileTab('photo-albums')">
                    <i class="fas fa-images"></i> ألبومات الصور
                </div>
                <div class="profile-nav-item" onclick="switchProfileTab('video-albums')">
                    <i class="fas fa-video"></i> ألبومات الفيديو
                </div>
                <div class="profile-nav-item" onclick="switchProfileTab('followers')">
                    <i class="fas fa-users"></i> المتابعين
                </div>
            </div>

            <!-- شريط المتابعين مثل Instagram -->
            <div class="instagram-followers-bar" id="followersBar">
                <div class="followers-bar-header">
                    <button class="expand-followers-btn" onclick="toggleFollowersExpansion()">
                        <i class="fas fa-expand-alt" id="expandIcon"></i>
                    </button>
                </div>

                <!-- أزرار الفلترة في العرض الموسع -->
                <div class="followers-filter-section" id="followersFilterSection" style="display: none;">
                    <div class="followers-filter-buttons">
                        <button class="filter-btn active" onclick="filterFollowers('all')" id="allBtn">
                            <i class="fas fa-users"></i> الكل
                        </button>
                        <button class="filter-btn" onclick="filterFollowers('followers')" id="followersBtn">
                            <i class="fas fa-user-friends"></i> المتابعين
                        </button>
                        <button class="filter-btn" onclick="filterFollowers('following')" id="followingBtn">
                            <i class="fas fa-user-plus"></i> أتابعهم
                        </button>
                    </div>
                </div>

                <div class="instagram-followers-scroll" id="followersBarContent">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- محتوى البروفايل -->
            <div class="profile-content-area">
                <div class="profile-sidebar">
                    <!-- معلومات شخصية -->
                    <div class="info-card">
                        <h3><i class="fas fa-info-circle"></i> معلومات شخصية</h3>
                        <div class="info-item">
                            <i class="fas fa-briefcase"></i>
                            <span>يعمل في <strong>تطوير التطبيقات</strong></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-graduation-cap"></i>
                            <span>درس في <strong>جامعة التكنولوجيا</strong></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>يعيش في <strong>بغداد، العراق</strong></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-heart"></i>
                            <span><strong>أعزب</strong></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-calendar-alt"></i>
                            <span>انضم في <strong>يناير 2020</strong></span>
                        </div>
                    </div>

                    <!-- الصور -->
                    <div class="photos-card">
                        <h3><i class="fas fa-images"></i> الصور</h3>
                        <div class="photos-grid" id="myPhotosGrid">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                        <button class="see-all-btn" onclick="switchProfileTab('photos')">عرض جميع الصور</button>
                    </div>

                    <!-- المتابعين -->
                    <div class="followers-card">
                        <h3><i class="fas fa-users"></i> المتابعين</h3>
                        <div class="followers-grid" id="myFollowersGrid">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                        <button class="see-all-btn" onclick="switchProfileTab('followers')">عرض جميع المتابعين</button>
                    </div>
                </div>

                <div class="profile-main-content">
                    <!-- منطقة إنشاء منشور -->
                    <div class="create-post-card">
                        <div class="create-post-header">
                            <img src="images/Hussein Nihad.png" alt="صورتك" class="create-post-avatar">
                            <input type="text" placeholder="منشور جديد..." class="create-post-input" onclick="openCreatePost()">
                        </div>
                        <div class="create-post-actions">
                            <button class="create-action" onclick="addPhoto()">
                                <i class="fas fa-images"></i> صورة/فيديو
                            </button>
                            <button class="create-action" onclick="addFeeling()">
                                <i class="fas fa-smile"></i> شعور/نشاط
                            </button>
                            <button class="create-action" onclick="addLocation()">
                                <i class="fas fa-map-marker-alt"></i> موقع
                            </button>
                        </div>
                    </div>

                    <!-- المنشورات -->
                    <div class="my-posts-container" id="myPostsContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسائل السريعة -->
    <div class="quick-replies-modal" id="quickRepliesModal">
        <div class="quick-replies-title">الرسائل السريعة ⚡</div>
        <div class="quick-reply-item" onclick="sendQuickReply('💰 كم السعر؟')">💰 كم السعر؟</div>
        <div class="quick-reply-item" onclick="sendQuickReply('📋 أريد عرض أسعار')">📋 أريد عروض</div>
        <div class="quick-reply-item" onclick="sendQuickReply('📦 هل المنتج متوفر؟')">📦 هل متوفر؟</div>
        <div class="quick-reply-item" onclick="sendQuickReply('🚚 كم تكلفة التوصيل؟')">🚚 تكلفة التوصيل</div>
        <div class="quick-reply-item" onclick="sendQuickReply('🤝 هل يمكن تخفيض السعر؟')">🤝 تخفيض السعر</div>
        <div class="quick-reply-item" onclick="sendQuickReply('📏 ما هي المقاسات؟')">📏 المقاسات</div>
        <div class="quick-reply-item" onclick="sendQuickReply('🎨 ما الألوان المتاحة؟')">🎨 الألوان المتاحة</div>
        <div class="quick-reply-item" onclick="sendQuickReply('💳 كيف أدفع؟')">💳 طريقة الدفع</div>
        <div class="quick-reply-item" onclick="sendQuickReply('🛡️ هل يوجد ضمان؟')">🛡️ الضمان</div>
        <div class="quick-reply-item" onclick="sendQuickReply('🔄 هل يمكن الاستبدال؟')">🔄 الاستبدال</div>
        <div class="quick-reply-item" onclick="sendQuickReply('⭐ ما تقييم المنتج؟')">⭐ التقييم</div>
        <div class="quick-reply-item" onclick="sendQuickReply('📞 رقم التواصل؟')">📞 رقم التواصل</div>
        <div class="quick-reply-item" onclick="sendQuickReply('📍 أين الموقع؟')">📍 الموقع</div>
        <div class="quick-reply-item" onclick="sendQuickReply('✅ موافق، سأشتري')">✅ موافق أشتري</div>
        <div class="quick-reply-item" onclick="sendQuickReply('❌ غير مناسب، شكراً')">❌ غير مناسب</div>
        <div class="quick-reply-item" onclick="sendQuickReply('🙏 شكراً لك')">🙏 شكراً</div>
    </div>

    <!-- خيارات المرفقات -->
    <div class="attachment-options" id="attachmentOptions">
        <div class="attachment-option" onclick="attachImage()">
            <i class="fas fa-image" style="color: #e74c3c;"></i>
            📷 صورة
        </div>
        <div class="attachment-option" onclick="attachVideo()">
            <i class="fas fa-video" style="color: #9b59b6;"></i>
            🎥 فيديو
        </div>
        <div class="attachment-option" onclick="attachFile()">
            <i class="fas fa-file" style="color: #3498db;"></i>
            📄 ملف
        </div>
        <div class="attachment-option" onclick="attachAudio()">
            <i class="fas fa-microphone" style="color: #f39c12;"></i>
            🎤 تسجيل صوتي
        </div>
        <div class="attachment-option" onclick="attachLocation()">
            <i class="fas fa-map-marker-alt" style="color: #27ae60;"></i>
            📍 الموقع
        </div>
        <div class="attachment-option" onclick="attachContact()">
            <i class="fas fa-address-book" style="color: #34495e;"></i>
            👤 جهة اتصال
        </div>
    </div>

    <!-- حقل رفع الملفات المخفي -->
    <input type="file" id="fileInput" style="display: none;" accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt" onchange="handleFileUpload(event)">
    <input type="file" id="imageInput" style="display: none;" accept="image/*" onchange="handleImageUpload(event)">
    <input type="file" id="videoInput" style="display: none;" accept="video/*" onchange="handleVideoUpload(event)">

    <script>
        // بيانات الخدمات
        const services = [
            { name: 'مطعم', icon: 'fas fa-utensils', image: 'مطعم.jpg' },
            { name: 'طبية', icon: 'fas fa-pills', image: 'طبية.jpg' },
            { name: 'مستحضرات تجميل', icon: 'fas fa-spa', image: 'مستحظرات تجميل.jpg' },
            { name: 'محلات', icon: 'fas fa-tshirt', image: 'محلات.jpg' },
            { name: 'السوبرماركت', icon: 'fas fa-shopping-cart', image: 'السوبرماركت.jpg' },
            { name: 'الطبية', icon: 'fas fa-user-md', image: 'الطبية.jpg' },
            { name: 'تعليم', icon: 'fas fa-graduation-cap', image: 'تعليم.jpg' },
            { name: 'سيارات', icon: 'fas fa-car', image: 'سيارات.jpg' },
            { name: 'عقارات', icon: 'fas fa-home', image: 'عقارات.jpg' },
            { name: 'كافيهات', icon: 'fas fa-coffee', image: 'كافيهات.jpg' },
            { name: 'مصارف وبنوك', icon: 'fas fa-university', image: 'مصارف وبنوك.jpg' },
            { name: 'زراعية', icon: 'fas fa-seedling', image: 'زراعية.jpg' },
            { name: 'توصيل', icon: 'fas fa-truck', image: 'توصيل.jpg' },
            { name: 'الصيرفة', icon: 'fas fa-coins', image: 'الصيرفة.jpg' },
            { name: 'الصناعة', icon: 'fas fa-industry', image: 'الصناعة.jpg' },
            { name: 'ذهب', icon: 'fas fa-gem', image: 'ذهب.jpg' },
            { name: 'قرطاسية', icon: 'fas fa-pen', image: 'قرطاسية.jpg' },
            { name: 'شركات السياحية', icon: 'fas fa-plane', image: 'شركات السياحية.jpg' },
            { name: 'مصايف', icon: 'fas fa-umbrella-beach', image: 'مصايف.jpg' },
            { name: 'استيراد وتصدير', icon: 'fas fa-ship', image: 'استيراد وتصدير.jpg' }
        ];

        // بيانات القصص
        const stories = [
            { name: 'قصتك', hasStory: false, isAdd: true },
            { name: 'أحمد محمد', hasStory: true, isAdd: false },
            { name: 'فاطمة علي', hasStory: false, isAdd: false },
            { name: 'محمد حسن', hasStory: true, isAdd: false },
            { name: 'سارة أحمد', hasStory: false, isAdd: false },
            { name: 'علي حسين', hasStory: true, isAdd: false }
        ];

        // بيانات البث المباشر
        const liveStreams = [
            { name: 'متجر الإلكترونيات', isLive: true },
            { name: 'مطعم الأصالة', isLive: false },
            { name: 'صيدلية النور', isLive: true },
            { name: 'معرض السيارات', isLive: false },
            { name: 'مركز التجميل', isLive: true }
        ];

        // بيانات العروض المميزة
        const premiumOffers = [
            {
                id: 1,
                title: 'مطعم الأصالة',
                subtitle: 'خصم 30% على جميع الوجبات',
                image: 'مطعم.jpg',
                icon: 'fas fa-utensils',
                seller: 'مطعم الأصالة',
                isPaid: true
            },
            {
                id: 2,
                title: 'معرض السيارات',
                subtitle: 'تخفيضات تصل إلى 50%',
                image: 'سيارات.jpg',
                icon: 'fas fa-car',
                seller: 'معرض النور',
                isPaid: true
            },
            {
                id: 3,
                title: 'مركز التجميل',
                subtitle: 'باقات تجميل بأسعار مميزة',
                image: 'مستحظرات تجميل.jpg',
                icon: 'fas fa-spa',
                seller: 'مركز الجمال',
                isPaid: true
            },
            {
                id: 4,
                title: 'صيدلية الشفاء',
                subtitle: 'خصومات على الأدوية',
                image: 'الطبية.jpg',
                icon: 'fas fa-pills',
                seller: 'صيدلية الشفاء',
                isPaid: true
            },
            {
                id: 5,
                title: 'متجر الإلكترونيات',
                subtitle: 'أحدث الأجهزة بأفضل الأسعار',
                image: 'محلات.jpg',
                icon: 'fas fa-laptop',
                seller: 'متجر التقنية',
                isPaid: true
            },
            {
                id: 6,
                title: 'كافيه المدينة',
                subtitle: 'مشروبات مميزة وأجواء رائعة',
                image: 'كافيهات.jpg',
                icon: 'fas fa-coffee',
                seller: 'كافيه المدينة',
                isPaid: true
            }
        ];

        // متغيرات التنقل للخدمات
        let currentPosition = 0;
        const cardWidth = 180;
        const cardGap = 15;
        const visibleCards = 7;
        const moveDistance = cardWidth + cardGap;

        // متغيرات التنقل للعروض المميزة
        let premiumCurrentPosition = 0;
        const premiumCardWidth = 380;
        const premiumCardGap = 20;
        const premiumVisibleCards = 3;
        const premiumMoveDistance = premiumCardWidth + premiumCardGap;

        // بيانات إعلانات الفيديو
        const videoAds = [
            {
                id: 1,
                title: 'أحدث الهواتف الذكية 2024',
                description: 'اكتشف مميزات الهاتف الجديد وتقنياته المتطورة',
                reward: '0.50$',
                icon: 'fas fa-mobile-alt',
                duration: '0:30',
                views: 1247,
                thumbnail: 'linear-gradient(135deg, #667eea, #764ba2)',
                channel: 'تقنية اليوم'
            },
            {
                id: 2,
                title: 'أزياء الموسم الجديد',
                description: 'تشكيلة رائعة من الأزياء العصرية للرجال والنساء',
                reward: '0.75$',
                icon: 'fas fa-tshirt',
                duration: '0:45',
                views: 892,
                thumbnail: 'linear-gradient(135deg, #f093fb, #f5576c)',
                channel: 'عالم الموضة'
            },
            {
                id: 3,
                title: 'وصفات شهية وسريعة',
                description: 'تعلم طبخ أشهى الوجبات في دقائق معدودة',
                reward: '0.60$',
                icon: 'fas fa-utensils',
                duration: '1:15',
                views: 2156,
                thumbnail: 'linear-gradient(135deg, #ffecd2, #fcb69f)',
                channel: 'مطبخ الأصالة'
            },
            {
                id: 4,
                title: 'نصائح للصحة والجمال',
                description: 'أسرار العناية بالبشرة والشعر من الخبراء',
                reward: '0.65$',
                icon: 'fas fa-spa',
                duration: '0:50',
                views: 1534,
                thumbnail: 'linear-gradient(135deg, #a8edea, #fed6e3)',
                channel: 'جمالك'
            }
        ];

        // بيانات المنشورات المميزة
        const trendingPosts = [
            {
                id: 1,
                userId: 'user1',
                author: 'متجر الإلكترونيات الذكية',
                category: 'إلكترونيات',
                content: 'عرض خاص على أحدث الهواتف الذكية! خصم يصل إلى 40% على جميع الموديلات الجديدة مع ضمان سنتين كاملتين.',
                likes: 1250,
                comments: 89,
                icon: 'fas fa-mobile-alt',
                images: ['الكترونيات 1.jpg', 'اليكترونيات2.jpg'],
                rating: 5,
                totalSales: 1250,
                memberSince: '3 سنوات',
                shares: 45,
                postComments: [
                    { author: 'أحمد محمد', text: 'منتجات ممتازة وجودة عالية! 👍', time: 'منذ ساعتين' },
                    { author: 'فاطمة علي', text: 'تعامل راقي وخدمة سريعة ✨', time: 'منذ 3 ساعات' },
                    { author: 'محمد سالم', text: 'أنصح بالتجربة، أسعار منافسة 💯', time: 'منذ 5 ساعات' }
                ],
                isTrending: true
            },
            {
                id: 2,
                userId: 'user6',
                author: 'مطعم الأصالة العربية',
                category: 'مطاعم',
                content: 'وجبات شهية وأصيلة! تذوق أشهى المأكولات العربية التقليدية مع خصم 25% على الطلبات الجماعية.',
                likes: 980,
                comments: 156,
                icon: 'fas fa-utensils',
                images: ['مطعم الاصالة.jpg', 'مطعم1.jpg', 'مطعم2.jpg'],
                rating: 4,
                totalSales: 890,
                memberSince: '2 سنة',
                shares: 32,
                postComments: [
                    { author: 'سارة أحمد', text: 'طعم رائع وأصيل! 🍽️', time: 'منذ ساعة' },
                    { author: 'خالد محمود', text: 'خدمة ممتازة وتوصيل سريع 🚀', time: 'منذ 4 ساعات' }
                ],
                isTrending: true
            },
            {
                id: 3,
                userId: 'user5',
                author: 'معرض السيارات الحديثة',
                category: 'سيارات',
                content: 'سيارات بحالة ممتازة وأسعار منافسة! تشكيلة واسعة من السيارات المستعملة والجديدة مع إمكانية التقسيط.',
                likes: 756,
                comments: 67,
                icon: 'fas fa-car',
                images: ['معرض السيارات.jpg'],
                rating: 5,
                totalSales: 456,
                memberSince: '1.5 سنة',
                shares: 28,
                postComments: [
                    { author: 'علي حسن', text: 'سيارات ممتازة وأسعار منافسة! 🚗', time: 'منذ ساعة' },
                    { author: 'نور محمد', text: 'تعامل راقي وصدق في الوصف ✨', time: 'منذ 3 ساعات' }
                ],
                isTrending: true
            },
            {
                id: 4,
                author: 'صيدلية الشفاء',
                category: 'طبية',
                content: 'خدمات طبية متميزة وأدوية أصلية. استشارات مجانية مع الصيدلي المختص وتوصيل سريع للمنزل.',
                likes: 634,
                comments: 43,
                icon: 'fas fa-pills',
                images: ['عرض صيدلية.jpg'],
                rating: 3,
                totalSales: 234,
                memberSince: '1 سنة',
                isTrending: false
            },
            {
                id: 5,
                author: 'مركز الجمال الملكي',
                category: 'تجميل',
                content: 'جلسات تجميل وعناية بالبشرة على أيدي خبراء متخصصين. باقات مميزة وأسعار تنافسية.',
                likes: 892,
                comments: 124,
                icon: 'fas fa-spa',
                images: ['مركز الجمال الملكي.jpg'],
                rating: 4,
                totalSales: 567,
                memberSince: '2.5 سنة',
                isTrending: true
            }
        ];

        // ملء الخدمات
        function populateServices() {
            const wrapper = document.getElementById('servicesWrapper');
            wrapper.innerHTML = services.map(service =>
                '<div class="service-card" onclick="openServicePage(\'' + service.name + '\')">' +
                    '<div class="service-image">' +
                        '<img src="images/' + service.image + '" alt="' + service.name + '" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">' +
                        '<div class="service-image-placeholder" style="display: none;">' +
                            '<i class="' + service.icon + '"></i>' +
                        '</div>' +
                    '</div>' +
                    '<div class="service-name">' + service.name + '</div>' +
                '</div>'
            ).join('');

            updateNavigationButtons();
        }

        // ملء القصص
        function populateStories() {
            const container = document.getElementById('storiesContainer');
            container.innerHTML = stories.map(story =>
                '<div class="story-item" onclick="openStory(\'' + story.name + '\')">' +
                    '<div class="story-avatar ' + (story.hasStory ? 'has-story' : 'no-story') + '">' +
                        '<div class="story-inner">' +
                            (story.isAdd ? '<i class="fas fa-plus" style="color: #227FCC;"></i>' : '<i class="fas fa-user"></i>') +
                        '</div>' +
                    '</div>' +
                    '<div class="story-name">' + story.name + '</div>' +
                '</div>'
            ).join('');
        }

        // ملء البث المباشر
        function populateLiveStreams() {
            const container = document.getElementById('liveContainer');
            container.innerHTML = liveStreams.map(stream =>
                '<div class="live-item" onclick="openLiveStream(\'' + stream.name + '\')">' +
                    '<div class="live-avatar ' + (stream.isLive ? 'is-live' : 'no-live') + '">' +
                        '<i class="fas fa-user"></i>' +
                        (stream.isLive ? '<div class="live-indicator">LIVE</div>' : '') +
                    '</div>' +
                    '<div class="story-name">' + stream.name + '</div>' +
                '</div>'
            ).join('');
        }

        // ملء العروض المميزة
        function populatePremiumOffers() {
            const wrapper = document.getElementById('premiumOffersWrapper');
            wrapper.innerHTML = premiumOffers.map(offer =>
                '<div class="premium-offer-card" onclick="openPremiumOffer(' + offer.id + ')">' +
                    '<div class="premium-offer-content">' +
                        '<img src="images/' + (offer.image || 'placeholder.jpg') + '" alt="' + offer.title + '" class="premium-offer-image" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">' +
                        '<div class="premium-offer-image-placeholder" style="display: none;">' +
                            '<i class="' + offer.icon + '"></i>' +
                        '</div>' +
                        '<div class="premium-offer-overlay">' +
                            '<div class="premium-offer-title">' + offer.title + '</div>' +
                            '<div class="premium-offer-subtitle">' + offer.subtitle + '</div>' +
                        '</div>' +
                    '</div>' +
                    '<div class="premium-offer-footer">' +
                        '<div class="premium-badge">عرض مميز</div>' +
                    '</div>' +
                '</div>'
            ).join('');

            updatePremiumNavigationButtons();
        }

        // تحديث أزرار التنقل للخدمات
        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const maxPosition = (services.length - visibleCards) * moveDistance;

            prevBtn.disabled = currentPosition >= maxPosition;
            nextBtn.disabled = currentPosition <= 0;
        }

        // تحديث أزرار التنقل للعروض المميزة
        function updatePremiumNavigationButtons() {
            const prevBtn = document.getElementById('premiumPrevBtn');
            const nextBtn = document.getElementById('premiumNextBtn');
            const maxPosition = (premiumOffers.length - premiumVisibleCards) * premiumMoveDistance;

            prevBtn.disabled = premiumCurrentPosition >= maxPosition;
            nextBtn.disabled = premiumCurrentPosition <= 0;
        }

        // تحريك الخدمات
        function moveServices(direction) {
            const wrapper = document.getElementById('servicesWrapper');
            const maxPosition = (services.length - visibleCards) * moveDistance;

            if (direction === 'right' && currentPosition < maxPosition) {
                currentPosition += moveDistance;
            } else if (direction === 'left' && currentPosition > 0) {
                currentPosition -= moveDistance;
            }

            currentPosition = Math.max(0, Math.min(currentPosition, maxPosition));
            wrapper.style.transform = 'translateX(-' + currentPosition + 'px)';
            updateNavigationButtons();
        }

        // تحريك العروض المميزة
        function movePremiumOffers(direction) {
            const wrapper = document.getElementById('premiumOffersWrapper');
            const maxPosition = (premiumOffers.length - premiumVisibleCards) * premiumMoveDistance;

            if (direction === 'right' && premiumCurrentPosition < maxPosition) {
                premiumCurrentPosition += premiumMoveDistance;
            } else if (direction === 'left' && premiumCurrentPosition > 0) {
                premiumCurrentPosition -= premiumMoveDistance;
            }

            premiumCurrentPosition = Math.max(0, Math.min(premiumCurrentPosition, maxPosition));
            wrapper.style.transform = 'translateX(-' + premiumCurrentPosition + 'px)';
            updatePremiumNavigationButtons();
        }

        // فتح صفحة الخدمة
        function openServicePage(serviceName) {
            if (serviceName === 'عقارات') {
                showRealEstateSection();
            } else {
                alert('فتح صفحة ' + serviceName + ' - سيتم تطويرها لاحقاً');
            }
        }

        // عرض قسم العقارات
        function showRealEstateSection() {
            // إخفاء جميع الأقسام الأخرى
            document.getElementById('feedSection').style.display = 'none';
            document.getElementById('notificationsSection').style.display = 'none';
            document.getElementById('messagesSection').style.display = 'none';
            document.getElementById('profileSection').style.display = 'none';

            // إنشاء قسم العقارات إذا لم يكن موجوداً
            let realEstateSection = document.getElementById('realEstateSection');
            if (!realEstateSection) {
                realEstateSection = document.createElement('div');
                realEstateSection.id = 'realEstateSection';
                realEstateSection.className = 'main-section';
                document.body.appendChild(realEstateSection);
            }

            // عرض قسم العقارات
            realEstateSection.style.display = 'block';

            // ملء محتوى العقارات
            populateRealEstateContent();

            // تحديث شريط التنقل
            updateNavigationForRealEstate();
        }

        function openStory(userName) {
            alert('فتح قصة ' + userName);
        }

        function openLiveStream(userName) {
            alert('فتح بث ' + userName + ' المباشر');
        }

        // فتح العرض المميز
        function openPremiumOffer(offerId) {
            const offer = premiumOffers.find(o => o.id === offerId);
            if (offer) {
                alert('فتح العرض المميز: ' + offer.title + '\n\nهذا عرض مدفوع من البائع: ' + offer.seller + '\n\nسيتم فتح صفحة العرض التفصيلية...');
            }
        }

        // ملء إعلانات الفيديو
        function populateVideoAds() {
            const container = document.getElementById('videoAdsContainer');
            container.innerHTML = videoAds.map(ad =>
                '<div class="video-ad-item" id="video-ad-' + ad.id + '">' +
                    '<button class="video-close-btn" onclick="closeVideoAd(' + ad.id + ')">' +
                        '<i class="fas fa-times"></i>' +
                    '</button>' +
                    '<div onclick="watchVideoAd(' + ad.id + ')">' +
                        '<div class="video-thumbnail" style="background: ' + ad.thumbnail + '">' +
                            '<div class="play-button">' +
                                '<i class="fas fa-play"></i>' +
                            '</div>' +
                            '<div class="video-duration">' + ad.duration + '</div>' +
                        '</div>' +
                        '<div class="video-ad-content">' +
                            '<div class="video-ad-header">' +
                                '<div class="video-ad-icon">' +
                                    '<i class="' + ad.icon + '"></i>' +
                                '</div>' +
                                '<div class="video-ad-title">' + ad.title + '</div>' +
                            '</div>' +
                            '<div class="video-ad-description">' + ad.description + '</div>' +
                            '<div class="video-ad-footer">' +
                                '<div class="video-ad-reward">+' + ad.reward + '</div>' +
                                '<div class="video-ad-views">' +
                                    '<i class="fas fa-eye"></i>' +
                                    '<span>' + ad.views.toLocaleString() + '</span>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                    '</div>' +
                '</div>'
            ).join('');
        }

        // ملء المنشورات المميزة
        function populateTrendingPosts() {
            const container = document.getElementById('trendingPostsContainer');
            container.innerHTML = trendingPosts.map(post =>
                '<div class="trending-post">' +
                    '<div class="trending-post-header">' +
                        '<div class="trending-post-avatar" onclick="openUserProfile(\'' + post.userId + '\')" style="cursor: pointer;">' +
                            '<i class="' + post.icon + '"></i>' +
                        '</div>' +
                        '<div class="trending-post-info">' +
                            '<div class="trending-post-author" onclick="openUserProfile(\'' + post.userId + '\')" style="cursor: pointer;">' +
                                post.author +
                                (post.isTrending ?
                                    '&nbsp;&nbsp;&nbsp;<span class="trending-badge">' +
                                        '<span class="crown-icon">' +
                                            '<i class="fas fa-crown"></i>' +
                                        '</span>' +
                                        '<span class="crown-text">مميز</span>' +
                                    '</span>' : '') +
                            '</div>' +
                            '<div class="trending-post-category">' + post.category + '</div>' +
                            '<div class="seller-rating">' +
                                '<div class="rating-stars">' +
                                    generateStars(post.rating) +
                                '</div>' +
                                '<div class="rating-info">' +
                                    '<span>(' + post.totalSales + ' عملية بيع)</span>' +
                                    '<span class="membership-duration">عضو منذ ' + post.memberSince + '</span>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                        '<div class="post-controls">' +
                            '<button class="post-menu-btn" onclick="togglePostMenu(' + post.id + ')">' +
                                '<i class="fas fa-ellipsis-v"></i>' +
                            '</button>' +
                            '<button class="close-post-btn" onclick="closePost(' + post.id + ')">' +
                                '<i class="fas fa-times"></i>' +
                            '</button>' +
                        '</div>' +
                        '<div class="post-menu" id="post-menu-' + post.id + '">' +
                            '<button class="menu-option interested" onclick="setInterested(' + post.id + ', true)">' +
                                '<i class="fas fa-thumbs-up"></i>' +
                                '<span>مهتم</span>' +
                            '</button>' +
                            '<button class="menu-option not-interested" onclick="setInterested(' + post.id + ', false)">' +
                                '<i class="fas fa-thumbs-down"></i>' +
                                '<span>غير مهتم</span>' +
                            '</button>' +
                            '<div class="menu-divider"></div>' +
                            '<button class="menu-option save" onclick="savePost(' + post.id + ')">' +
                                '<i class="fas fa-bookmark"></i>' +
                                '<span>حفظ المنشور</span>' +
                            '</button>' +
                            '<button class="menu-option notifications" onclick="toggleNotifications(' + post.id + ')">' +
                                '<i class="fas fa-bell"></i>' +
                                '<span>تشغيل الإشعارات لهذا المنشور</span>' +
                            '</button>' +
                            '<div class="menu-divider"></div>' +
                            '<button class="menu-option hide-post" onclick="hidePost(' + post.id + ')">' +
                                '<i class="fas fa-eye-slash"></i>' +
                                '<span>إخفاء المنشور</span>' +
                            '</button>' +
                            '<button class="menu-option hide-user" onclick="hideUser(' + post.id + ')">' +
                                '<i class="fas fa-user-slash"></i>' +
                                '<span>إخفاء كل الوارد من هذا المستخدم</span>' +
                            '</button>' +
                            '<div class="menu-divider"></div>' +
                            '<button class="menu-option report" onclick="reportPost(' + post.id + ')">' +
                                '<i class="fas fa-flag"></i>' +
                                '<span>الإبلاغ عن هذا المنشور</span>' +
                            '</button>' +
                            '<button class="menu-option block" onclick="blockUser(' + post.id + ')">' +
                                '<i class="fas fa-ban"></i>' +
                                '<span>حظر هذا المستخدم</span>' +
                            '</button>' +
                        '</div>' +

                    '</div>' +
                    '<div class="trending-post-content">' + post.content + '</div>' +
                    (post.images ?
                        '<div class="trending-post-images ' + getImageLayoutClass(post.images.length, post.id) + '">' +
                            (post.id === 2 && post.images.length === 3 ?
                                // تخطيط خاص للمطعم
                                '<div style="position: relative;">' +
                                    '<img src="assets/images/' + post.images[0] + '" alt="صورة المطعم" class="trending-post-image" ' +
                                    'onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';" ' +
                                    'onclick="viewImage(\'' + post.images[0] + '\')">' +
                                    '<div class="trending-post-image-placeholder" style="display: none;">' +
                                        '<i class="' + post.icon + '"></i>' +
                                    '</div>' +
                                '</div>' +
                                '<div class="food-images">' +
                                    post.images.slice(1).map(image =>
                                        '<div style="position: relative;">' +
                                            '<img src="assets/images/' + image + '" alt="صورة الطعام" class="trending-post-image" ' +
                                            'onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';" ' +
                                            'onclick="viewImage(\'' + image + '\')">' +
                                            '<div class="trending-post-image-placeholder" style="display: none;">' +
                                                '<i class="' + post.icon + '"></i>' +
                                            '</div>' +
                                        '</div>'
                                    ).join('') +
                                '</div>'
                                :
                                // تخطيط عادي للمنشورات الأخرى
                                post.images.map((image, index) => {
                                    const showMoreOverlay = post.images.length > 3 && index === 2;
                                    const remainingCount = post.images.length - 3;
                                    return '<div style="position: relative;">' +
                                        '<img src="assets/images/' + image + '" alt="صورة المنشور" class="trending-post-image" ' +
                                        'onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';" ' +
                                        'onclick="viewImage(\'' + image + '\')">' +
                                        '<div class="trending-post-image-placeholder" style="display: none;">' +
                                            '<i class="' + post.icon + '"></i>' +
                                        '</div>' +
                                        (showMoreOverlay ?
                                            '<div class="more-images-overlay" onclick="viewAllImages(' + post.id + ')">+' + remainingCount + '</div>'
                                            : '') +
                                    '</div>';
                                }).slice(0, 3).join('')
                            ) +
                        '</div>' : ''
                    ) +
                    '<div class="trending-post-stats">' +
                        '<div class="stat-item likes-stat">' +
                            '<i class="fas fa-heart stat-icon"></i>' +
                            '<span class="stat-number">' + post.likes + '</span>' +
                        '</div>' +
                        '<div class="stat-item comments-stat" onclick="toggleComments(' + post.id + ')">' +
                            '<i class="fas fa-comment stat-icon"></i>' +
                            '<span class="stat-number">' + post.comments + '</span>' +
                        '</div>' +
                        '<div class="stat-item shares-stat">' +
                            '<i class="fas fa-share stat-icon"></i>' +
                            '<span class="stat-number">' + (post.shares || 0) + '</span>' +
                        '</div>' +
                    '</div>' +
                    '<div class="trending-post-actions">' +
                        '<button class="action-btn like-btn" onclick="likePost(' + post.id + ')" id="like-btn-' + post.id + '">' +
                            '<i class="fas fa-heart"></i>' +
                            '<span>إعجاب (' + post.likes + ')</span>' +
                        '</button>' +
                        '<button class="action-btn comment-btn" onclick="openCommentModal(' + post.id + ')">' +
                            '<i class="fas fa-comment"></i>' +
                            '<span>تعليق (' + post.comments + ')</span>' +
                        '</button>' +
                        '<button class="action-btn share-btn" onclick="openShareModal(' + post.id + ')">' +
                            '<i class="fas fa-share"></i>' +
                            '<span>مشاركة</span>' +
                        '</button>' +
                        '<button class="action-btn favorite-btn" onclick="toggleFavorite(' + post.id + ')" id="favorite-btn-' + post.id + '">' +
                            '<i class="fas fa-shield-alt"></i>' +
                            '<span>حفظ</span>' +
                        '</button>' +
                    '</div>' +
                    '<div class="comments-section" id="comments-section-' + post.id + '">' +
                        '<div class="comments-header">' +
                            '<div class="comments-title">التعليقات <span class="comments-count">(' + post.comments + ')</span></div>' +
                            '<button class="comments-toggle" onclick="toggleComments(' + post.id + ')">إخفاء</button>' +
                        '</div>' +
                        '<div class="comments-list" id="comments-list-' + post.id + '">' +
                            '<!-- سيتم ملؤها بـ JavaScript -->' +
                        '</div>' +
                        '<div class="add-comment">' +
                            '<div class="add-comment-avatar"></div>' +
                            '<input type="text" class="add-comment-input" placeholder="اكتب تعليقاً..." onclick="openCommentModal(' + post.id + ')" readonly>' +
                        '</div>' +
                    '</div>' +
                '</div>'
            ).join('');
        }

        // مشاهدة إعلان فيديو
        function watchVideoAd(adId) {
            const ad = videoAds.find(a => a.id === adId);
            if (ad) {
                if (ad.watchedByUser) {
                    alert('⚠️ لقد شاهدت هذا الإعلان من قبل!\n\nيمكنك مشاهدة كل إعلان مرة واحدة فقط يومياً.');
                    return;
                }

                // محاكاة تشغيل الفيديو
                const watchTime = parseInt(ad.duration.split(':')[1]) || 30;

                alert('🎬 بدء تشغيل الفيديو...\n\n' +
                      '📺 العنوان: ' + ad.title + '\n' +
                      '📺 القناة: ' + ad.channel + '\n' +
                      '⏱️ المدة: ' + ad.duration + '\n' +
                      '👀 المشاهدات: ' + ad.views.toLocaleString() + '\n\n' +
                      '💰 ستحصل على ' + ad.reward + ' بعد المشاهدة الكاملة!\n\n' +
                      '⏳ يرجى الانتظار...');

                // محاكاة انتظار انتهاء الفيديو
                setTimeout(() => {
                    ad.views++;
                    ad.watchedByUser = true;
                    populateVideoAds(); // تحديث العرض
                    updateEarningsStats(); // تحديث الإحصائيات

                    alert('✅ تم إنهاء المشاهدة بنجاح!\n\n' +
                          '💰 تم إضافة ' + ad.reward + ' إلى رصيدك\n' +
                          '🎯 إجمالي أرباحك اليوم: ' + document.getElementById('totalEarnings').textContent + '\n\n' +
                          'شكراً لك! يمكنك مشاهدة المزيد من الإعلانات للحصول على المزيد من الأرباح.');
                }, 3000); // محاكاة انتظار لـ 3 ثوانٍ
            }
        }

        // حساب إجمالي الأرباح
        function getTotalEarnings() {
            return videoAds.reduce((total, ad) => {
                const reward = parseFloat(ad.reward.replace('$', ''));
                return total + reward;
            }, 0).toFixed(2) + '$';
        }

        // تحديث إحصائيات الأرباح
        function updateEarningsStats() {
            const totalEarnings = videoAds.reduce((total, ad) => {
                const reward = parseFloat(ad.reward.replace('$', ''));
                return total + (ad.watchedByUser ? reward : 0);
            }, 0);

            const videosWatched = videoAds.filter(ad => ad.watchedByUser).length;
            const totalViews = videoAds.reduce((total, ad) => total + (ad.watchedByUser ? 1 : 0), 0);

            document.getElementById('totalEarnings').textContent = '$' + totalEarnings.toFixed(2);
            document.getElementById('videosWatched').textContent = videosWatched;
            document.getElementById('totalViews').textContent = totalViews;
        }

        // فتح منشور مميز
        function openTrendingPost(postId) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post) {
                alert('منشور مميز من: ' + post.author + '\n\nالفئة: ' + post.category + '\n\nالإعجابات: ' + post.likes + '\nالتعليقات: ' + post.comments + '\n\nهذا المنشور مميز بسبب التفاعل العالي من المشترين!');
            }
        }

        // إعجاب بمنشور
        function likePost(postId) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post) {
                const likeBtn = document.getElementById('like-btn-' + postId);
                if (likeBtn.classList.contains('liked')) {
                    post.likes--;
                    likeBtn.classList.remove('liked');
                } else {
                    post.likes++;
                    likeBtn.classList.add('liked');
                }
                likeBtn.querySelector('span').textContent = 'إعجاب (' + post.likes + ')';
            }
        }

        // إضافة/إزالة من المفضلة
        function toggleFavorite(postId) {
            const favoriteBtn = document.getElementById('favorite-btn-' + postId);

            if (favoriteBtn.classList.contains('favorited')) {
                favoriteBtn.classList.remove('favorited');
            } else {
                favoriteBtn.classList.add('favorited');
            }
        }

        // متغيرات للنوافذ المنبثقة
        let currentPostId = null;
        let selectedComment = null;
        let selectedShareOption = null;

        // التعليقات السريعة
        const quickComments = [
            'رائع! 👍',
            'ممتاز جداً ✨',
            'أعجبني كثيراً ❤️',
            'شكراً لك 🙏',
            'مفيد جداً 💯',
            'أريد المزيد من التفاصيل 🤔',
            'سعر ممتاز 💰',
            'جودة عالية 🌟',
            'خدمة ممتازة 🎯',
            'أنصح بالتجربة 👌'
        ];

        // فتح نافذة التعليق
        function openCommentModal(postId) {
            currentPostId = postId;
            selectedComment = null;

            const modal = document.getElementById('commentModal');
            const optionsContainer = document.getElementById('commentOptions');

            // ملء خيارات التعليق
            optionsContainer.innerHTML = quickComments.map((comment, index) =>
                '<div class="comment-option" onclick="selectComment(' + index + ')">' + comment + '</div>'
            ).join('');

            modal.classList.add('show');
        }

        // اختيار تعليق
        function selectComment(index) {
            selectedComment = index;

            // إزالة التحديد من جميع الخيارات
            document.querySelectorAll('.comment-option').forEach(option => {
                option.classList.remove('selected');
            });

            // تحديد الخيار المختار
            document.querySelectorAll('.comment-option')[index].classList.add('selected');
        }

        // إرسال التعليق
        function submitComment() {
            if (selectedComment !== null && currentPostId) {
                const post = trendingPosts.find(p => p.id === currentPostId);
                if (post) {
                    // إضافة التعليق الجديد
                    const newComment = {
                        author: 'أنت',
                        text: quickComments[selectedComment],
                        time: 'الآن'
                    };

                    if (!post.postComments) {
                        post.postComments = [];
                    }
                    post.postComments.unshift(newComment);
                    post.comments++;

                    // تحديث عداد التعليقات في الإحصائيات
                    const commentStat = document.querySelector(`#comments-section-${currentPostId}`).parentElement.querySelector('.comments-stat .stat-number');
                    if (commentStat) {
                        commentStat.textContent = post.comments;
                    }

                    // تحديث عداد التعليقات في الأزرار
                    const commentBtn = document.querySelector(`#like-btn-${currentPostId}`).parentElement.querySelector('.comment-btn span');
                    if (commentBtn) {
                        commentBtn.textContent = 'تعليق (' + post.comments + ')';
                    }

                    // تحديث قائمة التعليقات فوراً
                    populateComments(currentPostId);

                    // التأكد من أن قسم التعليقات مفتوح
                    const commentsSection = document.getElementById('comments-section-' + currentPostId);
                    if (!commentsSection.classList.contains('show')) {
                        toggleComments(currentPostId);
                    }
                }

                closeCommentModal();
            }
        }

        // إغلاق نافذة التعليق
        function closeCommentModal() {
            document.getElementById('commentModal').classList.remove('show');
            currentPostId = null;
            selectedComment = null;
        }

        // فتح نافذة المشاركة
        function openShareModal(postId) {
            currentPostId = postId;
            selectedShareOption = null;
            document.getElementById('shareModal').classList.add('show');
        }

        // اختيار خيار المشاركة
        function selectShareOption(option) {
            selectedShareOption = option;

            const appNames = {
                'facebook': 'فيسبوك',
                'instagram': 'انستجرام',
                'tiktok': 'تيك توك',
                'whatsapp': 'واتساب',
                'viber': 'فايبر',
                'profile': 'صفحتك الشخصية'
            };

            const appIcons = {
                'facebook': 'fab fa-facebook',
                'instagram': 'fab fa-instagram',
                'tiktok': 'fab fa-tiktok',
                'whatsapp': 'fab fa-whatsapp',
                'viber': 'fab fa-viber',
                'profile': 'fas fa-user'
            };

            // إخفاء نافذة اختيار المشاركة
            document.getElementById('shareModal').classList.remove('show');

            // إظهار نافذة معاينة المشاركة
            const previewModal = document.getElementById('sharePreviewModal');
            const previewTitle = document.getElementById('sharePreviewTitle');
            const postPreview = document.getElementById('sharePostPreview');

            // تحديث العنوان
            previewTitle.innerHTML = '<i class="' + appIcons[option] + '"></i><span>مشاركة في ' + appNames[option] + '</span>';

            // إنشاء معاينة المنشور
            const post = trendingPosts.find(p => p.id === currentPostId);
            if (post) {
                postPreview.innerHTML =
                    '<div class="share-post-author">' + post.author + '</div>' +
                    '<div class="share-post-content">' + post.content + '</div>' +
                    (post.images && post.images.length > 0 ?
                        '<img src="' + post.images[0] + '" alt="صورة المنشور" class="share-post-image">' : '');
            }

            previewModal.classList.add('show');
        }

        // تأكيد المشاركة
        function confirmShare() {
            if (selectedShareOption && currentPostId) {
                // هنا يتم تنفيذ المشاركة الفعلية
                document.getElementById('shareConfirm').classList.remove('show');
                closeShareModal();
            }
        }

        // إلغاء المشاركة
        function cancelShare() {
            document.getElementById('shareConfirm').classList.remove('show');
            selectedShareOption = null;
        }

        // إغلاق نافذة المشاركة
        function closeShareModal() {
            document.getElementById('shareModal').classList.remove('show');
            document.getElementById('shareConfirm').classList.remove('show');
            document.getElementById('sharePreviewModal').classList.remove('show');
            currentPostId = null;
            selectedShareOption = null;
        }

        // إغلاق نافذة المعاينة
        function closeSharePreview() {
            document.getElementById('sharePreviewModal').classList.remove('show');
            currentPostId = null;
            selectedShareOption = null;
        }

        // العودة لخيارات المشاركة
        function backToShareOptions() {
            document.getElementById('sharePreviewModal').classList.remove('show');
            document.getElementById('shareModal').classList.add('show');
        }

        // تأكيد المشاركة النهائية
        function confirmFinalShare() {
            if (selectedShareOption && currentPostId) {
                const post = trendingPosts.find(p => p.id === currentPostId);
                if (post) {
                    // زيادة عداد المشاركات
                    if (!post.shares) post.shares = 0;
                    post.shares++;

                    // تحديث عداد المشاركات في الواجهة
                    const sharesStat = document.querySelector(`#comments-section-${currentPostId}`).parentElement.querySelector('.shares-stat .stat-number');
                    if (sharesStat) {
                        sharesStat.textContent = post.shares;
                    }
                }

                closeSharePreview();
            }
        }

        // تبديل عرض التعليقات
        function toggleComments(postId) {
            const commentsSection = document.getElementById('comments-section-' + postId);
            const toggleBtn = commentsSection.querySelector('.comments-toggle');

            if (commentsSection.classList.contains('show')) {
                commentsSection.classList.remove('show');
                toggleBtn.textContent = 'عرض';
            } else {
                commentsSection.classList.add('show');
                toggleBtn.textContent = 'إخفاء';
                populateComments(postId);
            }
        }

        // ملء التعليقات
        function populateComments(postId) {
            const post = trendingPosts.find(p => p.id === postId);
            const commentsList = document.getElementById('comments-list-' + postId);

            if (post && post.postComments && commentsList) {
                commentsList.innerHTML = post.postComments.map((comment, index) =>
                    '<div class="comment-item" id="comment-' + postId + '-' + index + '">' +
                        '<div class="comment-avatar' + (comment.author === 'أنت' ? ' user-avatar' : '') + '">' +
                            (comment.author === 'أنت' ? '' : '<i class="fas fa-user"></i>') +
                        '</div>' +
                        '<div class="comment-content">' +
                            '<div class="comment-bubble">' +
                                '<div class="comment-author" onclick="openUserProfile(\'' + comment.author + '\')" style="cursor: pointer;">' + comment.author + '</div>' +
                                '<div class="comment-text" id="comment-text-' + postId + '-' + index + '">' + comment.text + '</div>' +
                            '</div>' +
                            '<div class="comment-actions">' +
                                '<span class="comment-action">إعجاب</span>' +
                                '<span class="comment-action">رد</span>' +
                                '<span class="comment-time">' + comment.time + '</span>' +
                                (comment.author === 'أنت' ?
                                    '<div class="comment-menu">' +
                                        '<button class="comment-menu-btn" onclick="toggleCommentMenu(' + postId + ', ' + index + ')">' +
                                            '<i class="fas fa-ellipsis-h"></i>' +
                                        '</button>' +
                                        '<div class="comment-dropdown" id="comment-menu-' + postId + '-' + index + '">' +
                                            '<div class="comment-dropdown-item edit" onclick="editComment(' + postId + ', ' + index + ')">' +
                                                '<i class="fas fa-edit"></i> تعديل' +
                                            '</div>' +
                                            '<div class="comment-dropdown-item delete" onclick="deleteComment(' + postId + ', ' + index + ')">' +
                                                '<i class="fas fa-trash"></i> حذف' +
                                            '</div>' +
                                        '</div>' +
                                    '</div>' : '') +
                            '</div>' +
                        '</div>' +
                    '</div>'
                ).join('');

                // تحديث عداد التعليقات في العنوان
                const commentsTitle = document.querySelector(`#comments-section-${postId} .comments-title`);
                if (commentsTitle) {
                    commentsTitle.innerHTML = 'التعليقات <span class="comments-count">(' + post.comments + ')</span>';
                }
            }
        }



        // تبديل قائمة المنشور
        function togglePostMenu(postId) {
            const menu = document.getElementById('post-menu-' + postId);
            const isVisible = menu.classList.contains('show');

            // إخفاء جميع القوائم الأخرى
            document.querySelectorAll('.post-menu').forEach(m => m.classList.remove('show'));
            document.querySelectorAll('.share-menu').forEach(m => m.classList.remove('show'));

            // تبديل القائمة الحالية
            if (!isVisible) {
                menu.classList.add('show');
            }
        }

        // تعيين الاهتمام
        function setInterested(postId, interested) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post) {
                const status = interested ? 'مهتم' : 'غير مهتم';
                alert('تم تعيين حالتك كـ "' + status + '" لمنشور ' + post.author + '\n\nسيتم تحسين المحتوى المعروض لك بناءً على اهتماماتك.');
                togglePostMenu(postId);
            }
        }

        // حفظ المنشور
        function savePost(postId) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post) {
                alert('تم حفظ منشور ' + post.author + ' في مجموعة "المحفوظات"\n\nيمكنك العثور عليه في قسم المحفوظات في ملفك الشخصي.');
                togglePostMenu(postId);
            }
        }

        // تشغيل/إيقاف الإشعارات
        function toggleNotifications(postId) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post) {
                alert('تم تشغيل الإشعارات لمنشورات ' + post.author + '\n\nستحصل على إشعار عند نشر محتوى جديد من هذا المستخدم.');
                togglePostMenu(postId);
            }
        }

        // إخفاء المنشور
        function hidePost(postId) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post) {
                if (confirm('هل أنت متأكد من إخفاء هذا المنشور؟\n\nلن يظهر لك مرة أخرى في الخلاصة.')) {
                    alert('تم إخفاء منشور ' + post.author + '\n\nلن يظهر هذا المنشور في خلاصتك مرة أخرى.');
                    togglePostMenu(postId);
                }
            }
        }

        // إخفاء المستخدم
        function hideUser(postId) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post) {
                if (confirm('هل أنت متأكد من إخفاء جميع منشورات ' + post.author + '؟\n\nلن تظهر لك منشوراتهم في الخلاصة.')) {
                    alert('تم إخفاء جميع منشورات ' + post.author + '\n\nلن تظهر منشورات هذا المستخدم في خلاصتك.');
                    togglePostMenu(postId);
                }
            }
        }

        // الإبلاغ عن المنشور
        function reportPost(postId) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post) {
                const reasons = [
                    'محتوى مضلل',
                    'محتوى غير لائق',
                    'انتهاك حقوق الطبع والنشر',
                    'بريد عشوائي',
                    'محتوى مسيء',
                    'أخرى'
                ];

                let reportReason = 'اختر سبب الإبلاغ:\n\n';
                reasons.forEach((reason, index) => {
                    reportReason += (index + 1) + '. ' + reason + '\n';
                });

                const choice = prompt(reportReason + '\nأدخل رقم السبب:');
                const reasonIndex = parseInt(choice) - 1;

                if (reasonIndex >= 0 && reasonIndex < reasons.length) {
                    alert('تم إرسال بلاغك عن منشور ' + post.author + '\n\nسبب البلاغ: ' + reasons[reasonIndex] + '\n\nسيتم مراجعة البلاغ من قبل فريق المراجعة.');
                    togglePostMenu(postId);
                }
            }
        }

        // حظر المستخدم
        function blockUser(postId) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post) {
                if (confirm('هل أنت متأكد من حظر ' + post.author + '؟\n\nلن تتمكن من رؤية منشوراتهم أو التفاعل معهم.')) {
                    alert('تم حظر ' + post.author + '\n\nلن تتمكن من رؤية منشوراتهم أو التفاعل معهم.\nيمكنك إلغاء الحظر من إعدادات الحساب.');
                    togglePostMenu(postId);
                }
            }
        }

        // إغلاق المنشور
        function closePost(postId) {
            const postElement = document.getElementById('trending-post-' + postId);
            if (postElement) {
                postElement.style.display = 'none';
            }
        }

        // إغلاق فيديو إعلاني محدد
        function closeVideoAd(adId) {
            const videoAdElement = document.getElementById('video-ad-' + adId);
            if (videoAdElement) {
                videoAdElement.style.display = 'none';
            }
        }

        // تبديل قائمة خيارات التعليق
        function toggleCommentMenu(postId, commentIndex) {
            console.log('toggleCommentMenu called:', postId, commentIndex);
            const menu = document.getElementById('comment-menu-' + postId + '-' + commentIndex);
            console.log('Menu element:', menu);

            if (!menu) {
                console.error('Menu not found!');
                return;
            }

            const isVisible = menu.classList.contains('show');
            console.log('Is visible:', isVisible);

            // إخفاء جميع القوائم الأخرى
            document.querySelectorAll('.comment-dropdown').forEach(m => m.classList.remove('show'));

            // تبديل القائمة الحالية
            if (!isVisible) {
                menu.classList.add('show');
                console.log('Menu shown');
            }
        }

        // تعديل التعليق
        function editComment(postId, commentIndex) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post && post.postComments && post.postComments[commentIndex]) {
                const comment = post.postComments[commentIndex];
                const newText = prompt('تعديل التعليق:', comment.text);

                if (newText && newText.trim() !== '') {
                    comment.text = newText.trim();
                    comment.time = 'تم التعديل الآن';

                    // تحديث النص في الواجهة
                    const commentTextElement = document.getElementById('comment-text-' + postId + '-' + commentIndex);
                    if (commentTextElement) {
                        commentTextElement.textContent = comment.text;
                    }

                    // إعادة ملء التعليقات لتحديث الوقت
                    populateComments(postId);
                }
            }

            // إخفاء القائمة
            toggleCommentMenu(postId, commentIndex);
        }

        // حذف التعليق
        function deleteComment(postId, commentIndex) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post && post.postComments && post.postComments[commentIndex]) {
                if (confirm('هل أنت متأكد من حذف هذا التعليق؟')) {
                    // حذف التعليق من المصفوفة
                    post.postComments.splice(commentIndex, 1);
                    post.comments--;

                    // تحديث عداد التعليقات في الإحصائيات
                    const commentStat = document.querySelector(`#comments-section-${postId}`).parentElement.querySelector('.comments-stat .stat-number');
                    if (commentStat) {
                        commentStat.textContent = post.comments;
                    }

                    // تحديث عداد التعليقات في الأزرار
                    const commentBtn = document.querySelector(`#like-btn-${postId}`).parentElement.querySelector('.comment-btn span');
                    if (commentBtn) {
                        commentBtn.textContent = 'تعليق (' + post.comments + ')';
                    }

                    // إعادة ملء التعليقات
                    populateComments(postId);
                }
            }
        }

        // بيانات الإشعارات
        let notifications = [
            {
                id: 1,
                type: 'comment',
                author: 'أحمد محمد',
                avatar: 'images/Hussein Nihad.png',
                action: 'علق على منشورك',
                text: 'منتج ممتاز، أنصح بالتجربة! جودة عالية وسعر مناسب',
                time: 'منذ 5 دقائق',
                unread: true,
                selected: false
            },
            {
                id: 2,
                type: 'like',
                author: 'سارة أحمد',
                avatar: 'images/Hussein Nihad.png',
                action: 'أعجبت بمنشورك',
                text: 'مع 12 شخص آخر',
                time: 'منذ 15 دقيقة',
                unread: true,
                selected: false
            },
            {
                id: 3,
                type: 'comment',
                author: 'محمد علي',
                avatar: 'images/Hussein Nihad.png',
                action: 'علق على منشورك',
                text: 'هل يمكن معرفة السعر والتوصيل؟',
                time: 'منذ 30 دقيقة',
                unread: true,
                selected: false
            },
            {
                id: 4,
                type: 'offer',
                author: 'متجر الأزياء',
                avatar: null,
                action: 'عرض خاص',
                text: 'خصم 50% على جميع الملابس - لفترة محدودة',
                time: 'منذ ساعة',
                unread: false,
                selected: false
            },
            {
                id: 5,
                type: 'system',
                author: 'Get Me',
                avatar: null,
                action: 'تحديث التطبيق',
                text: 'تحديث جديد متاح مع مميزات وتحسينات جديدة',
                time: 'منذ 3 ساعات',
                unread: false,
                selected: false
            }
        ];

        let currentTab = 'all';
        let selectedNotifications = [];

        // الإشعارات السابقة
        let oldNotifications = [
            {
                id: 6,
                type: 'comment',
                author: 'فاطمة علي',
                avatar: 'images/Hussein Nihad.png',
                action: 'علقت على منشورك',
                text: 'شكراً لك على المعلومات المفيدة',
                time: 'منذ يوم',
                unread: false,
                selected: false
            },
            {
                id: 7,
                type: 'like',
                author: 'خالد محمود',
                avatar: 'images/Hussein Nihad.png',
                action: 'أعجب بمنشورك',
                text: 'مع 8 أشخاص آخرين',
                time: 'منذ يومين',
                unread: false,
                selected: false
            },
            {
                id: 8,
                type: 'offer',
                author: 'متجر الإلكترونيات',
                avatar: null,
                action: 'عرض جديد',
                text: 'خصم 30% على جميع الأجهزة الذكية',
                time: 'منذ 3 أيام',
                unread: false,
                selected: false
            },
            {
                id: 9,
                type: 'comment',
                author: 'نور أحمد',
                avatar: 'images/Hussein Nihad.png',
                action: 'علقت على منشورك',
                text: 'هل المنتج متوفر بألوان أخرى؟',
                time: 'منذ أسبوع',
                unread: false,
                selected: false
            },
            {
                id: 10,
                type: 'system',
                author: 'Get Me',
                avatar: null,
                action: 'إشعار النظام',
                text: 'تم تحديث سياسة الخصوصية',
                time: 'منذ أسبوعين',
                unread: false,
                selected: false
            }
        ];

        let showingOldNotifications = false;

        // عرض الإشعارات
        function showNotifications() {
            const modal = document.getElementById('notificationsModal');
            renderNotifications();
            modal.classList.add('show');
        }

        // رسم الإشعارات
        function renderNotifications() {
            const notificationsList = document.getElementById('notificationsList');
            let allNotifications = [...notifications];

            // إضافة الإشعارات السابقة إذا كانت مفعلة
            if (showingOldNotifications) {
                allNotifications = [...notifications, ...oldNotifications];
            }

            let filteredNotifications = allNotifications;

            if (currentTab === 'unread') {
                filteredNotifications = allNotifications.filter(n => n.unread);
            }

            notificationsList.innerHTML = filteredNotifications.map(notification =>
                '<div class="notification-item' + (notification.unread ? ' unread' : '') + '">' +
                    '<div class="notification-checkbox' + (notification.selected ? ' selected' : '') + '" onclick="toggleNotificationSelection(' + notification.id + ')"></div>' +
                    '<div class="notification-avatar' + (notification.avatar ? '' : ' ' + notification.type) + '">' +
                        (notification.avatar ?
                            '<img src="' + notification.avatar + '" alt="' + notification.author + '">' :
                            (notification.type === 'offer' ? '🎯' : '📱')
                        ) +
                    '</div>' +
                    '<div class="notification-content" onclick="markAsRead(' + notification.id + ')">' +
                        '<div class="notification-header">' +
                            '<span class="notification-author">' + notification.author + '</span>' +
                            '<span class="notification-action">' + notification.action + '</span>' +
                        '</div>' +
                        '<div class="notification-text">' + notification.text + '</div>' +
                        '<div class="notification-time">' + notification.time + '</div>' +
                    '</div>' +
                '</div>'
            ).join('');

            updateDeleteButton();
            updateShowOldButton();
        }

        // إغلاق نافذة الإشعارات
        function closeNotifications() {
            document.getElementById('notificationsModal').classList.remove('show');
        }

        // تعيين إشعار كمقروء
        function markAsRead(notificationId) {
            const notification = notifications.find(n => n.id === notificationId);
            if (notification) {
                notification.unread = false;
                updateNotificationBadge();
                showNotifications(); // إعادة تحديث القائمة
            }
        }

        // تعيين جميع الإشعارات كمقروءة
        function markAllAsRead() {
            notifications.forEach(notification => {
                notification.unread = false;
            });
            updateNotificationBadge();
            showNotifications(); // إعادة تحديث القائمة
        }

        // مسح جميع الإشعارات
        function clearAllNotifications() {
            if (confirm('هل أنت متأكد من مسح جميع الإشعارات؟')) {
                notifications = [];
                updateNotificationBadge();
                closeNotifications();
            }
        }

        // تحديث مؤشر الإشعارات
        function updateNotificationBadge() {
            const unreadCount = notifications.filter(n => n.unread).length;
            const badge = document.querySelector('.notification-badge');

            if (unreadCount > 0) {
                badge.textContent = unreadCount;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }

        // إغلاق قوائم التعليقات عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.comment-menu')) {
                document.querySelectorAll('.comment-dropdown').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });

        // تبديل التبويب
        function switchTab(tab) {
            currentTab = tab;

            // تحديث التبويبات
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            renderNotifications();
        }

        // تبديل قائمة الإعدادات
        function toggleNotificationsMenu() {
            const dropdown = document.getElementById('notificationsDropdown');
            dropdown.classList.toggle('show');
        }

        // تبديل تحديد الإشعار
        function toggleNotificationSelection(notificationId) {
            let notification = notifications.find(n => n.id === notificationId);
            if (!notification) {
                notification = oldNotifications.find(n => n.id === notificationId);
            }
            if (notification) {
                notification.selected = !notification.selected;
                renderNotifications();
            }
        }

        // تحديث زر الحذف
        function updateDeleteButton() {
            const deleteBtn = document.getElementById('deleteSelectedBtn');
            const selectedCount = notifications.filter(n => n.selected).length +
                                 oldNotifications.filter(n => n.selected).length;

            if (selectedCount > 0) {
                deleteBtn.classList.add('show');
                deleteBtn.textContent = 'مسح المحدد (' + selectedCount + ')';
            } else {
                deleteBtn.classList.remove('show');
            }
        }

        // حذف الإشعارات المحددة
        function deleteSelectedNotifications() {
            if (confirm('هل أنت متأكد من حذف الإشعارات المحددة؟')) {
                notifications = notifications.filter(n => !n.selected);
                oldNotifications = oldNotifications.filter(n => !n.selected);
                updateNotificationBadge();
                renderNotifications();
            }
        }

        // إعدادات الإشعارات
        function openNotificationSettings() {
            alert('إعدادات الإشعارات:\n\n• تشغيل/إيقاف الإشعارات\n• أنواع الإشعارات\n• أوقات الإشعارات\n• طريقة التنبيه');
            toggleNotificationsMenu();
        }

        // فتح نافذة الإشعارات
        function openNotificationsWindow() {
            alert('سيتم فتح نافذة الإشعارات في تبويب جديد');
            toggleNotificationsMenu();
        }

        // إظهار الإشعارات القديمة
        function showOldNotifications() {
            showingOldNotifications = !showingOldNotifications;
            renderNotifications();
        }

        // تحديث زر الإشعارات السابقة
        function updateShowOldButton() {
            const showOldBtn = document.querySelector('.show-old-notifications-btn');
            if (showingOldNotifications) {
                showOldBtn.textContent = 'إخفاء الإشعارات القديمة';
            } else {
                showOldBtn.textContent = 'إظهار الإشعارات القديمة';
            }
        }

        // تحديث مؤشر الإشعارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateNotificationBadge();
        });

        // بيانات المحادثات
        let conversations = [
            {
                id: 1,
                name: 'أحمد محمد',
                avatar: 'images/Hussein Nihad.png',
                lastMessage: 'هل المنتج متوفر؟',
                time: 'منذ 5 دقائق',
                unread: 2,
                status: 'متصل الآن',
                messages: [
                    { id: 1, text: 'السلام عليكم', time: '14:20', sent: false },
                    { id: 2, text: 'وعليكم السلام ورحمة الله', time: '14:21', sent: true },
                    { id: 3, text: 'هل المنتج متوفر؟', time: '14:25', sent: false },
                    { id: 4, text: 'نعم متوفر، كم تريد؟', time: '14:26', sent: true }
                ]
            },
            {
                id: 2,
                name: 'سارة علي',
                avatar: 'images/مستحظرات تجميل.jpg',
                lastMessage: 'شكراً لك على الخدمة الممتازة',
                time: 'منذ 15 دقيقة',
                unread: 0,
                status: 'متصلة منذ 10 دقائق',
                messages: [
                    { id: 1, text: 'تم استلام الطلب بنجاح', time: '13:45', sent: false },
                    { id: 2, text: 'الحمد لله، أتمنى أن يعجبك', time: '13:46', sent: true },
                    { id: 3, text: 'شكراً لك على الخدمة الممتازة', time: '13:50', sent: false }
                ]
            },
            {
                id: 3,
                name: 'متجر الإلكترونيات',
                avatar: 'images/الصناعة.jpg',
                lastMessage: 'عرض خاص لك - خصم 25%',
                time: 'منذ ساعة',
                unread: 1,
                status: 'متصل الآن',
                messages: [
                    { id: 1, text: 'عرض خاص لك - خصم 25%', time: '12:30', sent: false },
                    { id: 2, text: 'على أي المنتجات؟', time: '12:35', sent: true },
                    { id: 3, text: 'جميع الأجهزة الذكية', time: '12:36', sent: false }
                ]
            },
            {
                id: 4,
                name: 'دكتور محمد الطبيب',
                avatar: 'images/الطبية.jpg',
                lastMessage: 'موعد الفحص غداً',
                time: 'منذ 4 ساعات',
                unread: 0,
                status: 'متصل منذ 5 دقائق',
                messages: [
                    { id: 1, text: 'أريد حجز موعد', time: '10:15', sent: true },
                    { id: 2, text: 'متى تريد الموعد؟', time: '10:16', sent: false },
                    { id: 3, text: 'موعد الفحص غداً', time: '10:17', sent: false }
                ]
            },
            {
                id: 5,
                name: 'معرض السيارات',
                avatar: 'images/سيارات.jpg',
                lastMessage: 'السيارة جاهزة للتسليم',
                time: 'منذ 6 ساعات',
                unread: 1,
                status: 'متصل الآن',
                messages: [
                    { id: 1, text: 'متى ستكون السيارة جاهزة؟', time: '08:30', sent: true },
                    { id: 2, text: 'السيارة جاهزة للتسليم', time: '08:45', sent: false }
                ]
            },
            {
                id: 6,
                name: 'مطعم الأصالة',
                avatar: 'images/مطعم.jpg',
                lastMessage: 'طاولة محجوزة لك',
                time: 'منذ 8 ساعات',
                unread: 0,
                status: 'متصل الآن',
                messages: [
                    { id: 1, text: 'أريد حجز طاولة لشخصين', time: '06:20', sent: true },
                    { id: 2, text: 'طاولة محجوزة لك', time: '06:25', sent: false }
                ]
            },
            {
                id: 7,
                name: 'بنك الأمان',
                avatar: 'images/مصارف وبنوك.jpg',
                lastMessage: 'تم تفعيل حسابك',
                time: 'منذ يوم',
                unread: 0,
                status: 'متصل منذ 15 دقيقة',
                messages: [
                    { id: 1, text: 'أريد فتح حساب جديد', time: 'أمس 15:30', sent: true },
                    { id: 2, text: 'تم تفعيل حسابك', time: 'أمس 16:00', sent: false }
                ]
            },
            {
                id: 8,
                name: 'مزرعة الخير',
                avatar: 'images/زراعية.jpg',
                lastMessage: 'الخضروات طازجة اليوم',
                time: 'منذ يومين',
                unread: 0,
                status: 'متصل منذ 30 دقيقة',
                messages: [
                    { id: 1, text: 'هل الخضروات طازجة؟', time: 'أمس 12:00', sent: true },
                    { id: 2, text: 'الخضروات طازجة اليوم', time: 'أمس 12:15', sent: false }
                ]
            }
        ];

        let currentChatId = null;
        let currentContentTab = 'posts'; // posts أو media

        // بيانات المستخدمين مع منشوراتهم المفصلة
        const usersData = {
            1: {
                name: 'أحمد محمد',
                avatar: 'images/Hussein Nihad.png',
                status: 'متصل الآن',
                followers: '1.2K',
                following: '340',
                posts: '18',
                rating: 4.8,
                reviewsCount: 156,
                isFollowing: false,
                bio: 'بائع في مجال الإلكترونيات والهواتف الذكية. خبرة أكثر من 5 سنوات في السوق.',
                userPosts: [
                    {
                        id: 1,
                        content: 'مطعم فاخر يقدم أشهى الأطباق العربية والعالمية. جودة عالية وخدمة ممتازة. احجز طاولتك الآن!',
                        media: [
                            { type: 'image', url: 'images/مطعم.jpg' },
                            { type: 'image', url: 'images/كافيهات.jpg' },
                            { type: 'video', url: 'images/مطعم.jpg', thumbnail: 'images/مطعم.jpg' }
                        ],
                        time: 'منذ ساعتين',
                        category: 'مطاعم',
                        likes: 45,
                        comments: 12,
                        shares: 3,
                        price: '15-50 دولار',
                        isLiked: false,
                        isCommented: false,
                        isShared: false
                    },
                    {
                        id: 2,
                        content: 'عقارات فاخرة للبيع والإيجار في أفضل المواقع. شقق وفيلل بمواصفات عالية وأسعار مناسبة.',
                        media: [
                            { type: 'image', url: 'images/عقارات.jpg' },
                            { type: 'image', url: 'images/محلات.jpg' }
                        ],
                        time: 'منذ 5 ساعات',
                        category: 'عقارات',
                        likes: 78,
                        comments: 23,
                        shares: 15,
                        price: '50,000-200,000 دولار',
                        isLiked: true,
                        isCommented: false,
                        isShared: false
                    },
                    {
                        id: 3,
                        content: 'سيارات فاخرة للبيع والإيجار. أحدث الموديلات بأفضل الأسعار. صيانة دورية وضمان شامل.',
                        media: [
                            { type: 'image', url: 'images/سيارات.jpg' },
                            { type: 'image', url: 'images/توصيل.jpg' },
                            { type: 'video', url: 'images/سيارات.jpg', thumbnail: 'images/سيارات.jpg' }
                        ],
                        time: 'منذ يوم',
                        category: 'سيارات',
                        likes: 32,
                        comments: 8,
                        shares: 5,
                        price: '15,000-80,000 دولار',
                        isLiked: false,
                        isCommented: true,
                        isShared: false
                    },
                    {
                        id: 4,
                        content: 'خدمات طبية متميزة مع أفضل الأطباء والمختصين. عيادات حديثة ومعدات متطورة.',
                        media: [
                            { type: 'image', url: 'images/الطبية.jpg' },
                            { type: 'image', url: 'images/طبية.jpg' }
                        ],
                        time: 'منذ يومين',
                        category: 'طبية',
                        likes: 67,
                        comments: 19,
                        shares: 11,
                        price: '20-100 دولار',
                        isLiked: false,
                        isCommented: false,
                        isShared: true
                    },
                    {
                        id: 5,
                        content: 'مجوهرات ذهبية فاخرة بأجود الخامات. تصاميم عصرية وكلاسيكية. أسعار مناسبة وجودة مضمونة.',
                        media: [
                            { type: 'image', url: 'images/ذهب.jpg' },
                            { type: 'image', url: 'images/مستحظرات تجميل.jpg' }
                        ],
                        time: 'منذ 3 أيام',
                        category: 'مجوهرات',
                        likes: 89,
                        comments: 25,
                        shares: 18,
                        price: '200-5000 دولار',
                        isLiked: false,
                        isCommented: false,
                        isShared: true
                    },
                    {
                        id: 6,
                        content: 'خدمات مصرفية متكاملة مع أفضل البنوك. حسابات توفير، قروض، وخدمات استثمارية.',
                        media: [
                            { type: 'image', url: 'images/مصارف وبنوك.jpg' },
                            { type: 'image', url: 'images/الصيرفة.jpg' }
                        ],
                        time: 'منذ 4 أيام',
                        category: 'مصارف',
                        likes: 56,
                        comments: 14,
                        shares: 9,
                        price: 'خدمات مجانية',
                        isLiked: true,
                        isCommented: false,
                        isShared: false
                    },
                    {
                        id: 7,
                        content: 'كاميرات مراقبة ذكية للمنزل والمكتب. رؤية ليلية واضحة، تسجيل عالي الدقة، وتطبيق للمراقبة عن بُعد.',
                        media: [
                            { type: 'image', url: 'https://via.placeholder.com/400x300/16a085/ffffff?text=كاميرات+مراقبة' },
                            { type: 'image', url: 'https://via.placeholder.com/400x300/138d75/ffffff?text=رؤية+ليلية' },
                            { type: 'video', url: 'https://via.placeholder.com/400x300/117a65/ffffff?text=Demo+Video', thumbnail: 'https://via.placeholder.com/400x300/117a65/ffffff?text=Demo+Video' }
                        ],
                        time: 'منذ 5 أيام',
                        category: 'أمان',
                        likes: 43,
                        comments: 11,
                        shares: 7,
                        price: '150-300 دولار'
                    },
                    {
                        id: 8,
                        content: 'أجهزة تابلت iPad Air الجديدة مع شاشة Liquid Retina ومعالج M2. مثالية للعمل والإبداع والترفيه.',
                        media: [
                            { type: 'image', url: 'https://via.placeholder.com/400x300/d35400/ffffff?text=iPad+Air+M2' }
                        ],
                        time: 'منذ أسبوع',
                        category: 'تابلت',
                        likes: 72,
                        comments: 20,
                        shares: 13,
                        price: '650 دولار'
                    }
                ]
            },
            2: {
                name: 'سارة علي',
                avatar: 'images/مستحظرات تجميل.jpg',
                status: 'متصلة منذ 10 دقائق',
                followers: '890',
                following: '156',
                posts: '12',
                rating: 4.6,
                reviewsCount: 89,
                isFollowing: false,
                bio: 'خبيرة تجميل ومستحضرات العناية',
                userPosts: [
                    {
                        id: 1,
                        content: 'مستحضرات تجميل فاخرة بأجود الخامات. منتجات طبيعية آمنة للبشرة الحساسة.',
                        media: [
                            { type: 'image', url: 'images/مستحظرات تجميل.jpg' },
                            { type: 'image', url: 'images/ذهب.jpg' },
                            { type: 'video', url: 'images/مستحظرات تجميل.jpg', thumbnail: 'images/مستحظرات تجميل.jpg' }
                        ],
                        time: 'منذ 3 ساعات',
                        category: 'تجميل',
                        likes: 67,
                        comments: 18,
                        shares: 9,
                        price: '15-80 دولار',
                        isLiked: false,
                        isCommented: false,
                        isShared: false
                    },
                    {
                        id: 2,
                        content: 'نصائح للعناية بالبشرة في فصل الصيف. استخدمي واقي الشمس وكريمات الترطيب المناسبة لنوع بشرتك.',
                        media: [
                            { type: 'video', url: 'https://via.placeholder.com/400x300/e91e63/ffffff?text=نصائح+العناية', thumbnail: 'https://via.placeholder.com/400x300/e91e63/ffffff?text=العناية+بالبشرة' }
                        ],
                        time: 'منذ يومين',
                        category: 'جمال',
                        likes: 124,
                        comments: 34,
                        shares: 22,
                        price: 'نصائح مجانية'
                    },
                    {
                        id: 3,
                        content: 'مكياج طبيعي لإطلالة يومية رائعة. خطوات بسيطة لمظهر منعش وجذاب.',
                        media: [
                            { type: 'image', url: 'https://via.placeholder.com/400x300/9b59b6/ffffff?text=مكياج+طبيعي' },
                            { type: 'video', url: 'https://via.placeholder.com/400x300/8e44ad/ffffff?text=تطبيق+المكياج', thumbnail: 'https://via.placeholder.com/400x300/8e44ad/ffffff?text=تطبيق+المكياج' }
                        ],
                        time: 'منذ 3 أيام',
                        category: 'جمال',
                        likes: 89,
                        comments: 26,
                        shares: 15,
                        price: 'درس مجاني'
                    },
                    {
                        id: 4,
                        content: 'حقائب يد عصرية بتصاميم أنيقة. مناسبة للعمل والمناسبات الخاصة.',
                        media: [
                            { type: 'image', url: 'https://via.placeholder.com/400x300/2c3e50/ffffff?text=حقائب+يد' },
                            { type: 'image', url: 'https://via.placeholder.com/400x300/34495e/ffffff?text=حقيبة+سوداء' }
                        ],
                        time: 'منذ 4 أيام',
                        category: 'إكسسوارات',
                        likes: 45,
                        comments: 12,
                        shares: 8,
                        price: '80-200 دولار'
                    }
                ]
            },
            3: {
                name: 'متجر الإلكترونيات',
                avatar: 'images/الصناعة.jpg',
                status: 'متصل الآن',
                followers: '5.2K',
                following: '89',
                posts: '25',
                rating: 4.9,
                reviewsCount: 324,
                isFollowing: true,
                bio: 'متجر متخصص في المنتجات الصناعية والتقنية',
                userPosts: [
                    {
                        id: 1,
                        content: 'منتجات صناعية عالية الجودة للمصانع والشركات. معدات متطورة وقطع غيار أصلية.',
                        media: [
                            { type: 'image', url: 'images/الصناعة.jpg' },
                            { type: 'image', url: 'images/استيراد وتصدير.jpg' }
                        ],
                        time: 'منذ ساعة',
                        category: 'صناعة',
                        likes: 156,
                        comments: 45,
                        shares: 28,
                        price: '500-5000 دولار',
                        isLiked: true,
                        isCommented: false,
                        isShared: false
                    },
                    {
                        id: 2,
                        content: 'خدمات استيراد وتصدير للشركات. نقل البضائع بأمان وسرعة عالية.',
                        media: [
                            { type: 'image', url: 'images/استيراد وتصدير.jpg' },
                            { type: 'video', url: 'images/استيراد وتصدير.jpg', thumbnail: 'images/استيراد وتصدير.jpg' }
                        ],
                        time: 'منذ 4 ساعات',
                        category: 'تجارة',
                        likes: 89,
                        comments: 21,
                        shares: 12,
                        price: 'حسب الطلب',
                        isLiked: false,
                        isCommented: true,
                        isShared: false
                    }
                ]
            },
            4: {
                name: 'دكتور محمد الطبيب',
                avatar: 'images/الطبية.jpg',
                status: 'متصل منذ 5 دقائق',
                followers: '2.8K',
                following: '120',
                posts: '45',
                rating: 4.9,
                reviewsCount: 280,
                isFollowing: false,
                bio: 'طبيب متخصص في الطب العام والعيادات الطبية',
                userPosts: [
                    {
                        id: 1,
                        content: 'عيادة طبية متكاملة مع أحدث الأجهزة الطبية. فحوصات شاملة وعلاج متخصص.',
                        media: [
                            { type: 'image', url: 'images/الطبية.jpg' },
                            { type: 'image', url: 'images/طبية.jpg' }
                        ],
                        time: 'منذ ساعتين',
                        category: 'طب',
                        likes: 234,
                        comments: 67,
                        shares: 45,
                        price: '25-100 دولار',
                        isLiked: false,
                        isCommented: false,
                        isShared: false
                    }
                ]
            },
            5: {
                name: 'معرض السيارات',
                avatar: 'images/سيارات.jpg',
                status: 'متصل الآن',
                followers: '3.5K',
                following: '200',
                posts: '38',
                rating: 4.7,
                reviewsCount: 195,
                isFollowing: false,
                bio: 'معرض سيارات فاخرة وخدمات توصيل',
                userPosts: [
                    {
                        id: 1,
                        content: 'سيارات فاخرة للبيع والإيجار. أحدث الموديلات بأفضل الأسعار وضمان شامل.',
                        media: [
                            { type: 'image', url: 'images/سيارات.jpg' },
                            { type: 'image', url: 'images/توصيل.jpg' },
                            { type: 'video', url: 'images/سيارات.jpg', thumbnail: 'images/سيارات.jpg' }
                        ],
                        time: 'منذ 3 ساعات',
                        category: 'سيارات',
                        likes: 189,
                        comments: 52,
                        shares: 31,
                        price: '15,000-80,000 دولار',
                        isLiked: true,
                        isCommented: false,
                        isShared: true
                    }
                ]
            },
            6: {
                name: 'مطعم الأصالة',
                avatar: 'images/مطعم.jpg',
                status: 'متصل الآن',
                followers: '1.8K',
                following: '95',
                posts: '28',
                rating: 4.8,
                reviewsCount: 156,
                isFollowing: false,
                bio: 'مطعم يقدم أشهى الأطباق العربية والعالمية',
                userPosts: [
                    {
                        id: 1,
                        content: 'أطباق شهية من المطبخ العربي الأصيل. طعم لا يُنسى في أجواء رائعة.',
                        media: [
                            { type: 'image', url: 'images/مطعم.jpg' },
                            { type: 'image', url: 'images/كافيهات.jpg' }
                        ],
                        time: 'منذ ساعة',
                        category: 'مطاعم',
                        likes: 145,
                        comments: 38,
                        shares: 22,
                        price: '10-35 دولار',
                        isLiked: false,
                        isCommented: false,
                        isShared: false
                    }
                ]
            },
            7: {
                name: 'بنك الأمان',
                avatar: 'images/مصارف وبنوك.jpg',
                status: 'متصل منذ 15 دقيقة',
                followers: '4.2K',
                following: '50',
                posts: '22',
                rating: 4.9,
                reviewsCount: 312,
                isFollowing: true,
                bio: 'خدمات مصرفية متكاملة وحلول مالية مبتكرة',
                userPosts: [
                    {
                        id: 1,
                        content: 'خدمات مصرفية متطورة مع أفضل أسعار الفوائد. حسابات توفير وقروض بشروط مرنة.',
                        media: [
                            { type: 'image', url: 'images/مصارف وبنوك.jpg' },
                            { type: 'image', url: 'images/الصيرفة.jpg' }
                        ],
                        time: 'منذ 4 ساعات',
                        category: 'مصارف',
                        likes: 98,
                        comments: 25,
                        shares: 15,
                        price: 'خدمات مجانية',
                        isLiked: false,
                        isCommented: false,
                        isShared: false
                    }
                ]
            },
            8: {
                name: 'مزرعة الخير',
                avatar: 'images/زراعية.jpg',
                status: 'متصل منذ 30 دقيقة',
                followers: '1.2K',
                following: '180',
                posts: '35',
                rating: 4.6,
                reviewsCount: 89,
                isFollowing: false,
                bio: 'منتجات زراعية طازجة من المزرعة مباشرة',
                userPosts: [
                    {
                        id: 1,
                        content: 'خضروات وفواكه طازجة عضوية من المزرعة. جودة عالية وأسعار المزارع مباشرة.',
                        media: [
                            { type: 'image', url: 'images/زراعية.jpg' },
                            { type: 'image', url: 'images/السوبرماركت.jpg' },
                            { type: 'video', url: 'images/زراعية.jpg', thumbnail: 'images/زراعية.jpg' }
                        ],
                        time: 'منذ 6 ساعات',
                        category: 'زراعة',
                        likes: 76,
                        comments: 19,
                        shares: 12,
                        price: '2-15 دولار',
                        isLiked: false,
                        isCommented: true,
                        isShared: false
                    }
                ]
            }
        };

        // عرض قائمة الرسائل
        function showMessages() {
            const modal = document.getElementById('messagesModal');
            renderMessagesList();
            modal.classList.add('show');
        }



        // رسم قائمة المحادثات
        function renderMessagesList() {
            const messagesList = document.getElementById('messagesList');
            messagesList.innerHTML = conversations.map(conv =>
                '<div class="message-item' + (conv.unread > 0 ? ' unread' : '') + '" onclick="openChat(' + conv.id + ')">' +
                    '<div class="message-avatar" onclick="event.stopPropagation(); openUserProfile(\'' + conv.id + '\')" style="cursor: pointer;">' +
                        '<img src="' + conv.avatar + '" alt="' + conv.name + '">' +
                    '</div>' +
                    '<div class="message-content">' +
                        '<div class="message-header">' +
                            '<div class="message-name" onclick="event.stopPropagation(); openUserProfile(\'' + conv.id + '\')" style="cursor: pointer;">' + conv.name + '</div>' +
                            '<div class="message-time">' + conv.time + '</div>' +
                        '</div>' +
                        '<div class="message-preview' + (conv.unread > 0 ? ' unread' : '') + '">' + conv.lastMessage + '</div>' +
                    '</div>' +
                    (conv.unread > 0 ? '<div class="message-badge">' + conv.unread + '</div>' : '') +
                '</div>'
            ).join('');
        }

        // إغلاق قائمة الرسائل
        function closeMessages() {
            document.getElementById('messagesModal').classList.remove('show');
        }

        // فتح محادثة
        function openChat(conversationId) {
            currentChatId = conversationId;
            const conversation = conversations.find(c => c.id === conversationId);

            if (conversation) {
                // تحديث معلومات المستخدم
                document.getElementById('chatUserName').textContent = conversation.name;
                document.getElementById('chatUserStatus').textContent = conversation.status;
                document.getElementById('chatUserAvatar').src = conversation.avatar;
                document.getElementById('chatUserAvatar').alt = conversation.name;

                // رسم الرسائل
                renderChatMessages(conversation.messages);

                // إخفاء قائمة الرسائل وإظهار المحادثة
                document.getElementById('messagesModal').classList.remove('show');
                document.getElementById('chatModal').classList.add('show');

                // تعيين المحادثة كمقروءة
                conversation.unread = 0;
                updateMessagesBadge();
            }
        }

        // رسم رسائل المحادثة
        function renderChatMessages(messages) {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = messages.map(message =>
                '<div class="chat-message ' + (message.sent ? 'sent' : 'received') + '">' +
                    (message.sent ? '' :
                        '<div class="chat-message-avatar">' +
                            '<img src="' + conversations.find(c => c.id === currentChatId).avatar + '" alt="">' +
                        '</div>'
                    ) +
                    '<div class="chat-message-bubble">' +
                        '<div class="chat-message-text">' + message.text + '</div>' +
                        '<div class="chat-message-time">' + message.time + '</div>' +
                    '</div>' +
                '</div>'
            ).join('');

            // التمرير لأسفل
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // العودة لقائمة الرسائل
        function backToMessages() {
            document.getElementById('chatModal').classList.remove('show');
            document.getElementById('messagesModal').classList.add('show');
        }

        // إغلاق المحادثة
        function closeChat() {
            document.getElementById('chatModal').classList.remove('show');
        }

        // إرسال رسالة
        function sendMessage() {
            const inputField = document.getElementById('chatInputField');
            const messageText = inputField.value.trim();

            if (messageText && currentChatId) {
                const conversation = conversations.find(c => c.id === currentChatId);
                const currentTime = new Date().toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                });

                // إضافة الرسالة الجديدة
                const newMessage = {
                    id: conversation.messages.length + 1,
                    text: messageText,
                    time: currentTime,
                    sent: true
                };

                conversation.messages.push(newMessage);
                conversation.lastMessage = messageText;
                conversation.time = 'الآن';

                // إعادة رسم الرسائل
                renderChatMessages(conversation.messages);

                // مسح حقل الإدخال
                inputField.value = '';

                // محاكاة رد تلقائي بعد ثانيتين
                setTimeout(() => {
                    const autoReply = {
                        id: conversation.messages.length + 1,
                        text: 'شكراً لك، سأرد عليك قريباً',
                        time: new Date().toLocaleTimeString('ar-SA', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                        }),
                        sent: false
                    };

                    conversation.messages.push(autoReply);
                    conversation.lastMessage = autoReply.text;
                    renderChatMessages(conversation.messages);
                }, 2000);
            }
        }

        // التعامل مع ضغط Enter
        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // تحديث مؤشر الرسائل
        function updateMessagesBadge() {
            const totalUnread = conversations.reduce((sum, conv) => sum + conv.unread, 0);
            const badge = document.querySelector('.nav-item.has-notification .notification-badge');

            if (totalUnread > 0) {
                badge.textContent = totalUnread;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }

        // تبديل قائمة إعدادات الرسائل
        function toggleMessagesMenu() {
            const dropdown = document.getElementById('messagesDropdown');
            dropdown.classList.toggle('show');
        }

        // وظائف قائمة النقاط الثلاث
        function toggleMessageSounds() {
            alert('تم تشغيل/إيقاف أصوات الرسائل');
            toggleMessagesMenu();
        }

        function toggleCallSounds() {
            alert('تم تشغيل/إيقاف أصوات المكالمات');
            toggleMessagesMenu();
        }

        function toggleActiveStatus() {
            alert('تم تشغيل/إيقاف حالة النشاط');
            toggleMessagesMenu();
        }

        function showRestrictedAccounts() {
            alert('الحسابات المقيدة:\n\n• لا توجد حسابات مقيدة حالياً');
            toggleMessagesMenu();
        }

        function showBlockedAccounts() {
            alert('الحسابات المحظورة:\n\n• لا توجد حسابات محظورة حالياً');
            toggleMessagesMenu();
        }

        function showPrivacySettings() {
            alert('إعدادات خصوصية الرسائل:\n\n• من يمكنه مراسلتك\n• إعدادات الظهور\n• إعدادات القراءة');
            toggleMessagesMenu();
        }

        // تكبير نافذة الرسائل
        function expandMessages() {
            document.getElementById('messagesModal').classList.remove('show');
            document.getElementById('messagesFullscreen').classList.add('show');
            renderMessagesListFullscreen();
        }

        // إغلاق النافذة المكبرة
        function closeFullscreenMessages() {
            document.getElementById('messagesFullscreen').classList.remove('show');
        }

        // إضافة بيانات البائعين
        conversations.push({
            id: 5,
            name: 'متجر الأزياء الحديثة',
            avatar: 'images/Hussein Nihad.png',
            lastMessage: 'مرحباً، كيف يمكنني مساعدتك؟',
            time: 'منذ يوم',
            unread: 0,
            status: 'متصل الآن',
            messages: [
                { id: 1, text: 'مرحباً، كيف يمكنني مساعدتك؟', time: '10:00', sent: false }
            ]
        });

        conversations.push({
            id: 6,
            name: 'محل الإلكترونيات المتقدم',
            avatar: 'images/Hussein Nihad.png',
            lastMessage: 'لدينا عروض خاصة اليوم',
            time: 'منذ 3 ساعات',
            unread: 1,
            status: 'متصل منذ ساعة',
            messages: [
                { id: 1, text: 'لدينا عروض خاصة اليوم', time: '15:30', sent: false }
            ]
        });

        // البحث في الرسائل
        function searchMessages(query) {
            // تطبيق البحث في النافذة الصغيرة
            renderMessagesList(query);
        }

        // تنفيذ البحث عند النقر على الأيقونة
        function performSearch() {
            const searchInput = document.getElementById('messagesSearchInput');
            const query = searchInput.value.trim();
            searchMessages(query);
        }

        function searchMessagesFullscreen(query) {
            // تطبيق البحث في النافذة المكبرة
            renderMessagesListFullscreen(query);
        }

        // تنفيذ البحث في النافذة المكبرة عند النقر على الأيقونة
        function performSearchFullscreen() {
            const searchInput = document.getElementById('messagesSearchInputFullscreen');
            const query = searchInput.value.trim();
            searchMessagesFullscreen(query);
        }

        // متغيرات النافذة المكبرة
        let selectionMode = false;
        let selectedMessages = [];

        // تبديل قائمة النافذة المكبرة
        function toggleFullscreenMessagesMenu() {
            const dropdown = document.getElementById('messagesFullscreenDropdown');
            dropdown.classList.toggle('show');
        }

        // تبديل وضع التحديد
        function toggleSelectionMode() {
            selectionMode = !selectionMode;
            const messagesList = document.getElementById('messagesListFullscreen');

            if (selectionMode) {
                messagesList.classList.add('messages-selection-mode');
            } else {
                messagesList.classList.remove('messages-selection-mode');
                selectedMessages = [];
            }

            renderMessagesListFullscreen();
            toggleFullscreenMessagesMenu();
        }

        // حذف جميع الرسائل
        function deleteAllMessages() {
            if (confirm('هل أنت متأكد من حذف جميع الرسائل؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                conversations = [];
                updateMessagesBadge();
                renderMessagesListFullscreen();
                document.getElementById('messagesChatArea').innerHTML =
                    '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666; font-size: 16px;">لا توجد رسائل</div>';
            }
            toggleFullscreenMessagesMenu();
        }

        // عرض البائعين
        function showSellers() {
            const sellers = conversations.filter(c => c.name.includes('متجر') || c.name.includes('محل'));
            if (sellers.length > 0) {
                let sellersText = 'البائعين:\n\n';
                sellers.forEach(seller => {
                    sellersText += `• ${seller.name}\n`;
                });
                alert(sellersText);
            } else {
                alert('لا توجد محادثات مع بائعين حالياً');
            }
            toggleFullscreenMessagesMenu();
        }

        // عرض الرسائل السريعة
        function showQuickReplies() {
            const modal = document.getElementById('quickRepliesModal');
            modal.classList.add('show');

            // إخفاء النوافذ الأخرى
            document.getElementById('attachmentOptions').classList.remove('show');
        }

        // إرسال رسالة سريعة
        function sendQuickReply(message) {
            if (currentChatId) {
                const conversation = conversations.find(c => c.id === currentChatId);
                const currentTime = new Date().toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                });

                const newMessage = {
                    id: conversation.messages.length + 1,
                    text: message,
                    time: currentTime,
                    sent: true
                };

                conversation.messages.push(newMessage);
                conversation.lastMessage = message;
                conversation.time = 'الآن';

                // إعادة رسم الرسائل
                const chatMessages = document.getElementById('chatMessagesFullscreen');
                if (chatMessages) {
                    chatMessages.innerHTML = renderChatMessagesFullscreen(conversation.messages);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }

                renderMessagesListFullscreen();
            }

            document.getElementById('quickRepliesModal').classList.remove('show');
        }

        // عرض خيارات المرفقات
        function showAttachmentOptions() {
            const modal = document.getElementById('attachmentOptions');
            modal.classList.add('show');
        }

        // وظائف المرفقات المحسنة
        function attachImage() {
            if (confirm('هل تريد إرفاق صورة؟')) {
                sendQuickReply('📷 تم إرفاق صورة');
            }
            document.getElementById('attachmentOptions').classList.remove('show');
        }

        function attachVideo() {
            if (confirm('هل تريد إرفاق فيديو؟')) {
                sendQuickReply('🎥 تم إرفاق فيديو');
            }
            document.getElementById('attachmentOptions').classList.remove('show');
        }

        function attachFile() {
            if (confirm('هل تريد إرفاق ملف؟')) {
                sendQuickReply('📄 تم إرفاق ملف');
            }
            document.getElementById('attachmentOptions').classList.remove('show');
        }

        function attachAudio() {
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                startAudioRecording();
            } else {
                alert('التسجيل الصوتي غير مدعوم في هذا المتصفح');
            }
            document.getElementById('attachmentOptions').classList.remove('show');
        }

        function attachLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    sendQuickReply(`📍 الموقع: https://maps.google.com/?q=${lat},${lng}`);
                }, function() {
                    alert('لا يمكن الحصول على الموقع');
                });
            } else {
                alert('الموقع غير مدعوم في هذا المتصفح');
            }
            document.getElementById('attachmentOptions').classList.remove('show');
        }

        function attachContact() {
            const contact = prompt('أدخل معلومات جهة الاتصال (الاسم ورقم الهاتف):');
            if (contact) {
                sendQuickReply(`👤 جهة اتصال: ${contact}`);
            }
            document.getElementById('attachmentOptions').classList.remove('show');
        }

        // معالجة رفع الملفات
        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const fileSize = (file.size / 1024 / 1024).toFixed(2); // بالميجابايت
                sendQuickReply(`📄 تم إرفاق ملف: ${file.name} (${fileSize} MB)`);
            }
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                sendQuickReply(`📷 تم إرفاق صورة: ${file.name}`);
            }
        }

        function handleVideoUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                sendQuickReply(`🎥 تم إرفاق فيديو: ${file.name} (${fileSize} MB)`);
            }
        }

        // التسجيل الصوتي
        let mediaRecorder;
        let audioChunks = [];

        function startAudioRecording() {
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(stream => {
                    mediaRecorder = new MediaRecorder(stream);
                    audioChunks = [];

                    mediaRecorder.ondataavailable = event => {
                        audioChunks.push(event.data);
                    };

                    mediaRecorder.onstop = () => {
                        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                        const audioSize = (audioBlob.size / 1024).toFixed(2);
                        sendQuickReply(`🎤 تم إرسال رسالة صوتية (${audioSize} KB)`);
                        stream.getTracks().forEach(track => track.stop());
                    };

                    mediaRecorder.start();

                    // إيقاف التسجيل بعد 10 ثوان أو عند النقر مرة أخرى
                    setTimeout(() => {
                        if (mediaRecorder.state === 'recording') {
                            mediaRecorder.stop();
                        }
                    }, 10000);

                    alert('بدأ التسجيل... سيتوقف تلقائياً بعد 10 ثوان');
                })
                .catch(err => {
                    alert('خطأ في الوصول للميكروفون: ' + err.message);
                });
        }

        // وظيفة التسجيل الصوتي المبسطة
        function startVoiceRecording() {
            if (confirm('هل تريد إرسال رسالة صوتية؟')) {
                sendQuickReply('🎤 رسالة صوتية (تم التسجيل)');
            }
        }

        // وظيفة رفع الصورة مباشرة
        function attachImageDirect() {
            if (confirm('هل تريد رفع صورة أو فيديو؟')) {
                sendQuickReply('📷 تم رفع صورة/فيديو');
            }
        }

        // وظيفة الاتصال الهاتفي
        function makePhoneCall() {
            if (currentChatId) {
                const conversation = conversations.find(c => c.id === currentChatId);
                if (conversation) {
                    alert(`جاري الاتصال بـ ${conversation.name}... 📞`);
                }
            }
        }

        // تبديل التبويبات
        let currentMessagesTab = 'all';

        function switchMessagesTab(tab) {
            currentMessagesTab = tab;
            document.querySelectorAll('.messages-tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            renderMessagesList();
        }

        function switchMessagesTabFullscreen(tab) {
            currentMessagesTab = tab;
            document.querySelectorAll('.messages-tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            renderMessagesListFullscreen();
        }

        // رسم قائمة الرسائل للنافذة المكبرة
        function renderMessagesListFullscreen(searchQuery = '') {
            const messagesList = document.getElementById('messagesListFullscreen');
            let filteredConversations = conversations;

            if (currentMessagesTab === 'unread') {
                filteredConversations = conversations.filter(c => c.unread > 0);
            }

            if (searchQuery) {
                filteredConversations = filteredConversations.filter(c =>
                    c.name.includes(searchQuery) || c.lastMessage.includes(searchQuery)
                );
            }

            messagesList.innerHTML = filteredConversations.map(conv =>
                '<div class="message-item' + (conv.unread > 0 ? ' unread' : '') + '" onclick="' + (selectionMode ? 'toggleMessageSelection(' + conv.id + ')' : 'openChatFullscreen(' + conv.id + ')') + '">' +
                    (selectionMode ? '<div class="message-checkbox' + (selectedMessages.includes(conv.id) ? ' selected' : '') + '"></div>' : '') +
                    '<div class="message-avatar" onclick="event.stopPropagation(); openUserProfile(\'' + conv.id + '\')" style="cursor: pointer;">' +
                        '<img src="' + conv.avatar + '" alt="' + conv.name + '">' +
                    '</div>' +
                    '<div class="message-content">' +
                        '<div class="message-header">' +
                            '<div class="message-name" onclick="event.stopPropagation(); openUserProfile(\'' + conv.id + '\')" style="cursor: pointer;">' + conv.name + '</div>' +
                            '<div class="message-time">' + conv.time + '</div>' +
                        '</div>' +
                        '<div class="message-preview' + (conv.unread > 0 ? ' unread' : '') + '">' + conv.lastMessage + '</div>' +
                    '</div>' +
                    (conv.unread > 0 ? '<div class="message-badge">' + conv.unread + '</div>' : '') +
                '</div>'
            ).join('');
        }

        // تبديل تحديد الرسالة
        function toggleMessageSelection(messageId) {
            const index = selectedMessages.indexOf(messageId);
            if (index > -1) {
                selectedMessages.splice(index, 1);
            } else {
                selectedMessages.push(messageId);
            }
            renderMessagesListFullscreen();
        }

        // فتح محادثة في النافذة المكبرة
        function openChatFullscreen(conversationId) {
            currentChatId = conversationId;
            const conversation = conversations.find(c => c.id === conversationId);

            // عرض بروفايل المستخدم تلقائياً
            showUserProfile(conversationId);

            if (conversation) {
                const chatArea = document.getElementById('messagesChatArea');
                chatArea.className = 'messages-chat-area';
                chatArea.innerHTML = `
                    <div class="chat-header">
                        <div class="chat-header-left">
                            <div class="chat-user-avatar">
                                <img src="${conversation.avatar}" alt="${conversation.name}">
                            </div>
                            <div class="chat-user-info">
                                <div class="chat-user-name" onclick="openUserProfile('${conversation.id}')" style="cursor: pointer;">${conversation.name}</div>
                                <div class="chat-user-status">${conversation.status}</div>
                            </div>
                        </div>
                        <div class="chat-header-right">

                            <button class="chat-action-btn" onclick="makePhoneCall()" title="اتصال هاتفي">
                                <i class="fas fa-phone"></i>
                            </button>
                            <div class="messages-menu">
                                <button class="chat-action-btn" onclick="toggleChatMenu()">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="messages-dropdown" id="chatDropdown">
                                    <div class="messages-dropdown-item" onclick="addToFavorites()">
                                        <i class="fas fa-star"></i>
                                        جعله من المميزين
                                    </div>
                                    <div class="messages-dropdown-item" onclick="unfollowUser()">
                                        <i class="fas fa-user-minus"></i>
                                        إلغاء المتابعة
                                    </div>
                                    <div class="messages-dropdown-item" onclick="blockUser()">
                                        <i class="fas fa-ban"></i>
                                        حظر
                                    </div>
                                </div>
                            </div>
                            <button class="chat-action-btn" onclick="closeChatFullscreen()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chat-messages" id="chatMessagesFullscreen">
                        ${renderChatMessagesFullscreen(conversation.messages)}
                    </div>
                    <div class="chat-input-container">
                        <div class="chat-input">
                            <button class="chat-attachment-btn" onclick="showAttachmentOptions()" title="رفع ملف">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <button class="chat-image-btn" onclick="attachImageDirect()" title="رفع صورة أو فيديو">
                                <i class="fas fa-image"></i>
                            </button>
                            <button class="chat-voice-btn" onclick="startVoiceRecording()" title="رسالة صوتية">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <input type="text" class="chat-input-field" id="chatInputFieldFullscreen" placeholder="اكتب رسالة..." onkeypress="handleChatKeyPressFullscreen(event)">
                            <button class="chat-send-btn" onclick="sendMessageFullscreen()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                `;

                conversation.unread = 0;
                updateMessagesBadge();
                renderMessagesListFullscreen();
            }
        }

        // رسم رسائل المحادثة للنافذة المكبرة
        function renderChatMessagesFullscreen(messages) {
            return messages.map(message =>
                '<div class="chat-message ' + (message.sent ? 'sent' : 'received') + '">' +
                    (message.sent ? '' :
                        '<div class="chat-message-avatar">' +
                            '<img src="' + conversations.find(c => c.id === currentChatId).avatar + '" alt="">' +
                        '</div>'
                    ) +
                    '<div class="chat-message-bubble">' +
                        '<div class="chat-message-text">' + message.text + '</div>' +
                        '<div class="chat-message-time">' + message.time + '</div>' +
                    '</div>' +
                '</div>'
            ).join('');
        }

        // إغلاق القوائم عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.notifications-menu')) {
                document.getElementById('notificationsDropdown').classList.remove('show');
            }
            if (!event.target.closest('.messages-menu')) {
                const messagesDropdown = document.getElementById('messagesDropdown');
                if (messagesDropdown) {
                    messagesDropdown.classList.remove('show');
                }
            }
            if (!event.target.closest('.messages-fullscreen-menu')) {
                const fullscreenDropdown = document.getElementById('messagesFullscreenDropdown');
                if (fullscreenDropdown) {
                    fullscreenDropdown.classList.remove('show');
                }
            }
            if (!event.target.closest('.quick-replies-modal')) {
                document.getElementById('quickRepliesModal').classList.remove('show');
            }
            if (!event.target.closest('.attachment-options') && !event.target.closest('.chat-attachment-btn')) {
                document.getElementById('attachmentOptions').classList.remove('show');
            }
            if (!event.target.closest('.quick-replies-modal') && !event.target.closest('.chat-quick-replies-btn')) {
                document.getElementById('quickRepliesModal').classList.remove('show');
            }
        });

        // إغلاق القوائم عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.share-btn') && !event.target.closest('.post-menu-btn')) {
                document.querySelectorAll('.share-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
                document.querySelectorAll('.post-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });

        // تحديد نوع تخطيط الصور
        function getImageLayoutClass(imageCount, postId) {
            if (imageCount === 1) return 'single-image';
            if (imageCount === 2) return 'two-images';
            // تخطيط خاص للمطعم (منشور رقم 2)
            if (postId === 2 && imageCount === 3) return 'restaurant-layout';
            return 'multiple-images';
        }

        // توليد النجوم للتقييم
        function generateStars(rating) {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    stars += '<i class="fas fa-star star filled"></i>';
                } else {
                    stars += '<i class="fas fa-star star"></i>';
                }
            }
            return stars;
        }

        // عرض الصورة
        function viewImage(imageName) {
            alert('عرض الصورة: ' + imageName + '\n\nسيتم فتح الصورة في نافذة جديدة...');
            // يمكن هنا إضافة modal لعرض الصورة بحجم كبير
        }

        // عرض جميع الصور
        function viewAllImages(postId) {
            const post = trendingPosts.find(p => p.id === postId);
            if (post && post.images) {
                alert('عرض جميع الصور (' + post.images.length + ' صور)\n\n' +
                      post.images.map((img, i) => (i + 1) + '. ' + img).join('\n'));
            }
        }

        // إضافة أحداث الأزرار
        document.addEventListener('DOMContentLoaded', function() {
            populateServices();
            populatePremiumOffers();
            populateVideoAds();
            populateTrendingPosts();

            // أزرار الخدمات
            document.getElementById('prevBtn').addEventListener('click', function() {
                moveServices('right');
            });

            document.getElementById('nextBtn').addEventListener('click', function() {
                moveServices('left');
            });

            // أزرار العروض المميزة
            document.getElementById('premiumPrevBtn').addEventListener('click', function() {
                movePremiumOffers('right');
            });

            document.getElementById('premiumNextBtn').addEventListener('click', function() {
                movePremiumOffers('left');
            });

            // تحديث مؤشرات الإشعارات والرسائل
            updateNotificationBadge();
            updateMessagesBadge();
        });

        // وظائف إضافية للرسائل
        function showOldMessages() {
            alert('تم تحميل الرسائل السابقة');
        }

        function showOldMessagesFullscreen() {
            alert('تم تحميل الرسائل السابقة في النافذة المكبرة');
        }

        function deleteSelectedMessages() {
            if (selectedMessages && selectedMessages.length > 0) {
                if (confirm('هل أنت متأكد من حذف الرسائل المحددة؟')) {
                    conversations = conversations.filter(c => !selectedMessages.includes(c.id));
                    selectedMessages = [];
                    updateMessagesBadge();
                    renderMessagesList();
                }
            }
        }

        function sendMessageFullscreen() {
            const inputField = document.getElementById('chatInputFieldFullscreen');
            if (inputField && inputField.value.trim()) {
                sendQuickReply(inputField.value.trim());
                inputField.value = '';
            }
        }

        function handleChatKeyPressFullscreen(event) {
            if (event.key === 'Enter') {
                sendMessageFullscreen();
            }
        }

        function toggleChatMenu() {
            const dropdown = document.getElementById('chatDropdown');
            if (dropdown) {
                dropdown.classList.toggle('show');
            }
        }

        function addToFavorites() {
            alert('تم إضافة المستخدم للمفضلة');
            toggleChatMenu();
        }

        function unfollowUser() {
            alert('تم إلغاء متابعة المستخدم');
            toggleChatMenu();
        }

        function blockUser() {
            alert('تم حظر المستخدم');
            toggleChatMenu();
        }

        function closeChatFullscreen() {
            const chatArea = document.getElementById('messagesChatArea');
            if (chatArea) {
                chatArea.className = 'messages-chat-area';
                chatArea.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666; font-size: 16px;">اختر محادثة لبدء المراسلة</div>';
            }
            // إخفاء البروفايل
            hideUserProfile();
        }

        // عرض بروفايل المستخدم
        function showUserProfile(userId) {
            const userData = usersData[userId];
            if (!userData) {
                hideUserProfile();
                return;
            }

            const profilePanel = document.getElementById('userProfilePanel');

            // تحديث بيانات البروفايل
            document.getElementById('profileAvatar').querySelector('img').src = userData.avatar;
            document.getElementById('profileName').textContent = userData.name;
            document.getElementById('profileStatus').textContent = userData.status;
            document.getElementById('profileFollowers').textContent = userData.followers;
            document.getElementById('profileFollowing').textContent = userData.following;
            document.getElementById('profilePosts').textContent = userData.posts;
            document.getElementById('postsCount').textContent = userData.userPosts.length;



            // تحديث النجوم
            updateStarsRating(userData.rating);
            document.getElementById('ratingText').textContent = `(${userData.rating} - ${userData.reviewsCount} تقييم)`;

            // تحديث زر المتابعة
            updateFollowButton(userData.isFollowing);

            // إعادة تعيين التبويب النشط للمنشورات
            currentContentTab = 'posts';
            document.querySelectorAll('.content-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector('.content-tab[onclick="switchTab(\'posts\')"]').classList.add('active');

            // عرض المنشورات
            displayUserPosts(userData.userPosts);

            // إظهار البانل
            profilePanel.classList.add('show');

            // إضافة مستمع التمرير
            setupScrollListener();
        }

        // تبديل التبويبات
        function switchTab(tabType) {
            currentContentTab = tabType;

            // تحديث التبويبات
            document.querySelectorAll('.content-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.closest('.content-tab').classList.add('active');

            // تحديث المحتوى
            if (!currentChatId || !usersData[currentChatId]) return;

            const userData = usersData[currentChatId];

            if (tabType === 'posts') {
                document.getElementById('sectionTitle').textContent = 'المنشورات';
                document.getElementById('postsCount').textContent = userData.userPosts.length;
                displayUserPosts(userData.userPosts);
            } else if (tabType === 'images') {
                document.getElementById('sectionTitle').textContent = 'الصور';
                const imageCount = userData.userPosts.reduce((count, post) =>
                    count + (post.media ? post.media.filter(m => m.type === 'image').length : 0), 0);
                document.getElementById('postsCount').textContent = imageCount;
                displayUserMedia(userData.userPosts, 'image');
            } else if (tabType === 'videos') {
                document.getElementById('sectionTitle').textContent = 'الفيديوهات';
                const videoCount = userData.userPosts.reduce((count, post) =>
                    count + (post.media ? post.media.filter(m => m.type === 'video').length : 0), 0);
                document.getElementById('postsCount').textContent = videoCount;
                displayUserMedia(userData.userPosts, 'video');
            }
        }

        // عرض الوسائط فقط
        function displayUserMedia(posts, mediaType = 'all') {
            const postsContainer = document.getElementById('userPostsList');

            // جمع جميع الوسائط من المنشورات
            const allMedia = [];
            posts.forEach(post => {
                if (post.media && post.media.length > 0) {
                    post.media.forEach(media => {
                        // فلترة حسب نوع الوسائط
                        if (mediaType === 'all' || media.type === mediaType) {
                            allMedia.push({
                                ...media,
                                postId: post.id,
                                postContent: post.content,
                                postCategory: post.category,
                                postTime: post.time
                            });
                        }
                    });
                }
            });

            if (allMedia.length === 0) {
                const mediaTypeText = mediaType === 'image' ? 'صور' :
                                    mediaType === 'video' ? 'فيديوهات' : 'وسائط';
                postsContainer.innerHTML = `
                    <div class="no-results">
                        <i class="fas fa-${mediaType === 'image' ? 'images' : mediaType === 'video' ? 'video' : 'photo-video'}"></i>
                        <div>لا توجد ${mediaTypeText}</div>
                    </div>
                `;
                return;
            }

            postsContainer.innerHTML = `
                <div class="media-gallery-grid">
                    ${allMedia.map((media, index) => `
                        <div class="media-gallery-item" onclick="viewSingleMediaFromGallery('${media.url}', '${media.type}', '${media.postContent}')">
                            ${media.type === 'video' ?
                                `<div class="media-video-container">
                                    <img src="${media.thumbnail || media.url}" alt="فيديو">
                                    <div class="video-play-overlay">
                                        <i class="fas fa-play"></i>
                                    </div>
                                    <div class="media-type-badge">
                                        <i class="fas fa-video"></i>
                                    </div>
                                </div>` :
                                `<div class="media-image-container">
                                    <img src="${media.url}" alt="صورة">
                                    <div class="media-type-badge">
                                        <i class="fas fa-image"></i>
                                    </div>
                                </div>`
                            }
                            <div class="media-info">
                                <div class="media-category">${media.postCategory}</div>
                                <div class="media-time">${media.postTime}</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // عرض وسائط واحدة من المعرض
        function viewSingleMediaFromGallery(url, type, content) {
            const modal = document.getElementById('mediaModal');
            const modalTitle = document.getElementById('mediaModalTitle');
            const modalBody = document.getElementById('mediaModalBody');

            modalTitle.textContent = content.substring(0, 50) + '...';

            if (type === 'video') {
                modalBody.innerHTML = `
                    <video controls autoplay style="width: 100%; max-height: 70vh;">
                        <source src="${url}" type="video/mp4">
                        متصفحك لا يدعم تشغيل الفيديو.
                    </video>
                `;
            } else {
                modalBody.innerHTML = `<img src="${url}" alt="صورة" style="width: 100%; height: auto;">`;
            }

            modal.classList.add('show');
        }

        // تحديث نجوم التقييم
        function updateStarsRating(rating) {
            const stars = document.querySelectorAll('#profileStars .star');
            const fullStars = Math.floor(rating);

            stars.forEach((star, index) => {
                if (index < fullStars) {
                    star.classList.remove('empty');
                } else {
                    star.classList.add('empty');
                }
            });
        }

        // تحديث زر المتابعة
        function updateFollowButton(isFollowing) {
            const followBtn = document.getElementById('followBtn');
            if (isFollowing) {
                followBtn.innerHTML = '<i class="fas fa-user-check"></i> يتم متابعته';
                followBtn.classList.add('following');
            } else {
                followBtn.innerHTML = '<i class="fas fa-user-plus"></i> متابعة';
                followBtn.classList.remove('following');
            }
        }

        // تبديل المتابعة
        function toggleFollow() {
            if (!currentChatId || !usersData[currentChatId]) return;

            const userData = usersData[currentChatId];
            userData.isFollowing = !userData.isFollowing;

            updateFollowButton(userData.isFollowing);

            if (userData.isFollowing) {
                showNotification(`تم متابعة ${userData.name} بنجاح`, '#2ecc71');
            } else {
                showNotification(`تم إلغاء متابعة ${userData.name}`, '#e74c3c');
            }
        }

        // إخفاء بروفايل المستخدم
        function hideUserProfile() {
            const profilePanel = document.getElementById('userProfilePanel');
            profilePanel.classList.remove('show');

            // إعادة تعيين البيانات
            document.getElementById('profileName').textContent = 'اختر محادثة لعرض البروفايل';
            document.getElementById('profileStatus').textContent = 'غير متصل';
            document.getElementById('profileFollowers').textContent = '0';
            document.getElementById('profileFollowing').textContent = '0';
            document.getElementById('profilePosts').textContent = '0';
            document.getElementById('postsCount').textContent = '0';

            // إخفاء المنشورات
            document.getElementById('userPostsList').innerHTML = `
                <div class="no-results">
                    <i class="fas fa-comments"></i>
                    <div>اختر محادثة لعرض منشورات المستخدم</div>
                </div>
            `;
        }

        // عرض منشورات المستخدم
        function displayUserPosts(posts, searchTerm = '') {
            const postsContainer = document.getElementById('userPostsList');

            if (!posts || posts.length === 0) {
                postsContainer.innerHTML = `
                    <div class="no-results">
                        <i class="fas fa-file-alt"></i>
                        <div>لا توجد منشورات</div>
                    </div>
                `;
                return;
            }

            // تصفية المنشورات حسب البحث
            let filteredPosts = posts;
            if (searchTerm) {
                filteredPosts = posts.filter(post =>
                    post.content.includes(searchTerm) ||
                    post.category.includes(searchTerm) ||
                    post.price.includes(searchTerm)
                );
            }

            if (filteredPosts.length === 0) {
                postsContainer.innerHTML = `
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <div>لا توجد نتائج للبحث "${searchTerm}"</div>
                    </div>
                `;
                return;
            }

            postsContainer.innerHTML = filteredPosts.map(post => `
                <div class="user-post">
                    <div class="post-meta">
                        <div class="post-time">${post.time}</div>
                        <div class="post-category">${post.category}</div>
                    </div>
                    <div class="post-content" onclick="viewPostDetails(${post.id})">${post.content}</div>
                    ${renderPostMedia(post.media, post.id)}
                    <div style="margin: 8px 0; font-weight: bold; color: #16CCC8;">السعر: ${post.price}</div>
                    <div class="post-stats">
                        <div class="post-stat likes">
                            <i class="fas fa-heart"></i>
                            <span>${post.likes}</span>
                        </div>
                        <div class="post-stat comments">
                            <i class="fas fa-comment"></i>
                            <span>${post.comments}</span>
                        </div>
                        <div class="post-stat shares">
                            <i class="fas fa-share"></i>
                            <span>${post.shares}</span>
                        </div>
                    </div>
                    <div class="post-actions">
                        <button class="post-action-btn ${post.isLiked ? 'liked' : ''}" onclick="togglePostLike(${post.id})">
                            <i class="fas fa-heart"></i>
                            <span class="action-count">${post.likes}</span>
                        </button>
                        <button class="post-action-btn ${post.isCommented ? 'commented' : ''}" onclick="commentOnPost(${post.id})">
                            <i class="fas fa-comment"></i>
                            <span class="action-count">${post.comments}</span>
                        </button>
                        <button class="post-action-btn ${post.isShared ? 'shared' : ''}" onclick="sharePost(${post.id})">
                            <i class="fas fa-share"></i>
                            <span class="action-count">${post.shares}</span>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // عرض وسائط المنشور
        function renderPostMedia(media, postId) {
            if (!media || media.length === 0) return '';

            if (media.length === 1) {
                const item = media[0];
                if (item.type === 'video') {
                    return `
                        <div class="media-gallery single">
                            <div class="media-item single post-video" onclick="event.stopPropagation(); openMediaModal(${postId})">
                                <img src="${item.thumbnail}" alt="فيديو" class="post-media">
                                <div class="video-overlay">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    return `
                        <div class="media-gallery single">
                            <div class="media-item single" onclick="event.stopPropagation(); openMediaModal(${postId})">
                                <img src="${item.url}" alt="صورة المنشور" class="post-media">
                            </div>
                        </div>
                    `;
                }
            } else if (media.length === 2) {
                return `
                    <div class="media-gallery">
                        ${media.map((item, index) => `
                            <div class="media-item" onclick="event.stopPropagation(); openMediaModal(${postId}, ${index})">
                                <img src="${item.type === 'video' ? item.thumbnail : item.url}" alt="وسائط">
                                ${item.type === 'video' ? '<div class="video-overlay"><i class="fas fa-play"></i></div>' : ''}
                            </div>
                        `).join('')}
                    </div>
                `;
            } else if (media.length === 3) {
                return `
                    <div class="media-gallery triple">
                        <div class="media-item" onclick="event.stopPropagation(); openMediaModal(${postId}, 0)">
                            <img src="${media[0].type === 'video' ? media[0].thumbnail : media[0].url}" alt="وسائط">
                            ${media[0].type === 'video' ? '<div class="video-overlay"><i class="fas fa-play"></i></div>' : ''}
                        </div>
                        <div class="media-item" onclick="event.stopPropagation(); openMediaModal(${postId}, 1)">
                            <img src="${media[1].type === 'video' ? media[1].thumbnail : media[1].url}" alt="وسائط">
                            ${media[1].type === 'video' ? '<div class="video-overlay"><i class="fas fa-play"></i></div>' : ''}
                        </div>
                        <div class="media-item" onclick="event.stopPropagation(); openMediaModal(${postId}, 2)">
                            <img src="${media[2].type === 'video' ? media[2].thumbnail : media[2].url}" alt="وسائط">
                            ${media[2].type === 'video' ? '<div class="video-overlay"><i class="fas fa-play"></i></div>' : ''}
                        </div>
                    </div>
                `;
            } else {
                return `
                    <div class="media-gallery">
                        ${media.slice(0, 3).map((item, index) => `
                            <div class="media-item" onclick="event.stopPropagation(); openMediaModal(${postId}, ${index})">
                                <img src="${item.type === 'video' ? item.thumbnail : item.url}" alt="وسائط">
                                ${item.type === 'video' ? '<div class="video-overlay"><i class="fas fa-play"></i></div>' : ''}
                                ${index === 2 ? `<div class="media-count">+${media.length - 3}</div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                `;
            }
        }

        // فتح مودال عرض الوسائط
        function openMediaModal(postId, startIndex = 0) {
            if (!currentChatId || !usersData[currentChatId]) return;

            const userData = usersData[currentChatId];
            const post = userData.userPosts.find(p => p.id === postId);

            if (!post || !post.media) return;

            const modal = document.getElementById('mediaModal');
            const modalTitle = document.getElementById('mediaModalTitle');
            const modalBody = document.getElementById('mediaModalBody');

            modalTitle.textContent = `وسائط المنشور - ${post.category}`;

            if (post.media.length === 1) {
                const item = post.media[0];
                if (item.type === 'video') {
                    modalBody.innerHTML = `
                        <video controls autoplay style="width: 100%; max-height: 70vh;">
                            <source src="${item.url}" type="video/mp4">
                            متصفحك لا يدعم تشغيل الفيديو.
                        </video>
                    `;
                } else {
                    modalBody.innerHTML = `<img src="${item.url}" alt="صورة" style="width: 100%; height: auto;">`;
                }
            } else {
                modalBody.innerHTML = `
                    <div class="media-gallery-modal">
                        ${post.media.map((item, index) => `
                            <div class="media-item" onclick="viewSingleMedia('${item.url}', '${item.type}')">
                                ${item.type === 'video' ?
                                    `<video><source src="${item.url}" type="video/mp4"></video><div class="video-overlay"><i class="fas fa-play"></i></div>` :
                                    `<img src="${item.url}" alt="وسائط">`
                                }
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            modal.classList.add('show');
        }

        // إغلاق مودال الوسائط
        function closeMediaModal() {
            document.getElementById('mediaModal').classList.remove('show');
        }

        // عرض وسائط واحدة
        function viewSingleMedia(url, type) {
            const modalBody = document.getElementById('mediaModalBody');

            if (type === 'video') {
                modalBody.innerHTML = `
                    <video controls autoplay style="width: 100%; max-height: 70vh;">
                        <source src="${url}" type="video/mp4">
                        متصفحك لا يدعم تشغيل الفيديو.
                    </video>
                `;
            } else {
                modalBody.innerHTML = `<img src="${url}" alt="صورة" style="width: 100%; height: auto;">`;
            }
        }

        // البحث في منشورات المستخدم
        function searchUserPosts(searchTerm) {
            if (!currentChatId || !usersData[currentChatId]) return;

            const userData = usersData[currentChatId];
            displayUserPosts(userData.userPosts, searchTerm);
        }

        // تنفيذ البحث عند النقر على الأيقونة
        function performPostsSearch() {
            const searchInput = document.getElementById('postsSearchInput');
            const searchTerm = searchInput.value.trim();
            searchUserPosts(searchTerm);
        }

        // عرض تفاصيل المنشور
        function viewPostDetails(postId) {
            if (!currentChatId || !usersData[currentChatId]) return;

            const userData = usersData[currentChatId];
            const post = userData.userPosts.find(p => p.id === postId);

            if (post) {
                // إرسال رسالة تلقائية عن المنشور
                const message = `مرحباً، أريد الاستفسار عن هذا المنشور: "${post.content.substring(0, 50)}..." - السعر: ${post.price}`;

                // إضافة الرسالة للمحادثة
                const conversation = conversations.find(c => c.id === currentChatId);
                if (conversation) {
                    const currentTime = new Date().toLocaleTimeString('ar-SA', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });

                    const newMessage = {
                        id: conversation.messages.length + 1,
                        text: message,
                        time: currentTime,
                        sent: true
                    };

                    conversation.messages.push(newMessage);
                    conversation.lastMessage = message;
                    conversation.time = 'الآن';

                    // إعادة رسم الرسائل إذا كانت المحادثة مفتوحة
                    const chatMessages = document.getElementById('chatMessagesFullscreen');
                    if (chatMessages) {
                        chatMessages.innerHTML = renderChatMessagesFullscreen(conversation.messages);
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    }

                    renderMessagesListFullscreen();
                    showNotification('تم إرسال استفسار عن المنشور', '#2ecc71');
                }
            }
        }





        // وظائف التحكم في حجم المحادثة
        function resizeChat(size) {
            const chatArea = document.getElementById('messagesChatArea');
            if (chatArea) {
                // إزالة جميع فئات الحجم
                chatArea.classList.remove('minimized', 'maximized', 'normal');

                // إضافة الفئة الجديدة
                chatArea.classList.add(size);

                // تحديث الرسائل لتتكيف مع الحجم الجديد
                const chatMessages = document.getElementById('chatMessagesFullscreen');
                if (chatMessages) {
                    setTimeout(() => {
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    }, 100);
                }
            }
        }

        // إعداد مستمع التمرير
        function setupScrollListener() {
            const profilePanel = document.querySelector('.user-profile-panel');
            const profileHeader = document.querySelector('.profile-header');
            const postsSearch = document.querySelector('.posts-search');
            const contentTabs = document.querySelector('.content-tabs');
            let lastScrollTop = 0;

            if (profilePanel && profileHeader) {
                profilePanel.addEventListener('scroll', function() {
                    const scrollTop = this.scrollTop;

                    if (scrollTop > lastScrollTop && scrollTop > 50) {
                        // التمرير لأسفل - إخفاء جميع العناصر العلوية
                        profileHeader.classList.add('hidden');
                        if (postsSearch) postsSearch.classList.add('hidden');
                        if (contentTabs) contentTabs.classList.add('hidden');
                    } else if (scrollTop < lastScrollTop || scrollTop <= 20) {
                        // التمرير لأعلى أو في الأعلى - إظهار جميع العناصر
                        profileHeader.classList.remove('hidden');
                        if (postsSearch) postsSearch.classList.remove('hidden');
                        if (contentTabs) contentTabs.classList.remove('hidden');
                    }

                    lastScrollTop = scrollTop;
                });
            }
        }

        // تفعيل/إلغاء الإعجاب
        function togglePostLike(postId) {
            if (!currentChatId || !usersData[currentChatId]) return;

            const userData = usersData[currentChatId];
            const post = userData.userPosts.find(p => p.id === postId);

            if (post) {
                if (post.isLiked) {
                    post.likes--;
                    post.isLiked = false;
                    showNotification('تم إلغاء الإعجاب', '#e74c3c');
                } else {
                    post.likes++;
                    post.isLiked = true;
                    showNotification('تم الإعجاب بالمنشور', '#e74c3c');
                }

                // إعادة عرض المنشورات
                if (currentContentTab === 'posts') {
                    displayUserPosts(userData.userPosts);
                }
            }
        }

        // التعليق على المنشور
        function commentOnPost(postId) {
            if (!currentChatId || !usersData[currentChatId]) return;

            const userData = usersData[currentChatId];
            const post = userData.userPosts.find(p => p.id === postId);

            if (post) {
                if (!post.isCommented) {
                    post.comments++;
                    post.isCommented = true;
                    showNotification('تم إضافة تعليق', '#3498db');

                    // إرسال رسالة تلقائية
                    const message = `أريد التعليق على منشورك: "${post.content.substring(0, 30)}..."`;
                    sendAutoMessage(message);
                } else {
                    showNotification('لقد علقت على هذا المنشور مسبقاً', '#f39c12');
                }

                // إعادة عرض المنشورات
                if (currentContentTab === 'posts') {
                    displayUserPosts(userData.userPosts);
                }
            }
        }

        // مشاركة المنشور
        function sharePost(postId) {
            if (!currentChatId || !usersData[currentChatId]) return;

            const userData = usersData[currentChatId];
            const post = userData.userPosts.find(p => p.id === postId);

            if (post) {
                if (!post.isShared) {
                    post.shares++;
                    post.isShared = true;
                    showNotification('تم مشاركة المنشور', '#2ecc71');
                } else {
                    showNotification('لقد شاركت هذا المنشور مسبقاً', '#f39c12');
                }

                // إعادة عرض المنشورات
                if (currentContentTab === 'posts') {
                    displayUserPosts(userData.userPosts);
                }
            }
        }

        // إرسال رسالة تلقائية
        function sendAutoMessage(message) {
            const conversation = conversations.find(c => c.id === currentChatId);
            if (conversation) {
                const currentTime = new Date().toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                });

                const newMessage = {
                    id: conversation.messages.length + 1,
                    text: message,
                    time: currentTime,
                    sent: true
                };

                conversation.messages.push(newMessage);
                conversation.lastMessage = message;
                conversation.time = 'الآن';

                // إعادة رسم الرسائل إذا كانت المحادثة مفتوحة
                const chatMessages = document.getElementById('chatMessagesFullscreen');
                if (chatMessages) {
                    chatMessages.innerHTML = renderChatMessagesFullscreen(conversation.messages);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }

                renderMessagesListFullscreen();
            }
        }

        // بيانات البروفايل الشخصي
        const myProfileData = {
            name: 'حسين نهاد',
            bio: 'مطور تطبيقات ومهتم بالتكنولوجيا والابتكار',
            avatar: 'images/Hussein Nihad.png',
            followers: '1.3K',
            following: '365',
            posts: '18',
            rating: 4.8,
            work: 'تطوير التطبيقات',
            education: 'جامعة التكنولوجيا',
            location: 'بغداد، العراق',
            relationship: 'أعزب',
            joinDate: 'يناير 2020',
            companyInfo: 'شركة رائدة في مجال تطوير التطبيقات والحلول التقنية المبتكرة. نقدم خدمات متميزة في تطوير تطبيقات الهاتف المحمول وتطبيقات الويب.',
            certificates: [
                {
                    id: 1,
                    title: 'شهادة تطوير التطبيقات المتقدمة',
                    description: 'شهادة معتمدة في تطوير التطبيقات باستخدام أحدث التقنيات والأدوات',
                    date: 'مارس 2023',
                    icon: 'fas fa-code'
                },
                {
                    id: 2,
                    title: 'شهادة إدارة المشاريع التقنية',
                    description: 'شهادة في إدارة وتنسيق المشاريع التقنية الكبيرة',
                    date: 'يونيو 2022',
                    icon: 'fas fa-project-diagram'
                },
                {
                    id: 3,
                    title: 'شهادة الجودة الدولية ISO 9001',
                    description: 'شهادة معايير الجودة الدولية في تطوير البرمجيات',
                    date: 'سبتمبر 2021',
                    icon: 'fas fa-award'
                }
            ],
            photoAlbums: [
                {
                    id: 1,
                    title: 'مشاريع التطبيقات',
                    count: 15,
                    preview: ['images/Hussein Nihad.png', 'images/Hussein Nihad.png', 'images/Hussein Nihad.png'],
                    photos: ['images/Hussein Nihad.png', 'images/Hussein Nihad.png', 'images/Hussein Nihad.png'],
                    privacy: 'public'
                },
                {
                    id: 2,
                    title: 'فريق العمل',
                    count: 8,
                    preview: ['images/Hussein Nihad.png', 'images/Hussein Nihad.png', 'images/Hussein Nihad.png'],
                    photos: ['images/Hussein Nihad.png', 'images/Hussein Nihad.png'],
                    privacy: 'followers'
                }
            ],
            videoAlbums: [
                {
                    id: 1,
                    title: 'عروض المشاريع',
                    count: 5,
                    preview: ['images/Hussein Nihad.png', 'images/Hussein Nihad.png', 'images/Hussein Nihad.png'],
                    videos: ['video1.mp4', 'video2.mp4'],
                    privacy: 'public'
                }
            ],
            myPosts: [
                {
                    id: 1,
                    content: 'أعمل على تطوير تطبيق جديد للتجارة الإلكترونية. متحمس لمشاركة النتائج معكم قريباً!',
                    media: 'images/الصناعة.jpg',
                    time: 'منذ ساعتين',
                    likes: 45,
                    comments: 12,
                    shares: 3,
                    isLiked: false
                },
                {
                    id: 2,
                    content: 'يوم رائع في المكتب! العمل مع فريق مميز يجعل كل شيء أسهل وأمتع.',
                    media: 'images/تعليم.jpg',
                    time: 'منذ 5 ساعات',
                    likes: 78,
                    comments: 23,
                    shares: 15,
                    isLiked: true
                }
            ],
            photos: [
                'images/Hussein Nihad.png',
                'images/الصناعة.jpg',
                'images/تعليم.jpg',
                'images/الطبية.jpg',
                'images/سيارات.jpg',
                'images/مطعم.jpg'
            ],
            followersCount: '1.3K',
            followingCount: '365',
            followersData: [
                // المتابعون المتبادلون (أتابعهم ويتابعونني)
                { name: 'أحمد محمد', avatar: 'images/Hussein Nihad.png', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'سارة علي', avatar: 'images/مستحظرات تجميل.jpg', isFollowing: true, followsMe: true, isOnline: false },
                { name: 'دكتور محمد', avatar: 'images/الطبية.jpg', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'معرض السيارات', avatar: 'images/سيارات.jpg', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'مطعم الأصالة', avatar: 'images/مطعم.jpg', isFollowing: true, followsMe: true, isOnline: false },
                { name: 'بنك الأمان', avatar: 'images/مصارف وبنوك.jpg', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'مزرعة الخير', avatar: 'images/زراعية.jpg', isFollowing: true, followsMe: true, isOnline: false },
                { name: 'متجر الإلكترونيات', avatar: 'images/الصناعة.jpg', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'علي حسن', avatar: 'images/Hussein Nihad.png', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'فاطمة أحمد', avatar: 'images/Hussein Nihad.png', isFollowing: true, followsMe: true, isOnline: false },
                { name: 'محمد العراقي', avatar: 'images/عقارات.jpg', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'نور الهدى', avatar: 'images/تعليم.jpg', isFollowing: true, followsMe: true, isOnline: false },
                { name: 'كافيه الأصدقاء', avatar: 'images/كافيهات.jpg', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'مكتبة النور', avatar: 'images/قرطاسية.jpg', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'شركة السياحة', avatar: 'images/شركات السياحية.jpg', isFollowing: true, followsMe: true, isOnline: false },
                { name: 'محلات الذهب', avatar: 'images/ذهب.jpg', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'خدمات التوصيل', avatar: 'images/توصيل.jpg', isFollowing: true, followsMe: true, isOnline: false },
                { name: 'سوبرماركت الخير', avatar: 'images/السوبرماركت.jpg', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'شركة الاستيراد', avatar: 'images/استيراد وتصدير.jpg', isFollowing: true, followsMe: true, isOnline: true },
                { name: 'منتجع الصيف', avatar: 'images/مصايف.jpg', isFollowing: true, followsMe: true, isOnline: false },

                // الذين أتابعهم فقط (لا يتابعونني)
                { name: 'خالد أحمد', avatar: 'images/Hussein Nihad.png', isFollowing: true, followsMe: false, isOnline: false },
                { name: 'ليلى محمد', avatar: 'images/مستحظرات تجميل.jpg', isFollowing: true, followsMe: false, isOnline: true },
                { name: 'مركز طبي متقدم', avatar: 'images/الطبية.jpg', isFollowing: true, followsMe: false, isOnline: false },
                { name: 'معرض الأثاث', avatar: 'images/محلات.jpg', isFollowing: true, followsMe: false, isOnline: true },
                { name: 'مطعم البحر', avatar: 'images/مطعم.jpg', isFollowing: true, followsMe: false, isOnline: false },

                // الذين يتابعونني فقط (لا أتابعهم)
                { name: 'زينب علي', avatar: 'images/Hussein Nihad.png', isFollowing: false, followsMe: true, isOnline: true },
                { name: 'عمر حسن', avatar: 'images/Hussein Nihad.png', isFollowing: false, followsMe: true, isOnline: false },
                { name: 'مريم أحمد', avatar: 'images/مستحظرات تجميل.jpg', isFollowing: false, followsMe: true, isOnline: true },
                { name: 'صيدلية الشفاء', avatar: 'images/طبية.jpg', isFollowing: false, followsMe: true, isOnline: false },
                { name: 'ورشة السيارات', avatar: 'images/سيارات.jpg', isFollowing: false, followsMe: true, isOnline: true },
                { name: 'مخبز الأسرة', avatar: 'images/مطعم.jpg', isFollowing: false, followsMe: true, isOnline: false },
                { name: 'بقالة الحي', avatar: 'images/السوبرماركت.jpg', isFollowing: false, followsMe: true, isOnline: true },
                { name: 'مدرسة المستقبل', avatar: 'images/تعليم.jpg', isFollowing: false, followsMe: true, isOnline: false }
            ],
            companyInfoSections: {
                biography: {
                    privacy: 'public',
                    content: [
                        {
                            id: 1,
                            type: 'text',
                            content: 'مطور تطبيقات محترف مع خبرة تزيد عن 5 سنوات في تطوير التطبيقات المحمولة وتطبيقات الويب. متخصص في تقنيات Flutter و React Native.',
                            date: '2024-01-15',
                            media: null
                        }
                    ]
                },
                documents: {
                    privacy: 'followers',
                    content: [
                        {
                            id: 1,
                            type: 'media',
                            content: 'صورة من أعمالي السابقة في تطوير التطبيقات',
                            date: '2024-01-10',
                            media: {
                                type: 'image',
                                url: 'images/Hussein Nihad.png'
                            }
                        }
                    ]
                },
                achievements: {
                    privacy: 'public',
                    content: [
                        {
                            id: 1,
                            type: 'text',
                            content: 'حصلت على شهادة تطوير التطبيقات المتقدمة من معهد التكنولوجيا المتقدمة',
                            date: '2023-12-01',
                            media: {
                                type: 'image',
                                url: 'images/Hussein Nihad.png'
                            }
                        }
                    ]
                }
            }
        };

        // عرض البروفايل الشخصي - تحديث 2024
        function showMyProfile() {
            console.log('showMyProfile called');
            const modal = document.getElementById('myProfileModal');

            if (!modal) {
                alert('خطأ: لم يتم العثور على البروفايل');
                return;
            }

            // إظهار المودال بقوة
            modal.style.display = 'block';
            modal.style.visibility = 'visible';
            modal.style.opacity = '1';
            modal.classList.add('show');

            // تحديث البيانات
            const nameEl = document.getElementById('myProfileName');
            const bioEl = document.getElementById('myProfileBio');
            const avatarEl = document.getElementById('myProfileAvatar');
            const followersEl = document.getElementById('myFollowers');
            const followingEl = document.getElementById('myFollowing');
            const postsEl = document.getElementById('myPosts');

            console.log('Elements found:', {
                nameEl: !!nameEl,
                followersEl: !!followersEl,
                followingEl: !!followingEl,
                postsEl: !!postsEl
            });

            if (nameEl) nameEl.textContent = myProfileData.name;
            if (bioEl) bioEl.textContent = myProfileData.bio;
            if (avatarEl) avatarEl.src = myProfileData.avatar;
            if (followersEl) {
                followersEl.textContent = myProfileData.followers;
                console.log('Updated followers to:', myProfileData.followers);
            }
            if (followingEl) {
                followingEl.textContent = myProfileData.following;
                console.log('Updated following to:', myProfileData.following);
            }
            if (postsEl) {
                postsEl.textContent = myProfileData.posts;
                console.log('Updated posts to:', myProfileData.posts);
            }

            // تحديث النجوم
            updateMyRating();

            // عرض المنشورات
            displayMyPosts();

            // عرض المتابعين
            displayMyFollowers();

            // تحميل شريط المتابعين
            console.log('About to call loadFollowersBar');
            loadFollowersBar();

            // تأكد من تحميل الشريط بعد ثانية
            setTimeout(() => {
                console.log('Calling loadFollowersBar again after 1 second');
                loadFollowersBar();
            }, 1000);

            modal.classList.add('show');
        }

        // متغير لحفظ نوع الفلتر الحالي
        let currentFollowersFilter = 'mutual';

        // تحميل شريط المتابعين (10 متابعين - تصميم جميل مع حالة الاتصال)
        function loadFollowersBar() {
            console.log('loadFollowersBar called');
            const followersBarContent = document.getElementById('followersBarContent');

            if (!followersBarContent) {
                console.log('followersBarContent not found');
                return;
            }

            // أخذ أول 10 متابعين
            const displayFollowers = myProfileData.followersData.slice(0, 10);

            console.log('Displaying followers:', displayFollowers.length);

            if (displayFollowers.length === 0) {
                followersBarContent.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لا يوجد متابعون</div>';
                return;
            }

            const html = displayFollowers.map((user, index) => {
                // تقصير الاسم إذا كان طويلاً
                let displayName = user.name;
                if (displayName.length > 10) {
                    displayName = displayName.substring(0, 10) + '...';
                }

                // تحديد كلاس الإطار حسب حالة الاتصال
                const avatarClass = user.isOnline ? 'online' : 'offline';

                return `
                    <div class="instagram-follower-card" onclick="openUserProfile('${user.name}')">
                        <div class="instagram-follower-avatar ${avatarClass}">
                            <img src="${user.avatar}" alt="${user.name}" onerror="this.src='images/Hussein Nihad.png'">
                        </div>
                        <div class="instagram-follower-info">
                            <div class="instagram-follower-name">${displayName}</div>
                        </div>
                    </div>
                `;
            }).join('');

            followersBarContent.innerHTML = html;
        }



        // تبديل توسيع شريط المتابعين
        function toggleFollowersExpansion() {
            const followersBar = document.getElementById('followersBar');
            const followersBarContent = document.getElementById('followersBarContent');
            const followersFilterSection = document.getElementById('followersFilterSection');
            const expandIcon = document.getElementById('expandIcon');

            const isExpanded = followersBar.classList.contains('expanded');

            if (isExpanded) {
                // تصغير - عرض 10 متابعين فقط
                followersBar.classList.remove('expanded');
                followersFilterSection.style.display = 'none';
                followersBarContent.style.maxHeight = '200px';
                followersBarContent.style.overflow = 'hidden';
                expandIcon.className = 'fas fa-expand-alt';

                // إعادة تحميل 10 متابعين فقط
                loadFollowersBar();
            } else {
                // توسيع - إظهار أزرار الفلترة وجميع المتابعين
                followersBar.classList.add('expanded');
                followersFilterSection.style.display = 'block';
                followersBarContent.style.maxHeight = '600px';
                followersBarContent.style.overflow = 'auto';
                expandIcon.className = 'fas fa-compress-alt';

                // تحميل جميع المتابعين افتراضياً
                filterFollowers('all');
            }
        }

        // تحميل جميع المتابعين في العرض الموسع
        function loadAllFollowers() {
            const followersBarContent = document.getElementById('followersBarContent');

            if (!followersBarContent) {
                return;
            }

            // عرض جميع المتابعين (33 شخص)
            const allFollowers = myProfileData.followersData;

            console.log('Loading all followers:', allFollowers.length);

            const html = allFollowers.map((user, index) => {
                // تقصير الاسم إذا كان طويلاً
                let displayName = user.name;
                if (displayName.length > 10) {
                    displayName = displayName.substring(0, 10) + '...';
                }

                // تحديد كلاس الإطار حسب حالة الاتصال
                const avatarClass = user.isOnline ? 'online' : 'offline';

                let statusText = '';
                let statusClass = '';

                if (user.isFollowing && user.followsMe) {
                    statusText = 'متابع متبادل';
                    statusClass = 'mutual-status';
                } else if (user.isFollowing && !user.followsMe) {
                    statusText = 'أتابعه';
                    statusClass = 'following-status';
                } else if (!user.isFollowing && user.followsMe) {
                    statusText = 'يتابعني';
                    statusClass = 'follower-status';
                }

                return `
                    <div class="instagram-follower-card" onclick="openUserProfile('${user.name}')">
                        <div class="instagram-follower-avatar ${avatarClass}">
                            <img src="${user.avatar}" alt="${user.name}" onerror="this.src='images/Hussein Nihad.png'">
                        </div>
                        <div class="instagram-follower-info">
                            <div class="instagram-follower-name">${displayName}</div>
                            <div class="follower-status ${statusClass}" style="display: block; font-size: 9px; margin-top: 2px;">${statusText}</div>
                        </div>
                    </div>
                `;
            }).join('');

            followersBarContent.innerHTML = html;
        }

        // فلترة المتابعين في العرض الموسع
        function filterFollowers(type) {
            // إزالة الكلاس النشط من جميع الأزرار
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));

            // إضافة الكلاس النشط للزر المحدد
            document.getElementById(type + 'Btn').classList.add('active');

            const followersBarContent = document.getElementById('followersBarContent');
            let filteredFollowers = [];

            switch(type) {
                case 'all':
                    // جميع المتابعين
                    filteredFollowers = myProfileData.followersData;
                    break;
                case 'followers':
                    // الذين يتابعونني
                    filteredFollowers = myProfileData.followersData.filter(user => user.followsMe);
                    break;
                case 'following':
                    // الذين أتابعهم
                    filteredFollowers = myProfileData.followersData.filter(user => user.isFollowing);
                    break;
            }

            const html = filteredFollowers.map((user, index) => {
                // تقصير الاسم إذا كان طويلاً
                let displayName = user.name;
                if (displayName.length > 10) {
                    displayName = displayName.substring(0, 10) + '...';
                }

                // تحديد كلاس الإطار حسب حالة الاتصال
                const avatarClass = user.isOnline ? 'online' : 'offline';

                let statusText = '';
                let statusClass = '';

                if (user.isFollowing && user.followsMe) {
                    statusText = 'متابع متبادل';
                    statusClass = 'mutual-status';
                } else if (user.isFollowing && !user.followsMe) {
                    statusText = 'أتابعه';
                    statusClass = 'following-status';
                } else if (!user.isFollowing && user.followsMe) {
                    statusText = 'يتابعني';
                    statusClass = 'follower-status';
                }

                return `
                    <div class="instagram-follower-card" onclick="openUserProfile('${user.name}')">
                        <div class="instagram-follower-avatar ${avatarClass}">
                            <img src="${user.avatar}" alt="${user.name}" onerror="this.src='images/Hussein Nihad.png'">
                        </div>
                        <div class="instagram-follower-info">
                            <div class="instagram-follower-name">${displayName}</div>
                            <div class="follower-status ${statusClass}" style="display: block; font-size: 9px; margin-top: 2px;">${statusText}</div>
                        </div>
                    </div>
                `;
            }).join('');

            followersBarContent.innerHTML = html;
        }

        // تحديث تقييم النجوم
        function updateMyRating() {
            const starsContainer = document.querySelector('#myProfileRating .stars-container-white');
            const ratingText = document.querySelector('#myProfileRating .rating-text-white');
            const rating = myProfileData.rating;

            if (starsContainer) {
                starsContainer.innerHTML = '';
                for (let i = 1; i <= 5; i++) {
                    const star = document.createElement('i');
                    star.className = `fas fa-star star-yellow-large ${i <= rating ? 'filled' : 'empty'}`;
                    starsContainer.appendChild(star);
                }
            }

            if (ratingText) {
                ratingText.textContent = `(${rating} من 5)`;
            }
        }

        // فتح البروفايل الشخصي (دالة مساعدة)
        function openMyProfile() {
            showMyProfile();
        }

        // العودة للصفحة الرئيسية
        function showFeed() {
            // إخفاء جميع النوافذ المفتوحة
            document.querySelectorAll('.modal, .my-profile-modal').forEach(modal => {
                modal.classList.remove('show');
            });

            // تفعيل تبويب الرئيسية
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector('.nav-item.home-item').classList.add('active');
        }

        // إغلاق البروفايل الشخصي
        function closeMyProfile() {
            const modal = document.getElementById('myProfileModal');
            if (modal) {
                modal.classList.remove('show');
                // العودة للصفحة الرئيسية
                showFeed();
            }
        }

        // تبديل تبويبات البروفايل
        function switchProfileTab(tab) {
            // تحديث التبويبات
            document.querySelectorAll('.profile-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // عرض المحتوى المناسب
            const sidebar = document.querySelector('.profile-sidebar');
            const mainContent = document.querySelector('.profile-main-content');

            if (tab === 'posts') {
                sidebar.style.display = 'block';
                mainContent.innerHTML = `
                    <!-- منطقة إنشاء منشور -->
                    <div class="create-post-card">
                        <div class="create-post-header">
                            <img src="images/Hussein Nihad.png" alt="صورتك" class="create-post-avatar">
                            <input type="text" placeholder="منشور جديد..." class="create-post-input" onclick="openCreatePost()">
                        </div>
                        <div class="create-post-actions">
                            <button class="create-action" onclick="addPhoto()">
                                <i class="fas fa-images"></i> صورة/فيديو
                            </button>
                            <button class="create-action" onclick="addFeeling()">
                                <i class="fas fa-smile"></i> شعور/نشاط
                            </button>
                            <button class="create-action" onclick="addLocation()">
                                <i class="fas fa-map-marker-alt"></i> موقع
                            </button>
                        </div>
                    </div>

                    <!-- المنشورات -->
                    <div class="my-posts-container" id="myPostsContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                `;
                displayMyPosts();
            } else if (tab === 'info') {
                sidebar.style.display = 'none';
                mainContent.innerHTML = `
                    <div class="company-info-container">
                        <!-- السيرة الذاتية -->
                        <div class="company-section">
                            <div class="section-header">
                                <h3><i class="fas fa-user-tie"></i> السيرة الذاتية</h3>
                                <div class="section-actions">
                                    <select class="privacy-select" onchange="updateSectionPrivacy('biography', this.value)">
                                        <option value="public">عامة</option>
                                        <option value="followers">المتابعون</option>
                                        <option value="followers_of_followers">متابعو المتابعين</option>
                                        <option value="private">خاص</option>
                                    </select>
                                    <button class="add-content-btn" onclick="showAddContentModal('biography')">
                                        <i class="fas fa-plus"></i> إضافة محتوى
                                    </button>
                                </div>
                            </div>
                            <div class="section-content" id="biographyContent">
                                ${renderSectionContent('biography')}
                            </div>
                        </div>

                        <!-- الصور والوثائق -->
                        <div class="company-section">
                            <div class="section-header">
                                <h3><i class="fas fa-images"></i> الصور والوثائق</h3>
                                <div class="section-actions">
                                    <select class="privacy-select" onchange="updateSectionPrivacy('documents', this.value)">
                                        <option value="public">عامة</option>
                                        <option value="followers">المتابعون</option>
                                        <option value="followers_of_followers">متابعو المتابعين</option>
                                        <option value="private">خاص</option>
                                    </select>
                                    <button class="add-content-btn" onclick="showAddContentModal('documents')">
                                        <i class="fas fa-plus"></i> إضافة محتوى
                                    </button>
                                </div>
                            </div>
                            <div class="section-content" id="documentsContent">
                                ${renderSectionContent('documents')}
                            </div>
                        </div>

                        <!-- الشهادات والإنجازات -->
                        <div class="company-section">
                            <div class="section-header">
                                <h3><i class="fas fa-certificate"></i> الشهادات والإنجازات</h3>
                                <div class="section-actions">
                                    <select class="privacy-select" onchange="updateSectionPrivacy('achievements', this.value)">
                                        <option value="public">عامة</option>
                                        <option value="followers">المتابعون</option>
                                        <option value="followers_of_followers">متابعو المتابعين</option>
                                        <option value="private">خاص</option>
                                    </select>
                                    <button class="add-content-btn" onclick="showAddContentModal('achievements')">
                                        <i class="fas fa-plus"></i> إضافة محتوى
                                    </button>
                                </div>
                            </div>
                            <div class="section-content" id="achievementsContent">
                                ${renderSectionContent('achievements')}
                            </div>
                        </div>

                        <!-- معلومات أساسية -->
                        <div class="company-section">
                            <div class="section-header">
                                <h3><i class="fas fa-info-circle"></i> معلومات أساسية</h3>
                            </div>
                            <div class="basic-info-grid">
                                <div class="info-item">
                                    <i class="fas fa-briefcase"></i>
                                    <span>يعمل في <strong>${myProfileData.work}</strong></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>درس في <strong>${myProfileData.education}</strong></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>يعيش في <strong>${myProfileData.location}</strong></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>انضم في <strong>${myProfileData.joinDate}</strong></span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            } else if (tab === 'certificates') {
                sidebar.style.display = 'none';
                mainContent.innerHTML = `
                    <div class="certificates-container">
                        <button class="add-certificate-btn" onclick="addCertificate()">
                            <i class="fas fa-plus"></i> إضافة شهادة جديدة
                        </button>
                        ${myProfileData.certificates.map(cert => `
                            <div class="certificate-card">
                                <div class="certificate-header">
                                    <div class="certificate-icon">
                                        <i class="${cert.icon}"></i>
                                    </div>
                                    <div>
                                        <div class="certificate-title">${cert.title}</div>
                                        <div class="certificate-date">${cert.date}</div>
                                    </div>
                                </div>
                                <div class="certificate-description">${cert.description}</div>
                            </div>
                        `).join('')}
                    </div>
                `;
            } else if (tab === 'photo-albums') {
                sidebar.style.display = 'none';
                mainContent.innerHTML = `
                    <div class="albums-container">
                        <div class="albums-header">
                            <h3><i class="fas fa-images"></i> ألبومات الصور</h3>
                            <div class="albums-actions">
                                <button class="view-all-btn" onclick="viewAllPhotos()">
                                    <i class="fas fa-th"></i> عرض جميع الصور
                                </button>
                            </div>
                        </div>
                        <div class="simple-albums-grid">
                            <!-- مربع إضافة ألبوم جديد -->
                            <div class="simple-album-card add-new" onclick="showCreatePhotoAlbumModal()">
                                <div class="simple-album-image">
                                    <div class="add-icon">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                </div>
                                <div class="simple-album-info">
                                    <h3>إنشاء ألبوم جديد</h3>
                                </div>
                            </div>

                            <!-- الألبومات الموجودة -->
                            ${myProfileData.photoAlbums.map(album => `
                                <div class="simple-album-card" onclick="viewPhotoAlbum(${album.id})">
                                    <div class="simple-album-image">
                                        <img src="${album.preview[0] || 'images/Hussein Nihad.png'}" alt="${album.title}">
                                    </div>
                                    <div class="simple-album-info">
                                        <h3>${album.title}</h3>
                                        <div class="simple-album-count">${album.count} صورة</div>
                                        <div class="simple-album-controls" onclick="event.stopPropagation()">
                                            <div class="album-menu" onclick="toggleAlbumMenu(${album.id}, 'photo')">
                                                <i class="fas fa-ellipsis-v"></i>
                                                <div class="album-dropdown" id="photoAlbumMenu${album.id}">
                                                    <div class="album-dropdown-item" onclick="editPhotoAlbum(${album.id})">
                                                        <i class="fas fa-edit"></i> تعديل الألبوم
                                                    </div>
                                                    <div class="album-dropdown-item" onclick="deletePhotoAlbum(${album.id})">
                                                        <i class="fas fa-trash"></i> حذف الألبوم
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="privacy-control" onclick="togglePrivacyMenu(${album.id}, 'photo')">
                                                <i class="fas fa-globe"></i>
                                                <div class="privacy-dropdown" id="photoPrivacyMenu${album.id}">
                                                    <div class="privacy-dropdown-item" onclick="setAlbumPrivacy(${album.id}, 'photo', 'public')">
                                                        <i class="fas fa-globe"></i> عامة
                                                    </div>
                                                    <div class="privacy-dropdown-item" onclick="setAlbumPrivacy(${album.id}, 'photo', 'followers')">
                                                        <i class="fas fa-users"></i> المتابعون
                                                    </div>
                                                    <div class="privacy-dropdown-item" onclick="setAlbumPrivacy(${album.id}, 'photo', 'followers_of_followers')">
                                                        <i class="fas fa-user-friends"></i> متابعو المتابعين
                                                    </div>
                                                    <div class="privacy-dropdown-item" onclick="setAlbumPrivacy(${album.id}, 'photo', 'private')">
                                                        <i class="fas fa-lock"></i> أنا فقط
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else if (tab === 'video-albums') {
                sidebar.style.display = 'none';
                mainContent.innerHTML = `
                    <div class="albums-container">
                        <div class="albums-header">
                            <h3><i class="fas fa-video"></i> ألبومات الفيديوهات</h3>
                            <div class="albums-actions">
                                <button class="view-all-btn" onclick="viewAllVideos()">
                                    <i class="fas fa-play"></i> عرض جميع الفيديوهات
                                </button>
                            </div>
                        </div>
                        <div class="simple-albums-grid">
                            <!-- مربع إضافة ألبوم فيديو جديد -->
                            <div class="simple-album-card add-new" onclick="showCreateVideoAlbumModal()">
                                <div class="simple-album-image">
                                    <div class="add-icon">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                </div>
                                <div class="simple-album-info">
                                    <h3>إنشاء ألبوم فيديو جديد</h3>
                                </div>
                            </div>

                            <!-- الألبومات الموجودة -->
                            ${myProfileData.videoAlbums.map(album => `
                                <div class="simple-album-card" onclick="viewVideoAlbum(${album.id})">
                                    <div class="simple-album-image">
                                        <img src="${album.preview[0] || 'images/Hussein Nihad.png'}" alt="${album.title}">
                                        <div class="video-play-overlay">
                                            <div class="video-play-icon">
                                                <i class="fas fa-play"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="simple-album-info">
                                        <h3>${album.title}</h3>
                                        <div class="simple-album-count">${album.count} فيديو</div>
                                        <div class="simple-album-controls" onclick="event.stopPropagation()">
                                            <div class="album-menu" onclick="toggleAlbumMenu(${album.id}, 'video')">
                                                <i class="fas fa-ellipsis-v"></i>
                                                <div class="album-dropdown" id="videoAlbumMenu${album.id}">
                                                    <div class="album-dropdown-item" onclick="editVideoAlbum(${album.id})">
                                                        <i class="fas fa-edit"></i> تعديل الألبوم
                                                    </div>
                                                    <div class="album-dropdown-item" onclick="deleteVideoAlbum(${album.id})">
                                                        <i class="fas fa-trash"></i> حذف الألبوم
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="privacy-control" onclick="togglePrivacyMenu(${album.id}, 'video')">
                                                <i class="fas fa-globe"></i>
                                                <div class="privacy-dropdown" id="videoPrivacyMenu${album.id}">
                                                    <div class="privacy-dropdown-item" onclick="setAlbumPrivacy(${album.id}, 'video', 'public')">
                                                        <i class="fas fa-globe"></i> عامة
                                                    </div>
                                                    <div class="privacy-dropdown-item" onclick="setAlbumPrivacy(${album.id}, 'video', 'followers')">
                                                        <i class="fas fa-users"></i> المتابعون
                                                    </div>
                                                    <div class="privacy-dropdown-item" onclick="setAlbumPrivacy(${album.id}, 'video', 'followers_of_followers')">
                                                        <i class="fas fa-user-friends"></i> متابعو المتابعين
                                                    </div>
                                                    <div class="privacy-dropdown-item" onclick="setAlbumPrivacy(${album.id}, 'video', 'private')">
                                                        <i class="fas fa-lock"></i> أنا فقط
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else if (tab === 'followers') {
                sidebar.style.display = 'none';
                mainContent.innerHTML = `
                    <div class="followers-card">
                        <h3><i class="fas fa-users"></i> جميع المتابعين</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); gap: 15px;">
                            ${myProfileData.followersData.map(follower => `
                                <div class="follower-item" style="border: 1px solid #e1e8ed; border-radius: 12px; padding: 15px; cursor: pointer;" onclick="openUserProfile('${follower.name}')">
                                    <div class="follower-avatar" style="margin-bottom: 10px;">
                                        <img src="${follower.avatar}" alt="${follower.name}">
                                    </div>
                                    <div class="follower-name" style="margin-bottom: 8px; font-size: 14px;">${follower.name}</div>
                                    <button class="follow-back-btn" onclick="event.stopPropagation(); toggleFollow('${follower.name}')"
                                            style="padding: 6px 12px; border: 1px solid #16CCC8; background: ${follower.isFollowing ? '#16CCC8' : 'white'};
                                                   color: ${follower.isFollowing ? 'white' : '#16CCC8'}; border-radius: 20px; font-size: 12px; cursor: pointer;">
                                        ${follower.isFollowing ? 'يتابع' : 'متابعة'}
                                    </button>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else if (tab === 'ask') {
                sidebar.style.display = 'none';
                mainContent.innerHTML = `
                    <div class="ask-card" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <h3><i class="fas fa-question-circle"></i> اسألني</h3>
                        <p style="color: #666; margin-bottom: 20px;">يمكنك طرح سؤال أو اقتراح أو شكوى وسيصلني كرسالة مهمة</p>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">نوع الرسالة:</label>
                            <select id="messageType" style="width: 100%; padding: 10px; border: 2px solid #16CCC8; border-radius: 8px; font-size: 14px;">
                                <option value="question">سؤال</option>
                                <option value="suggestion">اقتراح</option>
                                <option value="complaint">شكوى</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">الرسالة:</label>
                            <textarea id="askMessage" placeholder="اكتب رسالتك هنا..."
                                      style="width: 100%; height: 120px; padding: 12px; border: 2px solid #16CCC8; border-radius: 8px;
                                             font-size: 14px; resize: vertical; font-family: inherit;"></textarea>
                        </div>
                        <button onclick="sendAskMessage()"
                                style="background: linear-gradient(135deg, #16CCC8, #227FCC); color: white; border: none;
                                       padding: 12px 24px; border-radius: 25px; font-size: 16px; font-weight: 600;
                                       cursor: pointer; transition: all 0.3s;">
                            <i class="fas fa-paper-plane"></i> إرسال الرسالة
                        </button>
                    </div>
                `;
            }
        }

        // عرض منشوراتي
        function displayMyPosts() {
            const container = document.getElementById('myPostsContainer');
            if (!container) {
                console.error('Posts container not found');
                return;
            }
            container.innerHTML = myProfileData.myPosts.map(post => `
                <div class="trending-post">
                    <div class="trending-post-header">
                        <div class="trending-post-avatar" onclick="openUserProfile('myProfile')" style="cursor: pointer;">
                            <img src="${myProfileData.avatar}" alt="${myProfileData.name}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                        </div>
                        <div class="trending-post-info">
                            <div class="trending-post-author" onclick="openUserProfile('myProfile')" style="cursor: pointer;">
                                ${myProfileData.name}
                            </div>
                            <div class="trending-post-time">${post.time}</div>
                        </div>
                        <div class="trending-post-menu">
                            <i class="fas fa-ellipsis-h"></i>
                        </div>
                    </div>
                    <div class="trending-post-content">${post.content}</div>
                    ${post.media ? `<div class="trending-post-media"><img src="${post.media}" alt="صورة المنشور"></div>` : ''}
                    <div class="trending-post-actions">
                        <button class="trending-action ${post.isLiked ? 'liked' : ''}" onclick="toggleMyPostLike(${post.id})">
                            <i class="fas fa-heart"></i> ${post.likes}
                        </button>
                        <button class="trending-action">
                            <i class="fas fa-comment"></i> ${post.comments}
                        </button>
                        <button class="trending-action">
                            <i class="fas fa-share"></i> ${post.shares}
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // عرض صوري
        function displayMyPhotos() {
            const container = document.getElementById('myPhotosGrid');
            container.innerHTML = myProfileData.photos.slice(0, 9).map(photo => `
                <div class="photo-item" onclick="viewPhoto('${photo}')">
                    <img src="${photo}" alt="صورة">
                </div>
            `).join('');
        }

        // عرض متابعيني
        function displayMyFollowers() {
            const container = document.getElementById('myFollowersGrid');
            container.innerHTML = myProfileData.followersData.slice(0, 9).map(follower => `
                <div class="follower-item" onclick="openUserProfile('${follower.name}')" style="cursor: pointer;">
                    <div class="follower-avatar">
                        <img src="${follower.avatar}" alt="${follower.name}">
                    </div>
                    <div class="follower-name">${follower.name}</div>
                </div>
            `).join('');
        }

        // تفعيل/إلغاء الإعجاب بمنشوراتي
        function toggleMyPostLike(postId) {
            const post = myProfileData.myPosts.find(p => p.id === postId);
            if (post) {
                if (post.isLiked) {
                    post.likes--;
                    post.isLiked = false;
                } else {
                    post.likes++;
                    post.isLiked = true;
                }
                displayMyPosts();
            }
        }

        // عرض صورة
        function viewPhoto(photoUrl) {
            const modal = document.getElementById('mediaModal');
            const modalTitle = document.getElementById('mediaModalTitle');
            const modalBody = document.getElementById('mediaModalBody');

            modalTitle.textContent = 'عرض الصورة';
            modalBody.innerHTML = `<img src="${photoUrl}" alt="صورة" style="width: 100%; height: auto;">`;
            modal.classList.add('show');
        }

        // وظائف إضافية للبروفايل

        function editAvatar() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('myProfileAvatar').src = e.target.result;
                        showNotification('تم تغيير الصورة الشخصية بنجاح!', '#4CAF50');
                    };
                    reader.readAsDataURL(file);
                }
            };
            input.click();
        }

        // تبديل حالة الاتصال للبروفايل
        function toggleOnlineStatus() {
            const avatar = document.querySelector('.profile-avatar-extra-large');
            if (avatar.classList.contains('online')) {
                avatar.classList.remove('online');
                avatar.classList.add('offline');
                showNotification('تم تغيير الحالة إلى غير متصل', '#9E9E9E');
            } else {
                avatar.classList.remove('offline');
                avatar.classList.add('online');
                showNotification('تم تغيير الحالة إلى متصل', '#4CAF50');
            }
        }

        // تعيين التقييم
        function setRating(rating) {
            const stars = document.querySelectorAll('.stars-container-white i');
            const ratingText = document.querySelector('.rating-text-white');

            // إعادة تعيين جميع النجوم
            stars.forEach((star, index) => {
                star.className = 'fas fa-star star-yellow-large';
                if (index < rating) {
                    star.classList.add('filled');
                } else {
                    star.classList.add('empty');
                }
            });

            // تحديث النص
            ratingText.textContent = `(${rating} من 5)`;

            showNotification(`تم تعيين التقييم: ${rating} نجوم`, '#FFA000');
        }

        function openCreatePost() {
            showNotification('سيتم إضافة ميزة إنشاء المنشورات قريباً', '#16CCC8');
        }

        function addPhoto() {
            showNotification('سيتم إضافة ميزة رفع الصور قريباً', '#16CCC8');
        }

        function addFeeling() {
            showNotification('سيتم إضافة ميزة المشاعر والأنشطة قريباً', '#16CCC8');
        }

        function addLocation() {
            showNotification('سيتم إضافة ميزة الموقع قريباً', '#16CCC8');
        }

        // عرض صورة البروفايل كاملة
        function viewMyAvatar() {
            const modal = document.getElementById('mediaModal');
            const modalTitle = document.getElementById('mediaModalTitle');
            const modalBody = document.getElementById('mediaModalBody');

            modalTitle.textContent = 'صورة الملف الشخصي';
            modalBody.innerHTML = `<img src="${myProfileData.avatar}" alt="صورة الملف الشخصي" style="width: 100%; height: auto;">`;
            modal.classList.add('show');
        }

        // تعديل صورة الملف الشخصي (وظيفة مكررة - محذوفة)

        // إضافة معلومات الشركة
        function addCompanyInfo() {
            const infoTypes = [
                'نبذة مختصرة عن الشركة',
                'تاريخ تأسيس الشركة',
                'طريقة العمل والخدمات',
                'رؤية ورسالة الشركة',
                'معلومات الاتصال',
                'معلومات أخرى'
            ];

            let infoMenu = 'اختر نوع المعلومات التي تريد إضافتها:\n\n';
            infoTypes.forEach((type, index) => {
                infoMenu += `${index + 1}. ${type}\n`;
            });

            const choice = prompt(infoMenu + '\nأدخل رقم الاختيار (1-6):');
            if (!choice || choice < 1 || choice > 6) return;

            const selectedType = infoTypes[choice - 1];
            const newInfo = prompt(`إضافة ${selectedType}:`);

            if (newInfo && newInfo.trim()) {
                const infoEntry = `**${selectedType}:**\n${newInfo.trim()}`;

                if (myProfileData.companyInfo) {
                    myProfileData.companyInfo += '\n\n' + infoEntry;
                } else {
                    myProfileData.companyInfo = infoEntry;
                }

                showNotification('تم إضافة المعلومات بنجاح', '#16CCC8');
                switchProfileTab('info');
            }
        }

        // إضافة شهادة جديدة
        function addCertificate() {
            const title = prompt('عنوان الشهادة:');
            if (!title) return;

            const description = prompt('وصف الشهادة:');
            if (!description) return;

            const date = prompt('تاريخ الحصول على الشهادة:');
            if (!date) return;

            const newCert = {
                id: myProfileData.certificates.length + 1,
                title: title,
                description: description,
                date: date,
                icon: 'fas fa-certificate'
            };

            myProfileData.certificates.push(newCert);
            showNotification('تم إضافة الشهادة بنجاح', '#16CCC8');
            switchProfileTab('certificates');
        }

        // إظهار نافذة إنشاء ألبوم صور
        function showCreatePhotoAlbumModal() {
            document.getElementById('createPhotoAlbumModal').classList.add('show');
            document.getElementById('photoAlbumName').value = '';
            document.getElementById('selectedPhotosPreview').innerHTML = '';
        }

        // إغلاق نافذة إنشاء ألبوم صور
        function closeCreatePhotoAlbumModal() {
            document.getElementById('createPhotoAlbumModal').classList.remove('show');
        }

        // اختيار صور للألبوم
        function selectPhotosForAlbum() {
            document.getElementById('photoAlbumFiles').click();
        }

        // معالج تغيير ملفات الصور
        document.addEventListener('DOMContentLoaded', function() {
            const photoInput = document.getElementById('photoAlbumFiles');
            const videoInput = document.getElementById('videoAlbumFiles');
            const contentMediaInput = document.getElementById('contentMediaFile');

            if (photoInput) {
                photoInput.addEventListener('change', function(e) {
                    const files = Array.from(e.target.files);
                    const preview = document.getElementById('selectedPhotosPreview');
                    preview.innerHTML = '';

                    files.forEach((file, index) => {
                        if (file.type.startsWith('image/')) {
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                const fileItem = document.createElement('div');
                                fileItem.className = 'selected-file-item';
                                fileItem.innerHTML = `
                                    <img src="${e.target.result}" alt="معاينة">
                                    <div class="selected-file-name">${file.name}</div>
                                    <button class="remove-file-btn" onclick="removeSelectedFile(${index}, 'photo')">
                                        <i class="fas fa-times"></i>
                                    </button>
                                `;
                                preview.appendChild(fileItem);
                            };
                            reader.readAsDataURL(file);
                        }
                    });
                });
            }

            if (videoInput) {
                videoInput.addEventListener('change', function(e) {
                    const files = Array.from(e.target.files);
                    const preview = document.getElementById('selectedVideosPreview');
                    preview.innerHTML = '';

                    files.forEach((file, index) => {
                        if (file.type.startsWith('video/')) {
                            const fileItem = document.createElement('div');
                            fileItem.className = 'selected-file-item';
                            fileItem.innerHTML = `
                                <div style="height: 80px; display: flex; align-items: center; justify-content: center; background: #f8f9fa;">
                                    <i class="fas fa-video" style="font-size: 24px; color: #e74c3c;"></i>
                                </div>
                                <div class="selected-file-name">${file.name}</div>
                                <button class="remove-file-btn" onclick="removeSelectedFile(${index}, 'video')">
                                    <i class="fas fa-times"></i>
                                </button>
                            `;
                            preview.appendChild(fileItem);
                        }
                    });
                });
            }

            // معالج ملف المحتوى
            if (contentMediaInput) {
                contentMediaInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    const preview = document.getElementById('selectedContentPreview');
                    preview.innerHTML = '';

                    if (file) {
                        const fileItem = document.createElement('div');
                        fileItem.className = 'selected-file-item';

                        if (file.type.startsWith('image/')) {
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                fileItem.innerHTML = `
                                    <img src="${e.target.result}" alt="معاينة">
                                    <div class="selected-file-name">${file.name}</div>
                                `;
                                preview.appendChild(fileItem);
                            };
                            reader.readAsDataURL(file);
                        } else if (file.type.startsWith('video/')) {
                            fileItem.innerHTML = `
                                <div style="height: 80px; display: flex; align-items: center; justify-content: center; background: #f8f9fa;">
                                    <i class="fas fa-video" style="font-size: 24px; color: #e74c3c;"></i>
                                </div>
                                <div class="selected-file-name">${file.name}</div>
                            `;
                            preview.appendChild(fileItem);
                        }
                    }
                });
            }
        });

        // إنشاء ألبوم صور
        function createPhotoAlbum() {
            const albumName = document.getElementById('photoAlbumName').value.trim();
            const files = document.getElementById('photoAlbumFiles').files;

            if (!albumName) {
                showNotification('يرجى إدخال اسم الألبوم', '#e74c3c');
                return;
            }

            const newAlbum = {
                id: myProfileData.photoAlbums.length + 1,
                title: albumName,
                count: files.length,
                preview: [],
                photos: [],
                privacy: 'public'
            };

            // معالجة الصور المرفوعة
            if (files.length > 0) {
                Array.from(files).forEach((file, index) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        newAlbum.photos.push(e.target.result);
                        if (index < 3) {
                            newAlbum.preview.push(e.target.result);
                        }

                        // إذا تم تحميل جميع الصور
                        if (newAlbum.photos.length === files.length) {
                            // ملء المعاينة إذا كانت أقل من 3
                            while (newAlbum.preview.length < 3) {
                                newAlbum.preview.push(newAlbum.photos[0] || 'images/Hussein Nihad.png');
                            }
                        }
                    };
                    reader.readAsDataURL(file);
                });
            } else {
                // إذا لم يتم رفع صور، استخدم صور افتراضية
                newAlbum.preview = ['images/Hussein Nihad.png', 'images/Hussein Nihad.png', 'images/Hussein Nihad.png'];
            }

            myProfileData.photoAlbums.push(newAlbum);
            showNotification('تم إنشاء ألبوم الصور بنجاح', '#16CCC8');
            closeCreatePhotoAlbumModal();
            switchProfileTab('photo-albums');
        }

        // رفع صور لألبوم معين
        function uploadToAlbum(albumId) {
            const album = myProfileData.photoAlbums.find(a => a.id === albumId);
            if (!album) return;

            // محاكاة رفع الصور
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = 'image/*';

            input.onchange = function(e) {
                const files = Array.from(e.target.files);
                if (files.length === 0) return;

                // محاكاة رفع الصور
                files.forEach(file => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        album.photos.push(e.target.result);
                        album.count = album.photos.length;

                        // تحديث المعاينة
                        if (album.preview.length < 3) {
                            album.preview.push(e.target.result);
                        }
                    };
                    reader.readAsDataURL(file);
                });

                setTimeout(() => {
                    showNotification(`تم رفع ${files.length} صورة إلى ألبوم "${album.title}"`, '#16CCC8');
                    switchProfileTab('photo-albums');
                }, 1000);
            };

            input.click();
        }

        // رفع صور لألبوم موجود
        function uploadPhotosToExisting() {
            if (myProfileData.photoAlbums.length === 0) {
                showNotification('يجب إنشاء ألبوم أولاً', '#e74c3c');
                return;
            }

            let albumsList = 'اختر الألبوم لرفع الصور إليه:\n\n';
            myProfileData.photoAlbums.forEach((album, index) => {
                albumsList += `${index + 1}. ${album.title} (${album.count} صورة)\n`;
            });

            const choice = prompt(albumsList + '\nأدخل رقم الألبوم:');
            if (!choice || choice < 1 || choice > myProfileData.photoAlbums.length) return;

            const selectedAlbum = myProfileData.photoAlbums[choice - 1];
            uploadToAlbum(selectedAlbum.id);
        }

        // إظهار نافذة إنشاء ألبوم فيديوهات
        function showCreateVideoAlbumModal() {
            document.getElementById('createVideoAlbumModal').classList.add('show');
            document.getElementById('videoAlbumName').value = '';
            document.getElementById('selectedVideosPreview').innerHTML = '';
        }

        // إغلاق نافذة إنشاء ألبوم فيديوهات
        function closeCreateVideoAlbumModal() {
            document.getElementById('createVideoAlbumModal').classList.remove('show');
        }

        // اختيار فيديوهات للألبوم
        function selectVideosForAlbum() {
            document.getElementById('videoAlbumFiles').click();
        }

        // إنشاء ألبوم فيديوهات
        function createVideoAlbum() {
            const albumName = document.getElementById('videoAlbumName').value.trim();
            const files = document.getElementById('videoAlbumFiles').files;

            if (!albumName) {
                showNotification('يرجى إدخال اسم الألبوم', '#e74c3c');
                return;
            }

            const newAlbum = {
                id: myProfileData.videoAlbums.length + 1,
                title: albumName,
                count: files.length,
                preview: ['images/Hussein Nihad.png', 'images/Hussein Nihad.png', 'images/Hussein Nihad.png'],
                videos: [],
                privacy: 'public'
            };

            // معالجة الفيديوهات المرفوعة
            if (files.length > 0) {
                Array.from(files).forEach(file => {
                    newAlbum.videos.push(file.name);
                });
            }

            myProfileData.videoAlbums.push(newAlbum);
            showNotification('تم إنشاء ألبوم الفيديوهات بنجاح', '#e74c3c');
            closeCreateVideoAlbumModal();
            switchProfileTab('video-albums');
        }

        // رفع فيديوهات لألبوم معين
        function uploadToVideoAlbum(albumId) {
            const album = myProfileData.videoAlbums.find(a => a.id === albumId);
            if (!album) return;

            // محاكاة رفع الفيديوهات
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = 'video/*';

            input.onchange = function(e) {
                const files = Array.from(e.target.files);
                if (files.length === 0) return;

                // محاكاة رفع الفيديوهات
                files.forEach(file => {
                    album.videos.push(file.name);
                    album.count = album.videos.length;
                });

                setTimeout(() => {
                    showNotification(`تم رفع ${files.length} فيديو إلى ألبوم "${album.title}"`, '#e74c3c');
                    switchProfileTab('video-albums');
                }, 1000);
            };

            input.click();
        }

        // رفع فيديوهات لألبوم موجود
        function uploadVideosToExisting() {
            if (myProfileData.videoAlbums.length === 0) {
                showNotification('يجب إنشاء ألبوم فيديوهات أولاً', '#e74c3c');
                return;
            }

            let albumsList = 'اختر الألبوم لرفع الفيديوهات إليه:\n\n';
            myProfileData.videoAlbums.forEach((album, index) => {
                albumsList += `${index + 1}. ${album.title} (${album.count} فيديو)\n`;
            });

            const choice = prompt(albumsList + '\nأدخل رقم الألبوم:');
            if (!choice || choice < 1 || choice > myProfileData.videoAlbums.length) return;

            const selectedAlbum = myProfileData.videoAlbums[choice - 1];
            uploadToVideoAlbum(selectedAlbum.id);
        }

        // عرض ألبوم صور
        function viewPhotoAlbum(albumId) {
            const album = myProfileData.photoAlbums.find(a => a.id === albumId);
            if (album) {
                showNotification(`عرض ألبوم: ${album.title}`, '#16CCC8');
            }
        }

        // تعديل ألبوم صور
        function editPhotoAlbum(albumId) {
            const album = myProfileData.photoAlbums.find(a => a.id === albumId);
            if (album) {
                const newTitle = prompt('تعديل اسم الألبوم:', album.title);
                if (newTitle && newTitle.trim()) {
                    album.title = newTitle.trim();
                    showNotification('تم تحديث اسم الألبوم', '#16CCC8');
                    switchProfileTab('photo-albums');
                }
            }
        }

        // عرض ألبوم فيديوهات
        function viewVideoAlbum(albumId) {
            const album = myProfileData.videoAlbums.find(a => a.id === albumId);
            if (album) {
                showNotification(`عرض ألبوم: ${album.title}`, '#16CCC8');
            }
        }

        // تعديل ألبوم فيديوهات
        function editVideoAlbum(albumId) {
            const album = myProfileData.videoAlbums.find(a => a.id === albumId);
            if (album) {
                const newTitle = prompt('تعديل اسم الألبوم:', album.title);
                if (newTitle && newTitle.trim()) {
                    album.title = newTitle.trim();
                    showNotification('تم تحديث اسم الألبوم', '#16CCC8');
                    switchProfileTab('video-albums');
                }
            }
        }

        // تبديل متابعة المتابعين
        function toggleFollow(followerName) {
            const follower = myProfileData.followersData.find(f => f.name === followerName);
            if (follower) {
                follower.isFollowing = !follower.isFollowing;
                const action = follower.isFollowing ? 'تتابع الآن' : 'تم إلغاء المتابعة';
                showNotification(`${action} ${followerName}`, '#16CCC8');

                // إعادة عرض التبويب إذا كان نشطاً
                if (document.querySelector('.profile-nav-item.active').textContent.includes('المتابعين')) {
                    switchProfileTab('followers');
                }
            }
        }

        // إرسال رسالة اسألني
        function sendAskMessage() {
            const messageType = document.getElementById('messageType').value;
            const messageText = document.getElementById('askMessage').value.trim();

            if (!messageText) {
                showNotification('يرجى كتابة رسالة', '#e74c3c');
                return;
            }

            const typeLabels = {
                question: 'سؤال',
                suggestion: 'اقتراح',
                complaint: 'شكوى',
                other: 'رسالة أخرى'
            };

            // إنشاء رسالة مهمة جديدة
            const importantMessage = {
                id: Date.now(),
                type: messageType,
                typeLabel: typeLabels[messageType],
                content: messageText,
                time: new Date().toLocaleString('ar-SA'),
                isRead: false
            };

            // حفظ الرسالة (يمكن إضافة نظام حفظ حقيقي لاحقاً)
            console.log('رسالة مهمة جديدة:', importantMessage);

            // إظهار رسالة نجاح
            showNotification(`تم إرسال ${typeLabels[messageType]} بنجاح!`, '#2ecc71');

            // مسح النموذج
            document.getElementById('askMessage').value = '';
            document.getElementById('messageType').value = 'question';

            // إغلاق البروفايل بعد ثانيتين
            setTimeout(() => {
                closeMyProfile();
            }, 2000);
        }

        // فتح بروفايل مستخدم آخر (دالة قديمة - محذوفة)
        function openUserProfileOld(userId) {
            const userData = usersData[userId];
            if (!userData) {
                showNotification('لم يتم العثور على بيانات المستخدم', '#e74c3c');
                return;
            }

            // إنشاء مودال البروفايل
            const modal = document.createElement('div');
            modal.className = 'my-profile-modal show';
            modal.id = 'userProfileModal';
            modal.innerHTML = `
                <div class="my-profile-content">
                    <!-- رأس البروفايل -->
                    <div class="my-profile-header">
                        <div class="profile-info-section">
                            <div class="profile-avatar-extra-large">
                                <img src="${userData.avatar}" alt="صورة ${userData.name}">
                            </div>
                            <div class="profile-main-info">
                                <h1 class="profile-name-large">${userData.name}</h1>
                                <p class="profile-bio">${userData.bio || 'لا توجد نبذة متاحة'}</p>
                                <div class="profile-rating-large">
                                    <div class="stars">
                                        ${Array.from({length: 5}, (_, i) =>
                                            `<i class="fas fa-star star ${i < Math.floor(userData.rating) ? '' : 'empty'}"></i>`
                                        ).join('')}
                                    </div>
                                    <span class="rating-text">(${userData.rating})</span>
                                </div>
                                <div class="profile-stats-large">
                                    <div class="stat-item">
                                        <span class="stat-number">${userData.followers}</span>
                                        <span class="stat-label">متابع</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number">${userData.following}</span>
                                        <span class="stat-label">يتابع</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number">${userData.posts}</span>
                                        <span class="stat-label">منشور</span>
                                    </div>
                                </div>
                            </div>
                            <button class="close-profile-btn" onclick="closeUserProfile()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <!-- شريط التنقل -->
                    <div class="profile-nav">
                        <div class="profile-nav-item active" onclick="switchUserProfileTab('posts', '${userId}')">
                            <i class="fas fa-newspaper"></i> المنشورات
                        </div>
                        <div class="profile-nav-item" onclick="switchUserProfileTab('info', '${userId}')">
                            <i class="fas fa-info-circle"></i> المعلومات
                        </div>
                        <div class="profile-nav-item" onclick="switchUserProfileTab('contact', '${userId}')">
                            <i class="fas fa-envelope"></i> التواصل
                        </div>
                    </div>

                    <!-- محتوى البروفايل -->
                    <div class="profile-content-area">
                        <div class="profile-main-content" id="userProfileMainContent">
                            <div class="my-posts-container" id="userPostsContainer">
                                ${userData.userPosts.map(post => `
                                    <div class="my-post-item">
                                        <div class="my-post-header">
                                            <img src="${userData.avatar}" alt="${userData.name}" class="my-post-avatar">
                                            <div class="my-post-info">
                                                <div class="my-post-author" onclick="openUserProfile('${userData.name}')" style="cursor: pointer;">${userData.name}</div>
                                                <div class="my-post-time">${post.time}</div>
                                            </div>
                                        </div>
                                        <div class="my-post-content">${post.content}</div>
                                        ${post.media ? `<div class="my-post-media"><img src="${post.media}" alt="صورة المنشور"></div>` : ''}
                                        <div class="my-post-actions">
                                            <button class="my-post-action ${post.isLiked ? 'liked' : ''}" onclick="toggleUserPostLike('${userId}', ${post.id})">
                                                <i class="fas fa-heart"></i> ${post.likes}
                                            </button>
                                            <button class="my-post-action">
                                                <i class="fas fa-comment"></i> ${post.comments}
                                            </button>
                                            <button class="my-post-action">
                                                <i class="fas fa-share"></i> مشاركة
                                            </button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // إغلاق بروفايل المستخدم
        function closeUserProfile() {
            const modal = document.getElementById('userProfileModal');
            if (modal) {
                modal.remove();
            }
        }

        // تبديل تبويبات بروفايل المستخدم
        function switchUserProfileTab(tab, userId) {
            const userData = usersData[userId];
            if (!userData) return;

            // تحديث التبويبات
            document.querySelectorAll('#userProfileModal .profile-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            const mainContent = document.getElementById('userProfileMainContent');

            if (tab === 'posts') {
                mainContent.innerHTML = `
                    <div class="my-posts-container">
                        ${userData.userPosts.map(post => `
                            <div class="my-post-item">
                                <div class="my-post-header">
                                    <img src="${userData.avatar}" alt="${userData.name}" class="my-post-avatar">
                                    <div class="my-post-info">
                                        <div class="my-post-author" onclick="openUserProfile('${userData.name}')" style="cursor: pointer;">${userData.name}</div>
                                        <div class="my-post-time">${post.time}</div>
                                    </div>
                                </div>
                                <div class="my-post-content">${post.content}</div>
                                ${post.media ? `<div class="my-post-media"><img src="${post.media}" alt="صورة المنشور"></div>` : ''}
                                <div class="my-post-actions">
                                    <button class="my-post-action ${post.isLiked ? 'liked' : ''}" onclick="toggleUserPostLike('${userId}', ${post.id})">
                                        <i class="fas fa-heart"></i> ${post.likes}
                                    </button>
                                    <button class="my-post-action">
                                        <i class="fas fa-comment"></i> ${post.comments}
                                    </button>
                                    <button class="my-post-action">
                                        <i class="fas fa-share"></i> مشاركة
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            } else if (tab === 'info') {
                mainContent.innerHTML = `
                    <div class="info-card">
                        <h3><i class="fas fa-info-circle"></i> معلومات ${userData.name}</h3>
                        <div class="info-item">
                            <i class="fas fa-star"></i>
                            <span>التقييم: <strong>${userData.rating}/5</strong> (${userData.reviewsCount} تقييم)</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-calendar-alt"></i>
                            <span>عضو منذ: <strong>يناير 2020</strong></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-eye"></i>
                            <span>آخر ظهور: <strong>${userData.status}</strong></span>
                        </div>
                        ${userData.bio ? `
                            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                                <h4>نبذة:</h4>
                                <p style="color: #666; line-height: 1.6;">${userData.bio}</p>
                            </div>
                        ` : ''}
                    </div>
                `;
            } else if (tab === 'contact') {
                mainContent.innerHTML = `
                    <div class="info-card">
                        <h3><i class="fas fa-envelope"></i> التواصل مع ${userData.name}</h3>
                        <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 20px;">
                            <button class="album-btn primary" onclick="startChatWith('${userId}')">
                                <i class="fas fa-comment"></i> إرسال رسالة
                            </button>
                            <button class="album-btn secondary" onclick="toggleUserFollow('${userId}')">
                                <i class="fas fa-user-plus"></i> ${userData.isFollowing ? 'إلغاء المتابعة' : 'متابعة'}
                            </button>
                            <button class="album-btn secondary" onclick="reportUser('${userId}')">
                                <i class="fas fa-flag"></i> إبلاغ
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        // تفعيل/إلغاء الإعجاب بمنشور مستخدم
        function toggleUserPostLike(userId, postId) {
            const userData = usersData[userId];
            const post = userData.userPosts.find(p => p.id === postId);
            if (post) {
                if (post.isLiked) {
                    post.likes--;
                    post.isLiked = false;
                } else {
                    post.likes++;
                    post.isLiked = true;
                }
                switchUserProfileTab('posts', userId);
            }
        }

        // بدء محادثة مع مستخدم
        function startChatWith(userId) {
            closeUserProfile();
            showMessages();
            selectConversation(userId);
        }

        // متابعة/إلغاء متابعة مستخدم
        function toggleUserFollow(userId) {
            const userData = usersData[userId];
            userData.isFollowing = !userData.isFollowing;
            const action = userData.isFollowing ? 'تم متابعة' : 'تم إلغاء متابعة';
            showNotification(`${action} ${userData.name}`, '#16CCC8');
            switchUserProfileTab('contact', userId);
        }

        // إبلاغ عن مستخدم
        function reportUser(userId) {
            const userData = usersData[userId];
            showNotification(`تم إرسال بلاغ عن ${userData.name}`, '#e74c3c');
        }

        // عرض جميع الصور
        function viewAllPhotos() {
            const allPhotos = [];
            myProfileData.photoAlbums.forEach(album => {
                album.photos.forEach(photo => {
                    allPhotos.push({
                        url: photo,
                        albumTitle: album.title
                    });
                });
            });

            const modal = document.getElementById('mediaModal');
            const modalTitle = document.getElementById('mediaModalTitle');
            const modalBody = document.getElementById('mediaModalBody');

            modalTitle.textContent = `جميع الصور (${allPhotos.length})`;
            modalBody.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; max-height: 70vh; overflow-y: auto;">
                    ${allPhotos.map(photo => `
                        <div style="position: relative; border-radius: 10px; overflow: hidden; cursor: pointer;" onclick="viewSinglePhoto('${photo.url}')">
                            <img src="${photo.url}" alt="صورة" style="width: 100%; height: 150px; object-fit: cover;">
                            <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 10px; font-size: 12px;">
                                ${photo.albumTitle}
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            modal.classList.add('show');
        }

        // عرض جميع الفيديوهات
        function viewAllVideos() {
            const allVideos = [];
            myProfileData.videoAlbums.forEach(album => {
                album.videos.forEach(video => {
                    allVideos.push({
                        name: video,
                        albumTitle: album.title
                    });
                });
            });

            const modal = document.getElementById('mediaModal');
            const modalTitle = document.getElementById('mediaModalTitle');
            const modalBody = document.getElementById('mediaModalBody');

            modalTitle.textContent = `جميع الفيديوهات (${allVideos.length})`;
            modalBody.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 15px; max-height: 70vh; overflow-y: auto;">
                    ${allVideos.map(video => `
                        <div style="background: #f8f9fa; border-radius: 10px; padding: 20px; text-align: center; border: 2px solid #e1e8ed;">
                            <div style="font-size: 48px; color: #e74c3c; margin-bottom: 15px;">
                                <i class="fas fa-play-circle"></i>
                            </div>
                            <div style="font-weight: 600; color: #333; margin-bottom: 5px;">${video.name}</div>
                            <div style="font-size: 12px; color: #666;">${video.albumTitle}</div>
                        </div>
                    `).join('')}
                </div>
            `;
            modal.classList.add('show');
        }

        // عرض صورة واحدة
        function viewSinglePhoto(photoUrl) {
            const modal = document.getElementById('mediaModal');
            const modalTitle = document.getElementById('mediaModalTitle');
            const modalBody = document.getElementById('mediaModalBody');

            modalTitle.textContent = 'عرض الصورة';
            modalBody.innerHTML = `<img src="${photoUrl}" alt="صورة" style="width: 100%; height: auto; max-height: 80vh; object-fit: contain;">`;
            modal.classList.add('show');
        }

        // متغير لتخزين القسم الحالي
        let currentSection = '';

        // عرض محتوى القسم
        function renderSectionContent(sectionName) {
            const section = myProfileData.companyInfoSections[sectionName];
            if (!section || section.content.length === 0) {
                return '<div class="empty-section">لا يوجد محتوى في هذا القسم بعد</div>';
            }

            return section.content.map(item => `
                <div class="content-item">
                    <div class="content-header">
                        <div class="content-date">${item.date}</div>
                        <div class="content-actions">
                            <button class="content-action-btn" onclick="editContent('${sectionName}', ${item.id})">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="content-action-btn" onclick="deleteContent('${sectionName}', ${item.id})">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                    <div class="content-text">${item.content}</div>
                    ${item.media ? `
                        <div class="content-media">
                            ${item.media.type === 'image' ?
                                `<img src="${item.media.url}" alt="صورة المحتوى" onclick="viewSinglePhoto('${item.media.url}')">` :
                                `<video controls><source src="${item.media.url}" type="video/mp4"></video>`
                            }
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }

        // إظهار نافذة إضافة المحتوى
        function showAddContentModal(sectionName) {
            currentSection = sectionName;
            const modal = document.getElementById('addContentModal');
            const title = document.getElementById('addContentTitle');

            const sectionTitles = {
                'biography': 'إضافة محتوى للسيرة الذاتية',
                'documents': 'إضافة محتوى للصور والوثائق',
                'achievements': 'إضافة محتوى للشهادات والإنجازات'
            };

            title.innerHTML = `<i class="fas fa-plus"></i> ${sectionTitles[sectionName]}`;

            // إعادة تعيين النموذج
            document.getElementById('contentType').value = 'text';
            document.getElementById('contentText').value = '';
            document.getElementById('mediaUploadGroup').style.display = 'none';
            document.getElementById('selectedContentPreview').innerHTML = '';

            modal.classList.add('show');
        }

        // إغلاق نافذة إضافة المحتوى
        function closeAddContentModal() {
            document.getElementById('addContentModal').classList.remove('show');
        }

        // تبديل عرض رفع الوسائط
        function toggleMediaUpload() {
            const contentType = document.getElementById('contentType').value;
            const mediaGroup = document.getElementById('mediaUploadGroup');

            if (contentType === 'media') {
                mediaGroup.style.display = 'block';
            } else {
                mediaGroup.style.display = 'none';
            }
        }

        // اختيار وسائط للمحتوى
        function selectContentMedia() {
            document.getElementById('contentMediaFile').click();
        }

        // إضافة المحتوى للقسم
        function addContentToSection() {
            const contentText = document.getElementById('contentText').value.trim();
            const contentType = document.getElementById('contentType').value;
            const mediaFile = document.getElementById('contentMediaFile').files[0];

            if (!contentText) {
                showNotification('يرجى إدخال المحتوى', '#e74c3c');
                return;
            }

            const newContent = {
                id: Date.now(),
                type: contentType,
                content: contentText,
                date: new Date().toLocaleDateString('ar-SA'),
                media: null
            };

            if (contentType === 'media' && mediaFile) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    newContent.media = {
                        type: mediaFile.type.startsWith('image/') ? 'image' : 'video',
                        url: e.target.result
                    };

                    // إضافة المحتوى
                    myProfileData.companyInfoSections[currentSection].content.push(newContent);
                    showNotification('تم إضافة المحتوى بنجاح', '#16CCC8');
                    closeAddContentModal();
                    switchProfileTab('info');
                };
                reader.readAsDataURL(mediaFile);
            } else {
                // إضافة المحتوى بدون وسائط
                myProfileData.companyInfoSections[currentSection].content.push(newContent);
                showNotification('تم إضافة المحتوى بنجاح', '#16CCC8');
                closeAddContentModal();
                switchProfileTab('info');
            }
        }

        // تحديث خصوصية القسم
        function updateSectionPrivacy(sectionName, privacy) {
            myProfileData.companyInfoSections[sectionName].privacy = privacy;
            showNotification('تم تحديث إعدادات الخصوصية', '#16CCC8');
        }

        // تعديل المحتوى
        function editContent(sectionName, contentId) {
            const content = myProfileData.companyInfoSections[sectionName].content.find(c => c.id === contentId);
            if (content) {
                const newText = prompt('تعديل المحتوى:', content.content);
                if (newText && newText.trim()) {
                    content.content = newText.trim();
                    showNotification('تم تحديث المحتوى', '#16CCC8');
                    switchProfileTab('info');
                }
            }
        }

        // حذف المحتوى
        function deleteContent(sectionName, contentId) {
            if (confirm('هل أنت متأكد من حذف هذا المحتوى؟')) {
                const section = myProfileData.companyInfoSections[sectionName];
                section.content = section.content.filter(c => c.id !== contentId);
                showNotification('تم حذف المحتوى', '#e74c3c');
                switchProfileTab('info');
            }
        }

        // تبديل قائمة الألبوم
        function toggleAlbumMenu(albumId, type) {
            const menuId = `${type}AlbumMenu${albumId}`;
            const menu = document.getElementById(menuId);

            // إغلاق جميع القوائم الأخرى
            document.querySelectorAll('.album-dropdown').forEach(dropdown => {
                if (dropdown.id !== menuId) {
                    dropdown.classList.remove('show');
                }
            });

            menu.classList.toggle('show');
        }

        // تبديل قائمة الخصوصية
        function togglePrivacyMenu(albumId, type) {
            const menuId = `${type}PrivacyMenu${albumId}`;
            const menu = document.getElementById(menuId);

            // إغلاق جميع القوائم الأخرى
            document.querySelectorAll('.privacy-dropdown').forEach(dropdown => {
                if (dropdown.id !== menuId) {
                    dropdown.classList.remove('show');
                }
            });

            menu.classList.toggle('show');
        }

        // تعيين خصوصية الألبوم
        function setAlbumPrivacy(albumId, type, privacy) {
            const albums = type === 'photo' ? myProfileData.photoAlbums : myProfileData.videoAlbums;
            const album = albums.find(a => a.id === albumId);

            if (album) {
                album.privacy = privacy;

                const privacyNames = {
                    'public': 'عامة',
                    'followers': 'المتابعون',
                    'followers_of_followers': 'متابعو المتابعين',
                    'private': 'أنا فقط'
                };

                showNotification(`تم تغيير خصوصية الألبوم إلى: ${privacyNames[privacy]}`, '#16CCC8');

                // إغلاق القائمة
                document.getElementById(`${type}PrivacyMenu${albumId}`).classList.remove('show');
            }
        }

        // حذف ألبوم صور
        function deletePhotoAlbum(albumId) {
            if (confirm('هل أنت متأكد من حذف هذا الألبوم؟ سيتم حذف جميع الصور بداخله.')) {
                myProfileData.photoAlbums = myProfileData.photoAlbums.filter(album => album.id !== albumId);
                showNotification('تم حذف الألبوم', '#e74c3c');
                switchProfileTab('photo-albums');
            }
        }

        // حذف ألبوم فيديوهات
        function deleteVideoAlbum(albumId) {
            if (confirm('هل أنت متأكد من حذف هذا الألبوم؟ سيتم حذف جميع الفيديوهات بداخله.')) {
                myProfileData.videoAlbums = myProfileData.videoAlbums.filter(album => album.id !== albumId);
                showNotification('تم حذف الألبوم', '#e74c3c');
                switchProfileTab('video-albums');
            }
        }

        // إغلاق القوائم عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.album-menu') && !e.target.closest('.album-dropdown')) {
                document.querySelectorAll('.album-dropdown').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }

            if (!e.target.closest('.privacy-control') && !e.target.closest('.privacy-dropdown')) {
                document.querySelectorAll('.privacy-dropdown').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });

        // فتح البروفايل الشخصي للمستخدمين
        function openUserProfile(userName) {
            console.log('فتح بروفايل:', userName); // للتأكد من عمل الوظيفة

            if (userName === 'myProfile') {
                showMyProfile();
                return;
            }

            // تجاهل النقر على "أنت"
            if (userName === 'أنت') {
                showMyProfile();
                return;
            }

            // البحث عن المستخدم في قوائم مختلفة
            let user = null;

            // البحث في قائمة المتابعين
            user = myProfileData.followersData.find(f => f.name === userName);

            // البحث في قائمة المحادثات
            if (!user) {
                user = conversations.find(c => c.name === userName || c.id === userName);
            }

            // البحث في المنشورات المميزة
            if (!user) {
                const post = trendingPosts.find(p => p.author === userName);
                if (post) {
                    user = {
                        name: post.author,
                        avatar: 'images/Hussein Nihad.png',
                        category: post.category
                    };
                }
            }

            // إنشاء مستخدم افتراضي إذا لم يتم العثور عليه
            if (!user) {
                user = {
                    name: userName,
                    avatar: 'images/Hussein Nihad.png'
                };
            }

            // إنشاء بيانات مخصصة للمستخدم
            let bio = 'مستخدم في تطبيق Get Me';
            let work = 'يعمل في مجال التكنولوجيا';
            let myPosts = [];

            // تخصيص البيانات حسب نوع المستخدم
            if (user.name.includes('الإلكترونيات') || user.name.includes('إلكترونيات')) {
                bio = 'متجر متخصص في بيع أحدث الأجهزة الإلكترونية والهواتف الذكية';
                work = 'تجارة الإلكترونيات والهواتف الذكية';
                myPosts = [
                    {
                        id: 1,
                        content: 'عرض خاص على أحدث الهواتف الذكية! خصم يصل إلى 40% على جميع الموديلات الجديدة مع ضمان سنتين كاملتين.',
                        time: 'منذ يوم',
                        likes: 1250,
                        comments: 89,
                        shares: 45,
                        isLiked: false
                    },
                    {
                        id: 2,
                        content: 'وصلت شحنة جديدة من أجهزة اللابتوب والتابلت بأسعار منافسة جداً. تعالوا وشوفوا بأنفسكم!',
                        time: 'منذ 3 أيام',
                        likes: 890,
                        comments: 67,
                        shares: 23,
                        isLiked: false
                    }
                ];
            } else {
                myPosts = [
                    {
                        id: 1,
                        content: 'مرحباً بكم في صفحتي الشخصية!',
                        time: 'منذ يوم',
                        likes: Math.floor(Math.random() * 100) + 10,
                        comments: Math.floor(Math.random() * 20) + 5,
                        shares: Math.floor(Math.random() * 10) + 2,
                        isLiked: false
                    }
                ];
            }

            const userData = {
                name: user.name,
                bio: bio,
                avatar: user.avatar || 'images/Hussein Nihad.png',
                followers: Math.floor(Math.random() * 1000) + 100,
                following: Math.floor(Math.random() * 500) + 50,
                posts: myPosts.length,
                rating: (Math.random() * 2 + 3).toFixed(1),
                work: work,
                education: 'جامعة بغداد',
                location: 'بغداد، العراق',
                joinDate: 'يناير 2021',
                myPosts: myPosts
            };

            // عرض البروفايل باستخدام modal الأصلي
            const modal = document.getElementById('myProfileModal');
            const modalContent = modal.querySelector('.my-profile-content');

            // تحديد الصورة المناسبة
            let userAvatar = userData.avatar;
            if (userData.name.includes('الإلكترونيات') || userData.name.includes('إلكترونيات') || userData.name.includes('الذكية')) {
                userAvatar = 'assets/images/اليكترونيات2.jpg';
            }

            modalContent.innerHTML = `
                <div class="my-profile-header">
                    <button class="close-profile-btn" onclick="closeMyProfile()">
                        <i class="fas fa-times"></i>
                    </button>

                    <!-- معلومات البروفايل في الجزء الملون -->
                    <div class="profile-info-colored-full">
                        <!-- صورة البروفايل في الأعلى -->
                        <div class="profile-avatar-top-center">
                            <div class="profile-avatar-extra-large online">
                                <img src="${userAvatar}" alt="صورة ${userData.name}">
                            </div>
                        </div>

                        <h1 class="profile-name-white">${userData.name}</h1>

                        <!-- النجوم - تقييم المستخدم -->
                        <div class="profile-rating-stars-colored">
                            <div class="stars-container-white">
                                ${Array.from({length: 5}, (_, i) =>
                                    `<i class="fas fa-star star-yellow-large ${i < Math.floor(userData.rating) ? 'filled' : 'empty'}"></i>`
                                ).join('')}
                            </div>
                            <div class="rating-text-white">(${userData.rating} من 5)</div>
                        </div>

                        <!-- الإحصائيات المتباعدة -->
                        <div class="profile-stats-colored">
                            <div class="stat-item-colored">
                                <span class="stat-number-white">${userData.followers}</span>
                                <span class="stat-label-white">متابع</span>
                            </div>
                            <div class="stat-item-colored">
                                <span class="stat-number-white">${userData.following}</span>
                                <span class="stat-label-white">يتابع</span>
                            </div>
                            <div class="stat-item-colored">
                                <span class="stat-number-white">${userData.posts}</span>
                                <span class="stat-label-white">منشور</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شريط التنقل -->
                <div class="nav-container">
                    <div class="nav-item active" onclick="switchUserTab('posts', '${userData.name}')">
                        <i class="fas fa-newspaper"></i>
                        <span>المنشورات</span>
                    </div>
                    <div class="nav-item" onclick="switchUserTab('info', '${userData.name}')">
                        <i class="fas fa-info-circle"></i>
                        <span>المعلومات</span>
                    </div>
                    <div class="nav-item" onclick="switchUserTab('contact', '${userData.name}')">
                        <i class="fas fa-envelope"></i>
                        <span>التواصل</span>
                    </div>
                </div>

                <!-- المحتوى -->
                <div class="main-content" id="userProfileContent">
                    <div class="my-posts-container">
                        ${userData.myPosts.map(post => `
                            <div class="trending-post">
                                <div class="trending-post-header">
                                    <div class="trending-post-avatar">
                                        <img src="${userAvatar}" alt="${userData.name}">
                                    </div>
                                    <div class="trending-post-info">
                                        <div class="trending-post-author">${userData.name}</div>
                                        <div class="trending-post-time">${post.time}</div>
                                    </div>
                                </div>
                                <div class="trending-post-content">${post.content}</div>
                                <div class="trending-post-actions">
                                    <button class="trending-action ${post.isLiked ? 'liked' : ''}">
                                        <i class="fas fa-heart"></i> ${post.likes}
                                    </button>
                                    <button class="trending-action">
                                        <i class="fas fa-comment"></i> ${post.comments}
                                    </button>
                                    <button class="trending-action">
                                        <i class="fas fa-share"></i> ${post.shares}
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            modal.classList.add('show');
        }

        // فتح البروفايل بالمعرف (للمحادثات)
        function openUserProfileById(userId) {
            const conversation = conversations.find(c => c.id === userId);
            if (conversation) {
                openUserProfile(conversation.name);
            } else {
                openUserProfile(userId);
            }
        }






        // إغلاق بروفايل المستخدم
        function closeUserProfile() {
            const modal = document.getElementById('userProfileModal');
            if (modal) {
                modal.remove();
            }
        }

        // تبديل تبويبات المستخدم
        function switchUserTab(tab, userName) {
            // تحديث التبويبات النشطة
            document.querySelectorAll('#userProfileModal .nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            const content = document.getElementById('userProfileContent');

            if (tab === 'posts') {
                // المنشورات تظهر بالفعل في التصميم الأساسي
                return;
            } else if (tab === 'info') {
                content.innerHTML = `
                    <div class="info-card">
                        <h3><i class="fas fa-info-circle"></i> معلومات ${userName}</h3>
                        <div class="info-item">
                            <i class="fas fa-briefcase"></i>
                            <span>يعمل في <strong>مجال التكنولوجيا</strong></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-graduation-cap"></i>
                            <span>درس في <strong>جامعة بغداد</strong></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>يعيش في <strong>بغداد، العراق</strong></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-calendar-alt"></i>
                            <span>انضم في <strong>يناير 2021</strong></span>
                        </div>
                    </div>
                `;
            } else if (tab === 'contact') {
                content.innerHTML = `
                    <div class="info-card">
                        <h3><i class="fas fa-envelope"></i> التواصل مع ${userName}</h3>
                        <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 20px;">
                            <button class="album-btn primary" onclick="startChatWith('${userName}')">
                                <i class="fas fa-comment"></i> إرسال رسالة
                            </button>
                            <button class="album-btn secondary" onclick="followUser('${userName}')">
                                <i class="fas fa-user-plus"></i> متابعة
                            </button>
                            <button class="album-btn secondary" onclick="reportUser('${userName}')">
                                <i class="fas fa-flag"></i> إبلاغ
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        // تفعيل/إلغاء الإعجاب بمنشورات المستخدمين
        function toggleUserPostLike(userName, postId) {
            showNotification('تم الإعجاب بالمنشور', '#16CCC8');
        }

        // بدء محادثة مع مستخدم
        function startChatWith(userName) {
            closeUserProfile();
            showMessages();
            showNotification(`تم فتح محادثة مع ${userName}`, '#16CCC8');
        }

        // متابعة مستخدم
        function followUser(userName) {
            showNotification(`تم متابعة ${userName}`, '#4CAF50');
        }

        // إبلاغ عن مستخدم
        function reportUser(userName) {
            if (confirm(`هل أنت متأكد من الإبلاغ عن ${userName}؟`)) {
                showNotification(`تم إرسال بلاغ عن ${userName}`, '#e74c3c');
            }
        }


    </script>
</body>
</html>
