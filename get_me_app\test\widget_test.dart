// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:get_me_app/main.dart';

void main() {
  testWidgets('Get Me App smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const GetMeApp());

    // Verify that the app loads successfully.
    expect(find.byType(GetMeApp), findsOneWidget);
  });
}
