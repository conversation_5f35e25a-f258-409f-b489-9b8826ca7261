import 'dart:ui';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_login/flutter_login.dart';
import '../models/user.dart';
import 'main_screen.dart';

class ModernLoginScreen extends StatefulWidget {
  const ModernLoginScreen({super.key});

  @override
  State<ModernLoginScreen> createState() => _ModernLoginScreenState();
}

class _ModernLoginScreenState extends State<ModernLoginScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 30),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    super.dispose();
  }

  Duration get loginTime => const Duration(milliseconds: 2250);

  Future<String?> _authUser(LoginData data) async {
    debugPrint('Name: ${data.name}, Password: ${data.password}');
    
    // Simulate network delay
    await Future.delayed(loginTime);
    
    // Simple validation for demo
    if (data.name == 'admin' && data.password == '123456') {
      return null; // Success
    }
    
    return 'اسم المستخدم أو كلمة المرور غير صحيحة';
  }

  Future<String?> _signupUser(SignupData data) async {
    debugPrint('Signup Name: ${data.name}, Password: ${data.password}');
    
    // Simulate network delay
    await Future.delayed(loginTime);
    
    // For demo, always succeed
    return null;
  }

  Future<String?> _recoverPassword(String name) async {
    debugPrint('Name: $name');
    
    // Simulate network delay
    await Future.delayed(loginTime);
    
    // For demo, always succeed
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          _buildAnimatedBackground(),
          FlutterLogin(
            title: 'Get Me',
            logo: const AssetImage('assets/images/logo.png'),
            onLogin: _authUser,
            onSignup: _signupUser,
            onSubmitAnimationCompleted: () {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => MainScreen(currentUser: User.demo()),
                ),
              );
            },
            onRecoverPassword: _recoverPassword,
            theme: LoginTheme(
              primaryColor: const Color(0xFF667eea),
              accentColor: const Color(0xFF764ba2),
              errorColor: Colors.red[400]!,
              titleStyle: const TextStyle(
                color: Colors.white,
                fontFamily: 'Cairo',
                letterSpacing: 4,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
              bodyStyle: const TextStyle(
                fontFamily: 'Cairo',
                color: Colors.white70,
              ),
              textFieldStyle: const TextStyle(
                fontFamily: 'Cairo',
                color: Colors.white,
              ),
              buttonStyle: const TextStyle(
                fontFamily: 'Cairo',
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              cardTheme: CardTheme(
                color: Colors.white.withOpacity(0.1),
                elevation: 20,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                  side: BorderSide(
                    color: Colors.white.withOpacity(0.3),
                    width: 1.5,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(24),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white.withOpacity(0.25),
                            Colors.white.withOpacity(0.1),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              inputTheme: InputDecorationTheme(
                filled: true,
                fillColor: Colors.white.withOpacity(0.1),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide(
                    color: Colors.white.withOpacity(0.3),
                    width: 1.5,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide(
                    color: Colors.white.withOpacity(0.3),
                    width: 1.5,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: const BorderSide(
                    color: Colors.white,
                    width: 2,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide(
                    color: Colors.red[400]!,
                    width: 2,
                  ),
                ),
                labelStyle: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontFamily: 'Cairo',
                ),
                hintStyle: TextStyle(
                  color: Colors.white.withOpacity(0.6),
                  fontFamily: 'Cairo',
                ),
              ),
              buttonTheme: LoginButtonTheme(
                splashColor: Colors.white.withOpacity(0.2),
                backgroundColor: const Color(0xFF667eea),
                highlightColor: const Color(0xFF764ba2),
                elevation: 8,
                highlightElevation: 12,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
            userValidator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال اسم المستخدم';
              }
              return null;
            },
            passwordValidator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال كلمة المرور';
              }
              if (value.length < 6) {
                return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
              }
              return null;
            },
            messages: LoginMessages(
              userHint: 'اسم المستخدم',
              passwordHint: 'كلمة المرور',
              confirmPasswordHint: 'تأكيد كلمة المرور',
              loginButton: 'تسجيل الدخول',
              signupButton: 'إنشاء حساب',
              forgotPasswordButton: 'نسيت كلمة المرور؟',
              recoverPasswordButton: 'استعادة',
              goBackButton: 'رجوع',
              confirmPasswordError: 'كلمات المرور غير متطابقة',
              recoverPasswordDescription: 'سنرسل لك رابط استعادة كلمة المرور',
              recoverPasswordSuccess: 'تم إرسال رابط الاستعادة بنجاح',
            ),
            userType: LoginUserType.name,
            initialAuthMode: AuthMode.login,
            loginAfterSignUp: true,
            hideForgotPasswordButton: false,
            hideSignUpButton: false,
            disableCustomPageTransformer: false,
            children: [
              // Add demo credentials info
              Container(
                margin: const EdgeInsets.only(top: 20),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white.withOpacity(0.1),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      'بيانات تجريبية',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اسم المستخدم: admin\nكلمة المرور: 123456',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF667eea),
            Color(0xFF764ba2),
            Color(0xFF667eea),
          ],
          stops: [0.0, 0.5, 1.0],
        ),
      ),
      child: AnimatedBuilder(
        animation: _backgroundController,
        builder: (context, child) {
          return CustomPaint(
            painter: LoginBackgroundPainter(_backgroundController.value),
            size: Size.infinite,
          );
        },
      ),
    );
  }
}

class LoginBackgroundPainter extends CustomPainter {
  final double animationValue;

  LoginBackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final glowPaint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke
      ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 3);

    // Draw animated waves
    for (int i = 0; i < 4; i++) {
      final path = Path();
      final amplitude = 30.0 + i * 15;
      final frequency = 0.015 + i * 0.005;
      final phase = animationValue * 2 * math.pi + i * 1.2;
      
      path.moveTo(0, size.height * 0.2 + i * size.height * 0.15);
      
      for (double x = 0; x <= size.width; x += 3) {
        final y = size.height * 0.2 + i * size.height * 0.15 + 
                  amplitude * math.sin(x * frequency + phase);
        path.lineTo(x, y);
      }
      
      if (i % 2 == 0) {
        canvas.drawPath(path, glowPaint);
      }
      canvas.drawPath(path, paint);
    }

    // Draw floating particles
    final particlePaint = Paint()
      ..color = Colors.white.withOpacity(0.6)
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 15; i++) {
      final x = (size.width * 0.05 + i * size.width * 0.06 + 
                animationValue * 40 + i * 25) % (size.width + 50);
      final y = (size.height * 0.1 + i * size.height * 0.05 + 
                math.sin(animationValue * 2 * math.pi + i * 0.8) * 50) % size.height;
      
      final radius = 1.5 + math.sin(animationValue * 3 * math.pi + i) * 1;
      canvas.drawCircle(Offset(x, y), radius, particlePaint);
    }
  }

  @override
  bool shouldRepaint(LoginBackgroundPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}
