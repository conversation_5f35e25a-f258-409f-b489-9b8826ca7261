import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import '../models/subscription_plan.dart';

class BasicInfoScreen extends StatefulWidget {
  final UserRegistrationData registrationData;
  final VoidCallback onNext;

  const BasicInfoScreen({
    super.key,
    required this.registrationData,
    required this.onNext,
  });

  @override
  State<BasicInfoScreen> createState() => _BasicInfoScreenState();
}

class _BasicInfoScreenState extends State<BasicInfoScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _locationController = TextEditingController();
  
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    _nameController.text = widget.registrationData.fullName ?? '';
    _phoneController.text = widget.registrationData.phone ?? '';
    _emailController.text = widget.registrationData.email ?? '';
    _locationController.text = widget.registrationData.location ?? '';
    _selectedDate = widget.registrationData.birthDate;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 32),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        _buildNameField(),
                        const SizedBox(height: 20),
                        _buildDateField(),
                        const SizedBox(height: 20),
                        _buildPhoneField(),
                        const SizedBox(height: 20),
                        _buildEmailField(),
                        const SizedBox(height: 20),
                        _buildLocationField(),
                        const SizedBox(height: 40),
                      ],
                    ),
                  ),
                ),
                _buildContinueButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    widget.registrationData.selectedPlan?.primaryColor ??
                        const Color(0xFF667eea),
                    widget.registrationData.selectedPlan?.secondaryColor ??
                        const Color(0xFF764ba2),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                widget.registrationData.selectedPlan?.icon ?? Icons.person,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.registrationData.selectedPlan?.title ?? 'تسجيل جديد',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1C1E21),
                    ),
                  ),
                  Text(
                    'البيانات الأساسية',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          'أدخل بياناتك الشخصية الأساسية',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }

  Widget _buildNameField() {
    return GFCard(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      content: TextFormField(
        controller: _nameController,
        decoration: const InputDecoration(
          labelText: 'الاسم الكامل',
          prefixIcon: Icon(Icons.person_outline),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'يرجى إدخال الاسم الكامل';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildDateField() {
    return GFCard(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      content: InkWell(
        onTap: _selectDate,
        child: InputDecorator(
          decoration: const InputDecoration(
            labelText: 'تاريخ الميلاد',
            prefixIcon: Icon(Icons.calendar_today_outlined),
            border: InputBorder.none,
            contentPadding: EdgeInsets.all(16),
          ),
          child: Text(
            _selectedDate != null
                ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                : 'اختر تاريخ الميلاد',
            style: TextStyle(
              color: _selectedDate != null ? Colors.black : Colors.grey[600],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPhoneField() {
    return GFCard(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      content: TextFormField(
        controller: _phoneController,
        keyboardType: TextInputType.phone,
        decoration: const InputDecoration(
          labelText: 'رقم الهاتف',
          prefixIcon: Icon(Icons.phone_outlined),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'يرجى إدخال رقم الهاتف';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildEmailField() {
    return GFCard(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      content: TextFormField(
        controller: _emailController,
        keyboardType: TextInputType.emailAddress,
        decoration: const InputDecoration(
          labelText: 'البريد الإلكتروني',
          prefixIcon: Icon(Icons.email_outlined),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'يرجى إدخال البريد الإلكتروني';
          }
          if (!value.contains('@')) {
            return 'يرجى إدخال بريد إلكتروني صحيح';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildLocationField() {
    return GFCard(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      content: TextFormField(
        controller: _locationController,
        decoration: InputDecoration(
          labelText: 'الموقع',
          prefixIcon: const Icon(Icons.location_on_outlined),
          suffixIcon: IconButton(
            icon: const Icon(Icons.my_location),
            onPressed: _getCurrentLocation,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'يرجى إدخال الموقع';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildContinueButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _handleContinue,
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.registrationData.selectedPlan?.primaryColor ??
              const Color(0xFF667eea),
          foregroundColor: Colors.white,
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          textStyle: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        child: const Text('متابعة'),
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now().subtract(const Duration(days: 6570)), // 18 years ago
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _getCurrentLocation() {
    // TODO: Implement GPS location
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة تحديد الموقع قيد التطوير'),
        backgroundColor: Color(0xFF667eea),
      ),
    );
  }

  void _handleContinue() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار تاريخ الميلاد'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Save data
    widget.registrationData.fullName = _nameController.text;
    widget.registrationData.birthDate = _selectedDate;
    widget.registrationData.phone = _phoneController.text;
    widget.registrationData.email = _emailController.text;
    widget.registrationData.location = _locationController.text;

    widget.onNext();
  }
}
