import 'package:flutter/material.dart';

// نموذج طلب الحجز
class BookingRequest {
  final String id;
  final String postId;
  final String postTitle;
  final String customerName;
  final String customerPhone;
  final String customerEmail;
  final String customerProfileImage;
  final String message;
  final DateTime requestDate;
  final BookingStatus status;
  final double? offeredPrice;

  BookingRequest({
    required this.id,
    required this.postId,
    required this.postTitle,
    required this.customerName,
    required this.customerPhone,
    required this.customerEmail,
    required this.customerProfileImage,
    required this.message,
    required this.requestDate,
    required this.status,
    this.offeredPrice,
  });

  static List<BookingRequest> getDemoRequests() {
    return [
      BookingRequest(
        id: '1',
        postId: 'post_1',
        postTitle: 'شقة للإيجار - 3 غرف وصالة',
        customerName: 'أحمد محمد',
        customerPhone: '+964 ************',
        customerEmail: '<EMAIL>',
        customerProfileImage: 'assets/images/Hussein <PERSON>.png',
        message: 'مهتم بالشقة، هل يمكن ترتيب موعد للمعاينة؟',
        requestDate: DateTime.now().subtract(const Duration(hours: 2)),
        status: BookingStatus.pending,
        offeredPrice: 800000,
      ),
      BookingRequest(
        id: '2',
        postId: 'post_1',
        postTitle: 'شقة للإيجار - 3 غرف وصالة',
        customerName: 'فاطمة علي',
        customerPhone: '+964 ************',
        customerEmail: '<EMAIL>',
        customerProfileImage: 'assets/images/Hussein Nihad.png',
        message: 'أريد استئجار الشقة، متى يمكنني الانتقال؟',
        requestDate: DateTime.now().subtract(const Duration(hours: 5)),
        status: BookingStatus.confirmed,
        offeredPrice: 850000,
      ),
      BookingRequest(
        id: '3',
        postId: 'post_2',
        postTitle: 'محل تجاري للإيجار - شارع الرشيد',
        customerName: 'محمد حسن',
        customerPhone: '+964 ************',
        customerEmail: '<EMAIL>',
        customerProfileImage: 'assets/images/Hussein Nihad.png',
        message: 'مهتم بالمحل لفتح مطعم صغير',
        requestDate: DateTime.now().subtract(const Duration(days: 1)),
        status: BookingStatus.pending,
        offeredPrice: 1200000,
      ),
      BookingRequest(
        id: '4',
        postId: 'post_3',
        postTitle: 'خدمة توصيل طعام',
        customerName: 'سارة أحمد',
        customerPhone: '+964 ************',
        customerEmail: '<EMAIL>',
        customerProfileImage: 'assets/images/Hussein Nihad.png',
        message: 'أحتاج خدمة توصيل يومية للمكتب',
        requestDate: DateTime.now().subtract(const Duration(hours: 8)),
        status: BookingStatus.rejected,
      ),
    ];
  }
}

enum BookingStatus {
  pending,
  confirmed,
  rejected,
  completed,
}

extension BookingStatusExtension on BookingStatus {
  String get displayName {
    switch (this) {
      case BookingStatus.pending:
        return 'في الانتظار';
      case BookingStatus.confirmed:
        return 'مؤكد';
      case BookingStatus.rejected:
        return 'مرفوض';
      case BookingStatus.completed:
        return 'مكتمل';
    }
  }

  Color get color {
    switch (this) {
      case BookingStatus.pending:
        return Colors.orange;
      case BookingStatus.confirmed:
        return Colors.green;
      case BookingStatus.rejected:
        return Colors.red;
      case BookingStatus.completed:
        return Colors.blue;
    }
  }

  IconData get icon {
    switch (this) {
      case BookingStatus.pending:
        return Icons.schedule;
      case BookingStatus.confirmed:
        return Icons.check_circle;
      case BookingStatus.rejected:
        return Icons.cancel;
      case BookingStatus.completed:
        return Icons.done_all;
    }
  }
}

// نموذج التقرير المالي
class FinancialReport {
  final String id;
  final DateTime startDate;
  final DateTime endDate;
  final double totalRevenue;
  final double totalExpenses;
  final double netProfit;
  final int totalOrders;
  final List<ExpenseItem> expenses;
  final List<RevenueItem> revenues;

  FinancialReport({
    required this.id,
    required this.startDate,
    required this.endDate,
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netProfit,
    required this.totalOrders,
    required this.expenses,
    required this.revenues,
  });

  double get profitMargin => totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

  static FinancialReport getWeeklyReport() {
    final now = DateTime.now();
    final startDate = now.subtract(const Duration(days: 7));
    
    return FinancialReport(
      id: 'weekly_${now.millisecondsSinceEpoch}',
      startDate: startDate,
      endDate: now,
      totalRevenue: 15000,
      totalExpenses: 8000,
      netProfit: 7000,
      totalOrders: 25,
      expenses: ExpenseItem.getDemoExpenses(),
      revenues: RevenueItem.getDemoRevenues(),
    );
  }

  static FinancialReport getMonthlyReport() {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month - 1, now.day);
    
    return FinancialReport(
      id: 'monthly_${now.millisecondsSinceEpoch}',
      startDate: startDate,
      endDate: now,
      totalRevenue: 65000,
      totalExpenses: 35000,
      netProfit: 30000,
      totalOrders: 120,
      expenses: ExpenseItem.getDemoExpenses(),
      revenues: RevenueItem.getDemoRevenues(),
    );
  }
}

class ExpenseItem {
  final String category;
  final double amount;
  final String description;

  ExpenseItem({
    required this.category,
    required this.amount,
    required this.description,
  });

  static List<ExpenseItem> getDemoExpenses() {
    return [
      ExpenseItem(category: 'إعلانات', amount: 2000, description: 'إعلانات فيسبوك وإنستغرام'),
      ExpenseItem(category: 'صيانة', amount: 1500, description: 'صيانة المعدات'),
      ExpenseItem(category: 'مواصلات', amount: 800, description: 'تكاليف التنقل'),
      ExpenseItem(category: 'أخرى', amount: 700, description: 'مصاريف متنوعة'),
    ];
  }
}

class RevenueItem {
  final String source;
  final double amount;
  final String description;

  RevenueItem({
    required this.source,
    required this.amount,
    required this.description,
  });

  static List<RevenueItem> getDemoRevenues() {
    return [
      RevenueItem(source: 'إيجارات', amount: 8000, description: 'إيجار الشقق والمحلات'),
      RevenueItem(source: 'مبيعات', amount: 4500, description: 'مبيعات المنتجات'),
      RevenueItem(source: 'خدمات', amount: 2500, description: 'رسوم الخدمات'),
    ];
  }
}
