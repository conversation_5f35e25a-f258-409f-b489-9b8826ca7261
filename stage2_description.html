<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - المرحلة الثانية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            overflow: hidden;
        }

        /* Description Screen */
        .description-screen {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6), rgba(0,0,0,0.8)),
                        url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            animation: fadeIn 2s ease-in-out;
            padding: 40px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .description-content {
            animation: slideUp 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            max-width: 500px;
            width: 100%;
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
            text-align: center;
            margin: 0 auto;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(100px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .app-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            overflow: hidden;
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .app-logo img {
            width: 110%;
            height: 110%;
            object-fit: cover;
            border-radius: 20px;
        }

        .app-logo i {
            color: white;
            font-size: 50px;
        }

        .app-description {
            font-size: 18px;
            color: rgba(255,255,255,0.7);
            line-height: 1.5;
            margin-bottom: 60px;
            text-align: center;
            font-weight: 300;
        }

        .app-name-highlight {
            font-weight: bold;
        }

        .app-title {
            font-size: 48px;
            font-weight: bold;
            color: white;
            text-align: center;
            margin-bottom: 30px;
            letter-spacing: 2px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.5);
        }

        .start-button {
            background: linear-gradient(135deg, #667eea, #764ba2, #667eea);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            padding: 12px 24px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-family: 'Cairo', sans-serif;
            margin: 0 auto;
        }

        .start-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .description-screen {
                justify-content: center;
                padding: 20px;
            }

            .description-content {
                margin: 0 auto;
                max-width: 90%;
                padding: 30px;
            }

            .app-description {
                font-size: 16px;
                margin-bottom: 40px;
            }

            .start-button {
                padding: 14px 28px;
                font-size: 16px;
            }

            .app-title {
                font-size: 36px;
                margin-bottom: 25px;
            }
        }

        @media (max-width: 480px) {
            .description-content {
                padding: 25px;
                max-width: 95%;
            }

            .app-description {
                font-size: 14px;
                line-height: 1.6;
                margin-bottom: 35px;
            }

            .app-title {
                font-size: 28px;
                margin-bottom: 20px;
            }

            .start-button {
                padding: 12px 24px;
                font-size: 14px;
            }
        }


    </style>
</head>
<body>
    <!-- Description Screen -->
    <div class="description-screen">
        <div class="description-content">
            <h1 class="app-title">Get Me</h1>
            
            <p class="app-description">
                <span class="app-name-highlight">"تطبيق Get Me"</span> هو منصة متطورة تربط المشتري مباشرة بالبائع، مما يلغي الوسطاء ويقلل التكاليف. يتيح التطبيق عرض المنتجات والخدمات بشكل مباشر، مع دعم التواصل الفوري بين الطرفين، ما يسهّل عملية البيع والشراء في جميع المجالات (التجارية، الصناعية، الزراعية، الطبية، العقارية، وغيرها).
                <br><br>
                كما يوفّر فرص عمل للعاطلين من خلال هذا التطبيق ويوفر أيضا فرص عمل ودعم أصحاب المشاريع الكبيرة والمتوسطة الصغيرة، ويقدم عروضًا مميزة للطرفين لزيادة فرص النجاح والتوسع في الأعمال، مما يجعل تجربة التجارة أكثر سهولة وأماناً وفعالية.
            </p>
            
            <button class="start-button" onclick="continueToNext()">
                الانتقال للخطوة الثالثة
                <i class="fas fa-arrow-left"></i>
            </button>
        </div>
    </div>

    <script>
        function continueToNext() {
            // تأثير انتقال جميل
            const button = document.querySelector('.start-button');
            button.style.transform = 'scale(0.95)';
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';

            setTimeout(() => {
                // الانتقال إلى شاشة تسجيل الدخول
                window.location.href = 'stage3_login.html';
            }, 1000);
        }

        // تأثير تدريجي للنص
        document.addEventListener('DOMContentLoaded', function() {
            const description = document.querySelector('.app-description');
            description.style.opacity = '0';
            description.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                description.style.transition = 'all 1s ease-out';
                description.style.opacity = '1';
                description.style.transform = 'translateY(0)';
            }, 500);
        });
    </script>
</body>
</html>
