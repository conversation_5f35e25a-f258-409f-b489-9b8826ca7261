import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';
import '../models/user.dart';
import 'feed_screen.dart';
import 'categories_screen.dart';
import 'profile_screen.dart';
import 'messages_screen.dart';
import 'notifications_screen.dart';

class MainScreen extends StatefulWidget {
  final User currentUser;

  const MainScreen({
    super.key,
    required this.currentUser,
  });

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  late List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _screens = [
      FeedScreen(currentUser: widget.currentUser),
      const CategoriesScreen(),
      const NotificationsScreen(),
      const MessagesScreen(),
      ProfileScreen(user: widget.currentUser),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final isRTL = Localizations.localeOf(context).languageCode == 'ar' ||
        Localizations.localeOf(context).languageCode == 'ku';

    return Directionality(
      textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
      child: Scaffold(
        body: IndexedStack(
          index: _currentIndex,
          children: _screens,
        ),
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            backgroundColor: Colors.white,
            selectedItemColor: const Color(0xFF2196F3),
            unselectedItemColor: Colors.grey[600],
            selectedLabelStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            items: [
              BottomNavigationBarItem(
                icon: Icon(
                  _currentIndex == 0 ? Icons.home : Icons.home_outlined,
                  size: 26,
                ),
                label: 'الرئيسية',
              ),
              BottomNavigationBarItem(
                icon: Icon(
                  _currentIndex == 1 ? Icons.apps : Icons.apps_outlined,
                  size: 26,
                ),
                label: 'الأقسام',
              ),
              BottomNavigationBarItem(
                icon: Stack(
                  children: [
                    Icon(
                      _currentIndex == 2
                          ? Icons.notifications
                          : Icons.notifications_outlined,
                      size: 26,
                    ),
                    if (_hasNotifications())
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                  ],
                ),
                label: 'الإشعارات',
              ),
              BottomNavigationBarItem(
                icon: Stack(
                  children: [
                    Icon(
                      _currentIndex == 3
                          ? Icons.chat_bubble
                          : Icons.chat_bubble_outline,
                      size: 26,
                    ),
                    if (_hasMessages())
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                  ],
                ),
                label: 'الرسائل',
              ),
              BottomNavigationBarItem(
                icon: CircleAvatar(
                  radius: 13,
                  backgroundColor: _currentIndex == 4
                      ? const Color(0xFF2196F3)
                      : Colors.grey[300],
                  child: CircleAvatar(
                    radius: 11,
                    backgroundImage: widget.currentUser.profileImage.isNotEmpty
                        ? NetworkImage(widget.currentUser.profileImage)
                        : null,
                    child: widget.currentUser.profileImage.isEmpty
                        ? Icon(
                            Icons.person,
                            size: 16,
                            color: Colors.grey[600],
                          )
                        : null,
                  ),
                ),
                label: 'الملف الشخصي',
              ),
            ],
          ),
        ),
      ),
    );
  }

  bool _hasNotifications() {
    // TODO: Implement notification check
    return true;
  }

  bool _hasMessages() {
    // TODO: Implement message check
    return true;
  }
}
