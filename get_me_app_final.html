<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - وصّلني | التطبيق النهائي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }

        /* Header الشريط العلوي */
        .header {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 70px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* الشعار واسم التطبيق */
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-left: 20px;
        }

        .logo-img {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .app-name {
            font-size: 28px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        /* البحث */
        .search-section {
            flex: 1;
            max-width: 500px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .search-input {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255,255,255,0.9);
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .search-btn {
            width: 45px;
            height: 45px;
            border: none;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .search-btn:hover {
            transform: scale(1.1);
        }

        .search-btn i {
            color: #227FCC;
            font-size: 18px;
        }

        /* شريط التنقل */
        .nav-bar {
            background: linear-gradient(90deg, #16CCC8, #227FCC);
            position: fixed;
            top: 70px;
            left: 0;
            right: 0;
            z-index: 999;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .nav-items {
            display: flex;
            gap: 30px;
            align-items: center;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            color: rgba(255,255,255,0.8);
            font-weight: 500;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(255,255,255,0.2);
            color: white;
            transform: translateY(-2px);
        }

        .nav-item i {
            font-size: 18px;
        }

        /* قائمة الخطوط الثلاثة */
        .menu-dropdown {
            position: relative;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            min-width: 200px;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 1001;
            margin-top: 10px;
        }

        .dropdown-content.show {
            display: block;
        }

        .dropdown-item {
            padding: 12px 20px;
            color: #333;
            cursor: pointer;
            transition: background 0.3s;
            border-bottom: 1px solid #eee;
        }

        .dropdown-item:hover {
            background: #f5f5f5;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 130px;
            padding: 20px;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* شريط الخدمات */
        .services-section {
            margin-bottom: 30px;
            position: relative;
        }

        .services-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .scroll-arrows {
            display: flex;
            gap: 10px;
        }

        .scroll-arrow {
            width: 40px;
            height: 40px;
            border: none;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s;
        }

        .scroll-arrow:hover {
            transform: scale(1.1);
        }

        .services-container {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding: 10px 0;
            scroll-behavior: smooth;
        }

        .services-container::-webkit-scrollbar {
            display: none;
        }

        /* بطاقة الخدمة */
        .service-card {
            min-width: 200px;
            height: 280px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: transform 0.3s;
            position: relative;
        }

        .service-card:hover {
            transform: translateY(-5px);
        }

        .service-image {
            width: 100%;
            height: 195px;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #666;
        }

        .service-name {
            height: 85px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
        }

        /* العروض المميزة */
        .featured-offers {
            margin: 30px 0;
        }

        .featured-card {
            width: 100%;
            max-width: 800px;
            height: 200px;
            margin: 0 auto;
            border: 3px solid #FFD700;
            border-radius: 20px;
            background: linear-gradient(135deg, #FFD700, #FFA000);
            position: relative;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.4);
            cursor: pointer;
            transition: transform 0.3s;
        }

        .featured-card:hover {
            transform: scale(1.02);
        }

        .featured-badge {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(135deg, #FFD700, #FF8F00);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        /* المحتوى الرئيسي */
        .content-area {
            display: flex;
            gap: 30px;
            margin-top: 30px;
        }

        /* منطقة المنشورات */
        .posts-area {
            flex: 1;
        }

        /* القصص */
        .stories-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .stories-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .story-item {
            min-width: 80px;
            text-align: center;
            cursor: pointer;
        }

        .story-avatar {
            width: 65px;
            height: 65px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
            background: #ddd;
        }

        .story-avatar.has-story {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            padding: 3px;
        }

        .story-avatar.no-story {
            background: #ccc;
            padding: 3px;
        }

        .story-inner {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .story-name {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        /* البث المباشر */
        .live-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .live-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .live-item {
            min-width: 80px;
            text-align: center;
            cursor: pointer;
        }

        .live-avatar {
            width: 65px;
            height: 65px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
        }

        .live-avatar.is-live {
            background: linear-gradient(45deg, #ff0000, #ff8c00, #ffd700, #9932cc);
            padding: 3px;
            animation: pulse 2s infinite;
        }

        .live-avatar.not-live {
            background: #ccc;
            padding: 3px;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* الشريط الجانبي */
        .sidebar {
            width: 350px;
        }

        /* مربع الإعلانات */
        .ads-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: 400px;
        }

        .ads-header {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .ads-container {
            height: 320px;
            overflow-y: auto;
        }

        .ad-item {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .ad-item:hover {
            transform: translateX(-5px);
        }

        /* المنشورات المميزة */
        .featured-posts {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        /* نظام النجوم */
        .rating-system {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 5px 0;
        }

        .stars {
            display: flex;
            gap: 2px;
        }

        .star {
            color: #ddd;
            font-size: 14px;
        }

        .star.filled {
            color: #FFD700;
        }

        .membership-duration {
            font-size: 11px;
            color: #666;
            margin-right: 10px;
        }

        /* النوافذ المنبثقة */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
        }

        .modal-content {
            background: white;
            width: 90%;
            max-width: 500px;
            margin: 50px auto;
            border-radius: 15px;
            padding: 20px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        /* الرسائل */
        .messages-container {
            height: 400px;
            overflow-y: auto;
        }

        .message-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.3s;
        }

        .message-item:hover {
            background: #f5f5f5;
        }

        .message-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #ddd;
        }

        .message-info {
            flex: 1;
        }

        .message-name {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .message-preview {
            color: #666;
            font-size: 14px;
        }

        .message-badge {
            background: #ff0000;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        /* الإشعارات */
        .notification-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.3s;
        }

        .notification-item:hover {
            background: #f5f5f5;
        }

        .notification-item.unread {
            background: #f0f8ff;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .notification-heart {
            color: #ddd;
            cursor: pointer;
            transition: color 0.3s;
        }

        .notification-heart.read {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* تجاوبية */
        @media (max-width: 768px) {
            .header {
                padding: 0 10px;
                height: 60px;
            }
            
            .nav-bar {
                top: 60px;
                height: 50px;
                padding: 0 10px;
            }
            
            .main-content {
                margin-top: 110px;
                padding: 10px;
            }
            
            .content-area {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .app-name {
                font-size: 20px;
            }
            
            .nav-items {
                gap: 15px;
            }
            
            .nav-item span {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <div class="header">
        <!-- الشعار واسم التطبيق -->
        <div class="logo-section">
            <div class="logo-img">
                <i class="fas fa-shopping-bag" style="color: #227FCC; font-size: 24px;"></i>
            </div>
            <div class="app-name">Get Me</div>
        </div>

        <!-- البحث -->
        <div class="search-section">
            <input type="text" class="search-input" placeholder="اختر نوع الخدمة" id="searchInput">
            <button class="search-btn" onclick="performSearch()">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>

    <!-- شريط التنقل -->
    <div class="nav-bar">
        <div class="nav-items">
            <div class="nav-item active" onclick="switchTab('home')">
                <i class="fas fa-home"></i>
                <span>الرئيسية</span>
            </div>
            <div class="nav-item" onclick="openNotifications()">
                <i class="fas fa-bell"></i>
                <span>الإشعارات</span>
            </div>
            <div class="nav-item" onclick="openMessages()">
                <i class="fas fa-envelope"></i>
                <span>الرسائل</span>
                <div class="message-badge" id="messageBadge">3</div>
            </div>
            <div class="nav-item" onclick="switchTab('profile')">
                <i class="fas fa-user"></i>
                <span>الملف الشخصي</span>
            </div>
            <div class="nav-item menu-dropdown" onclick="toggleMenu()">
                <i class="fas fa-bars"></i>
                <div class="dropdown-content" id="menuDropdown">
                    <div class="dropdown-item">
                        <i class="fas fa-question-circle"></i> المساعدة والدعم
                    </div>
                    <div class="dropdown-item">
                        <i class="fas fa-cog"></i> الإعدادات والخصوصية
                    </div>
                    <div class="dropdown-item">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط الخدمات -->
        <div class="services-section">
            <div class="services-header">
                <div class="section-title">الخدمات التجارية</div>
                <div class="scroll-arrows">
                    <button class="scroll-arrow" onclick="scrollServices('left')">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="scroll-arrow" onclick="scrollServices('right')">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            <div class="services-container" id="servicesContainer">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>

        <!-- العروض المميزة -->
        <div class="featured-offers">
            <div class="featured-card" onclick="showNotification('تم النقر على العرض المميز')">
                <div class="featured-badge">عرض مميز</div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content-area">
            <!-- منطقة المنشورات -->
            <div class="posts-area">
                <!-- القصص -->
                <div class="stories-section">
                    <h3 style="margin-bottom: 15px;">القصص</h3>
                    <div class="stories-container" id="storiesContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- البث المباشر -->
                <div class="live-section">
                    <h3 style="margin-bottom: 15px;">البث المباشر</h3>
                    <div class="live-container" id="liveContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="sidebar">
                <!-- مربع الإعلانات -->
                <div class="ads-section">
                    <div class="ads-header">مشاهدة الإعلانات</div>
                    <div class="ads-container" id="adsContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- المنشورات المميزة -->
                <div class="featured-posts">
                    <div class="ads-header">المنشورات المميزة</div>
                    <div id="featuredPostsContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة الإشعارات -->
    <div class="modal" id="notificationsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>الإشعارات</h3>
                <div style="display: flex; gap: 10px;">
                    <button onclick="toggleNotificationMenu()" style="background: none; border: none; cursor: pointer;">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <button class="close-btn" onclick="closeModal('notificationsModal')">&times;</button>
                </div>
            </div>
            <div id="notificationsContainer">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>

    <!-- نافذة الرسائل -->
    <div class="modal" id="messagesModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>الرسائل</h3>
                <button class="close-btn" onclick="closeModal('messagesModal')">&times;</button>
            </div>
            <input type="text" placeholder="البحث في الرسائل..." style="width: 100%; padding: 10px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 8px;">
            <div class="messages-container" id="messagesContainer">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // بيانات الخدمات
        const services = [
            { name: 'مطاعم', icon: 'fas fa-utensils', image: 'مطعم.jpg' },
            { name: 'صيدليات', icon: 'fas fa-pills', image: 'طبية.jpg' },
            { name: 'مراكز تجميل', icon: 'fas fa-spa', image: 'مستحظرات تجميل.jpg' },
            { name: 'ملابس', icon: 'fas fa-tshirt', image: 'محلات.jpg' },
            { name: 'إلكترونيات', icon: 'fas fa-laptop', image: 'pp.png' },
            { name: 'أطباء', icon: 'fas fa-user-md', image: 'الطبية.jpg' },
            { name: 'تعليم', icon: 'fas fa-graduation-cap', image: 'تعليم.jpg' },
            { name: 'سيارات', icon: 'fas fa-car', image: 'سيارات.jpg' },
            { name: 'عقارات', icon: 'fas fa-home', image: 'عقارات.jpg' },
            { name: 'كافيهات', icon: 'fas fa-coffee', image: 'كافيهات.jpg' },
            { name: 'مصارف وبنوك', icon: 'fas fa-university', image: 'مصارف وبنوك.jpg' },
            { name: 'نباتات وحدائق', icon: 'fas fa-seedling', image: 'زراعية.jpg' },
            { name: 'خدمات توصيل', icon: 'fas fa-truck', image: 'توصيل.jpg' },
            { name: 'مواد غذائية', icon: 'fas fa-shopping-cart', image: 'السوبرماركت.jpg' },
            { name: 'الصناعة', icon: 'fas fa-industry', image: 'الصناعة.jpg' }
        ];

        // بيانات القصص
        const stories = [
            { name: 'قصتك', hasStory: false, isAdd: true },
            { name: 'أحمد محمد', hasStory: true },
            { name: 'فاطمة أحمد', hasStory: true },
            { name: 'محمد علي', hasStory: false },
            { name: 'سارة حسن', hasStory: true },
            { name: 'عبدالله أحمد', hasStory: false }
        ];

        // بيانات البث المباشر
        const liveStreams = [
            { name: 'أحمد محمد', isLive: true },
            { name: 'فاطمة أحمد', isLive: false },
            { name: 'محمد علي', isLive: true },
            { name: 'سارة حسن', isLive: false },
            { name: 'عبدالله أحمد', isLive: true }
        ];

        // بيانات الإعلانات
        let ads = [
            'إعلان رقم 1 - عرض خاص على الهواتف الذكية',
            'إعلان رقم 2 - خصم 50% على الملابس',
            'إعلان رقم 3 - مطعم جديد في المنطقة',
            'إعلان رقم 4 - دورة تعليمية مجانية',
            'إعلان رقم 5 - عقارات بأسعار مميزة',
            'إعلان رقم 6 - خدمات توصيل سريعة'
        ];

        // بيانات الإشعارات
        const notifications = [
            { type: 'like', message: 'أعجب أحمد محمد بمنشورك', time: 'منذ 5 دقائق', read: false },
            { type: 'comment', message: 'علق محمد علي على منشورك', time: 'منذ 10 دقائق', read: false },
            { type: 'follow', message: 'بدأ فاطمة أحمد بمتابعتك', time: 'منذ 15 دقيقة', read: true },
            { type: 'message', message: 'رسالة جديدة من سارة حسن', time: 'منذ 20 دقيقة', read: false }
        ];

        // بيانات الرسائل
        const messages = [
            { name: 'أحمد محمد', message: 'مرحباً، هل المنتج متوفر؟', unread: 2 },
            { name: 'فاطمة أحمد', message: 'شكراً لك على الخدمة', unread: 0 },
            { name: 'محمد علي', message: 'متى يمكنني الاستلام؟', unread: 1 },
            { name: 'سارة حسن', message: 'ممتاز، سأقوم بالطلب', unread: 0 }
        ];

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            populateServices();
            populateStories();
            populateLiveStreams();
            populateAds();
            populateNotifications();
            populateMessages();
            startAdRotation();
        });

        // ملء الخدمات
        function populateServices() {
            const container = document.getElementById('servicesContainer');
            container.innerHTML = services.map(service => `
                <div class="service-card" onclick="openServicePage('${service.name}')">
                    <div class="service-image">
                        <i class="${service.icon}"></i>
                    </div>
                    <div class="service-name">${service.name}</div>
                </div>
            `).join('');
        }

        // ملء القصص
        function populateStories() {
            const container = document.getElementById('storiesContainer');
            container.innerHTML = stories.map(story => `
                <div class="story-item" onclick="openStory('${story.name}')">
                    <div class="story-avatar ${story.hasStory ? 'has-story' : 'no-story'}">
                        <div class="story-inner">
                            ${story.isAdd ? '<i class="fas fa-plus" style="color: #227FCC;"></i>' : '<i class="fas fa-user"></i>'}
                        </div>
                    </div>
                    <div class="story-name">${story.name}</div>
                </div>
            `).join('');
        }

        // ملء البث المباشر
        function populateLiveStreams() {
            const container = document.getElementById('liveContainer');
            container.innerHTML = liveStreams.map(stream => `
                <div class="live-item" onclick="openLiveStream('${stream.name}')">
                    <div class="live-avatar ${stream.isLive ? 'is-live' : 'not-live'}">
                        <div class="story-inner">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="story-name">${stream.name}</div>
                </div>
            `).join('');
        }

        // ملء الإعلانات
        function populateAds() {
            const container = document.getElementById('adsContainer');
            const visibleAds = ads.slice(0, 4);
            container.innerHTML = visibleAds.map((ad, index) => `
                <div class="ad-item" onclick="watchAd(${index})">
                    ${ad}
                </div>
            `).join('');
        }

        // ملء الإشعارات
        function populateNotifications() {
            const container = document.getElementById('notificationsContainer');
            container.innerHTML = notifications.map((notif, index) => `
                <div class="notification-item ${notif.read ? '' : 'unread'}" onclick="markAsRead(${index})">
                    <div class="notification-icon" style="background: ${getNotificationColor(notif.type)}">
                        <i class="${getNotificationIcon(notif.type)}"></i>
                    </div>
                    <div style="flex: 1;">
                        <div>${notif.message}</div>
                        <div style="font-size: 12px; color: #666;">${notif.time}</div>
                    </div>
                    <div class="notification-heart ${notif.read ? 'read' : ''}" onclick="toggleRead(${index})">
                        <i class="fas fa-heart"></i>
                    </div>
                </div>
            `).join('');
        }

        // ملء الرسائل
        function populateMessages() {
            const container = document.getElementById('messagesContainer');
            container.innerHTML = messages.map((msg, index) => `
                <div class="message-item" onclick="openChat('${msg.name}')">
                    <div class="message-avatar"></div>
                    <div class="message-info">
                        <div class="message-name">${msg.name}</div>
                        <div class="message-preview">${msg.message}</div>
                    </div>
                    ${msg.unread > 0 ? `<div class="message-badge">${msg.unread}</div>` : ''}
                </div>
            `).join('');
        }

        // الوظائف
        function performSearch() {
            const query = document.getElementById('searchInput').value;
            if (query) {
                const service = services.find(s => s.name.includes(query));
                if (service) {
                    openServicePage(service.name);
                } else {
                    showNotification('لم يتم العثور على الخدمة المطلوبة');
                }
            }
        }

        function scrollServices(direction) {
            const container = document.getElementById('servicesContainer');
            const scrollAmount = 300;
            if (direction === 'left') {
                container.scrollLeft -= scrollAmount;
            } else {
                container.scrollLeft += scrollAmount;
            }
        }

        function switchTab(tab) {
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');
            showNotification(`تم التبديل إلى ${getTabName(tab)}`);
        }

        function toggleMenu() {
            const dropdown = document.getElementById('menuDropdown');
            dropdown.classList.toggle('show');
        }

        function openNotifications() {
            document.getElementById('notificationsModal').style.display = 'block';
        }

        function openMessages() {
            document.getElementById('messagesModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function openServicePage(serviceName) {
            // إنشاء صفحة المنشورات للخدمة المحددة
            const servicePageHTML = createServicePage(serviceName);
            document.body.innerHTML = servicePageHTML;
            initializeServicePage(serviceName);
        }

        function openStory(userName) {
            showNotification(`فتح قصة ${userName}`);
        }

        function openLiveStream(userName) {
            showNotification(`فتح بث ${userName} المباشر`);
        }

        function watchAd(index) {
            showNotification(`مشاهدة الإعلان رقم ${index + 1} - تم إضافة نقاط إلى حسابك`);
        }

        function markAsRead(index) {
            notifications[index].read = true;
            populateNotifications();
        }

        function toggleRead(index) {
            event.stopPropagation();
            notifications[index].read = !notifications[index].read;
            populateNotifications();
        }

        function openChat(userName) {
            showNotification(`فتح محادثة مع ${userName}`);
            closeModal('messagesModal');
        }

        // تدوير الإعلانات كل دقيقة
        function startAdRotation() {
            setInterval(() => {
                // تحريك الإعلانات
                const firstAd = ads.shift();
                ads.push(firstAd);
                populateAds();
            }, 60000); // كل دقيقة
        }

        // وظائف مساعدة
        function getNotificationColor(type) {
            const colors = {
                'like': '#e74c3c',
                'comment': '#3498db',
                'follow': '#9b59b6',
                'message': '#2ecc71'
            };
            return colors[type] || '#95a5a6';
        }

        function getNotificationIcon(type) {
            const icons = {
                'like': 'fas fa-heart',
                'comment': 'fas fa-comment',
                'follow': 'fas fa-user-plus',
                'message': 'fas fa-envelope'
            };
            return icons[type] || 'fas fa-bell';
        }

        function getTabName(tab) {
            const names = {
                'home': 'الرئيسية',
                'profile': 'الملف الشخصي'
            };
            return names[tab] || tab;
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: linear-gradient(135deg, #16CCC8, #227FCC);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 5px 20px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: 'Cairo', sans-serif;
                max-width: 300px;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // إنشاء صفحة المنشورات للخدمة
        function createServicePage(serviceName) {
            return `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${serviceName} - Get Me</title>
                <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                <style>
                    * { margin: 0; padding: 0; box-sizing: border-box; }
                    body { font-family: 'Cairo', sans-serif; background: #f5f5f5; direction: rtl; }

                    .service-header {
                        background: white;
                        padding: 15px 20px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        display: flex;
                        align-items: center;
                        gap: 15px;
                    }

                    .back-btn {
                        background: none;
                        border: none;
                        font-size: 24px;
                        cursor: pointer;
                        color: #227FCC;
                    }

                    .service-title {
                        flex: 1;
                        font-size: 24px;
                        font-weight: bold;
                        color: #333;
                    }

                    .logo-small {
                        width: 40px;
                        height: 40px;
                        background: linear-gradient(135deg, #16CCC8, #227FCC);
                        border-radius: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                    }

                    .content {
                        padding: 20px;
                        max-width: 1200px;
                        margin: 0 auto;
                    }

                    .stories-section, .live-section {
                        background: white;
                        border-radius: 15px;
                        padding: 20px;
                        margin-bottom: 20px;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    }

                    .stories-container, .live-container {
                        display: flex;
                        gap: 15px;
                        overflow-x: auto;
                        padding: 10px 0;
                    }

                    .story-item, .live-item {
                        min-width: 80px;
                        text-align: center;
                        cursor: pointer;
                    }

                    .story-avatar, .live-avatar {
                        width: 65px;
                        height: 65px;
                        border-radius: 50%;
                        margin: 0 auto 8px;
                        position: relative;
                        background: #ddd;
                    }

                    .story-avatar.has-story {
                        background: linear-gradient(135deg, #16CCC8, #227FCC);
                        padding: 3px;
                    }

                    .story-avatar.no-story {
                        background: #ccc;
                        padding: 3px;
                    }

                    .live-avatar.is-live {
                        background: linear-gradient(45deg, #ff0000, #ff8c00, #ffd700, #9932cc);
                        padding: 3px;
                        animation: pulse 2s infinite;
                    }

                    .live-avatar.not-live {
                        background: #ccc;
                        padding: 3px;
                    }

                    @keyframes pulse {
                        0% { transform: scale(1); }
                        50% { transform: scale(1.05); }
                        100% { transform: scale(1); }
                    }

                    .story-inner {
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        background: white;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .story-name {
                        font-size: 12px;
                        color: #333;
                        font-weight: 500;
                    }

                    .create-post {
                        background: white;
                        border-radius: 15px;
                        padding: 20px;
                        margin-bottom: 20px;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    }

                    .create-post-header {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        margin-bottom: 15px;
                    }

                    .user-avatar {
                        width: 50px;
                        height: 50px;
                        border-radius: 50%;
                        background: #ddd;
                    }

                    .post-input {
                        flex: 1;
                        padding: 12px 20px;
                        border: 1px solid #ddd;
                        border-radius: 25px;
                        background: #f5f5f5;
                        cursor: pointer;
                        color: #666;
                    }

                    .post-actions {
                        display: flex;
                        justify-content: space-around;
                        padding-top: 15px;
                        border-top: 1px solid #eee;
                    }

                    .post-action {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        padding: 8px 16px;
                        border-radius: 8px;
                        cursor: pointer;
                        transition: background 0.3s;
                    }

                    .post-action:hover {
                        background: #f5f5f5;
                    }

                    .post-card {
                        background: white;
                        border-radius: 15px;
                        margin-bottom: 20px;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                        overflow: hidden;
                    }

                    .post-header {
                        padding: 15px;
                        display: flex;
                        align-items: center;
                        gap: 12px;
                    }

                    .post-user-info {
                        flex: 1;
                    }

                    .post-user-name {
                        font-weight: bold;
                        margin-bottom: 4px;
                    }

                    .rating-system {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        margin: 5px 0;
                    }

                    .stars {
                        display: flex;
                        gap: 2px;
                    }

                    .star {
                        color: #ddd;
                        font-size: 14px;
                    }

                    .star.filled {
                        color: #FFD700;
                    }

                    .membership-duration {
                        font-size: 11px;
                        color: #666;
                        margin-right: 10px;
                    }

                    .post-time {
                        font-size: 12px;
                        color: #666;
                    }

                    .post-content {
                        padding: 0 15px 15px;
                        line-height: 1.5;
                        color: #333;
                    }

                    .post-image {
                        width: 100%;
                        max-height: 400px;
                        object-fit: cover;
                    }

                    .post-stats {
                        padding: 10px 15px;
                        display: flex;
                        justify-content: space-between;
                        border-top: 1px solid #eee;
                        border-bottom: 1px solid #eee;
                        font-size: 14px;
                        color: #666;
                    }

                    .post-interactions {
                        display: flex;
                        justify-content: space-around;
                        padding: 10px;
                    }

                    .interaction-btn {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        padding: 8px 16px;
                        border: none;
                        background: none;
                        border-radius: 8px;
                        cursor: pointer;
                        transition: background 0.3s;
                        font-family: 'Cairo', sans-serif;
                        color: #666;
                    }

                    .interaction-btn:hover {
                        background: #f5f5f5;
                    }

                    .interaction-btn.liked {
                        background: linear-gradient(135deg, #16CCC8, #227FCC);
                        color: white;
                    }
                </style>
            </head>
            <body>
                <div class="service-header">
                    <button class="back-btn" onclick="goBack()">
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <div class="service-title">${serviceName}</div>
                    <div class="logo-small">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                </div>

                <div class="content">
                    <!-- القصص -->
                    <div class="stories-section">
                        <h3 style="margin-bottom: 15px;">القصص</h3>
                        <div class="stories-container" id="serviceStoriesContainer">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>

                    <!-- البث المباشر -->
                    <div class="live-section">
                        <h3 style="margin-bottom: 15px;">البث المباشر</h3>
                        <div class="live-container" id="serviceLiveContainer">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>

                    <!-- إنشاء منشور -->
                    <div class="create-post">
                        <div class="create-post-header">
                            <div class="user-avatar"></div>
                            <div class="post-input" onclick="createNewPost()">ماذا تريد أن تشارك في ${serviceName}؟</div>
                        </div>
                        <div class="post-actions">
                            <div class="post-action">
                                <i class="fas fa-image" style="color: #4CAF50;"></i>
                                <span>صورة/فيديو</span>
                            </div>
                            <div class="post-action">
                                <i class="fas fa-map-marker-alt" style="color: #F44336;"></i>
                                <span>موقع</span>
                            </div>
                            <div class="post-action">
                                <i class="fas fa-tag" style="color: #FF9800;"></i>
                                <span>سعر</span>
                            </div>
                        </div>
                    </div>

                    <!-- المنشورات -->
                    <div id="servicePostsContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <script>
                    function goBack() {
                        location.reload();
                    }

                    function createNewPost() {
                        alert('فتح نافذة إنشاء منشور جديد');
                    }

                    function likePost(button) {
                        button.classList.toggle('liked');
                        const icon = button.querySelector('i');
                        if (button.classList.contains('liked')) {
                            icon.className = 'fas fa-heart';
                        } else {
                            icon.className = 'far fa-heart';
                        }
                    }

                    function commentPost() {
                        alert('تم إرسال طلب السعر في رسالة خاصة');
                    }

                    function sharePost() {
                        alert('مشاركة المنشور');
                    }
                </script>
            </body>
            </html>
            `;
        }

        // تهيئة صفحة الخدمة
        function initializeServicePage(serviceName) {
            // ملء القصص
            const storiesContainer = document.getElementById('serviceStoriesContainer');
            if (storiesContainer) {
                storiesContainer.innerHTML = stories.map(story => `
                    <div class="story-item">
                        <div class="story-avatar ${story.hasStory ? 'has-story' : 'no-story'}">
                            <div class="story-inner">
                                ${story.isAdd ? '<i class="fas fa-plus" style="color: #227FCC;"></i>' : '<i class="fas fa-user"></i>'}
                            </div>
                        </div>
                        <div class="story-name">${story.name}</div>
                    </div>
                `).join('');
            }

            // ملء البث المباشر
            const liveContainer = document.getElementById('serviceLiveContainer');
            if (liveContainer) {
                liveContainer.innerHTML = liveStreams.map(stream => `
                    <div class="live-item">
                        <div class="live-avatar ${stream.isLive ? 'is-live' : 'not-live'}">
                            <div class="story-inner">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div class="story-name">${stream.name}</div>
                    </div>
                `).join('');
            }

            // ملء المنشورات
            const postsContainer = document.getElementById('servicePostsContainer');
            if (postsContainer) {
                const servicePosts = generateServicePosts(serviceName);
                postsContainer.innerHTML = servicePosts.map(post => `
                    <div class="post-card">
                        <div class="post-header">
                            <div class="user-avatar"></div>
                            <div class="post-user-info">
                                <div class="post-user-name">${post.author}</div>
                                <div class="rating-system">
                                    <div class="stars">
                                        ${generateStars(post.rating)}
                                    </div>
                                    <div class="membership-duration">${post.membershipDuration}</div>
                                </div>
                                <div class="post-time">${post.time}</div>
                            </div>
                        </div>
                        <div class="post-content">${post.content}</div>
                        ${post.image ? `<img src="${post.image}" alt="Post image" class="post-image">` : ''}
                        <div class="post-stats">
                            <span>${post.likes} إعجاب</span>
                            <span>${post.comments} تعليق</span>
                        </div>
                        <div class="post-interactions">
                            <button class="interaction-btn" onclick="likePost(this)">
                                <i class="far fa-heart"></i>
                                <span>إعجاب</span>
                            </button>
                            <button class="interaction-btn" onclick="commentPost()">
                                <i class="far fa-comment"></i>
                                <span>طلب السعر</span>
                            </button>
                            <button class="interaction-btn" onclick="sharePost()">
                                <i class="far fa-share"></i>
                                <span>مشاركة</span>
                            </button>
                        </div>
                    </div>
                `).join('');
            }
        }

        // إنشاء منشورات للخدمة
        function generateServicePosts(serviceName) {
            const posts = [
                {
                    author: 'أحمد محمد',
                    rating: 4,
                    membershipDuration: 'عضو منذ سنتين',
                    time: 'منذ ساعتين',
                    content: `عرض خاص في ${serviceName}! جودة عالية وأسعار مناسبة. تواصل معنا للحصول على أفضل العروض.`,
                    image: 'https://via.placeholder.com/500x300',
                    likes: 45,
                    comments: 12
                },
                {
                    author: 'فاطمة أحمد',
                    rating: 5,
                    membershipDuration: 'عضو منذ 3 سنوات',
                    time: 'منذ 4 ساعات',
                    content: `خدمة ممتازة في ${serviceName}. نقدم أفضل الخدمات بأعلى جودة وأقل الأسعار.`,
                    image: 'https://via.placeholder.com/500x300',
                    likes: 89,
                    comments: 23
                }
            ];
            return posts;
        }

        // إنشاء النجوم
        function generateStars(rating) {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    stars += '<i class="star filled fas fa-star"></i>';
                } else {
                    stars += '<i class="star fas fa-star"></i>';
                }
            }
            return stars;
        }

        // إغلاق القوائم عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.menu-dropdown')) {
                document.getElementById('menuDropdown').classList.remove('show');
            }
        });
    </script>
</body>
</html>
