<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - تطبيق الخدمات المحلية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }

        /* الشريط العلوي */
        .header {
            background: linear-gradient(90deg, #16CCC8, #227FCC);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15), 0 4px 10px rgba(0,0,0,0.1);
        }

        /* اسم التطبيق */
        .app-name {
            font-size: 40px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-right: 20px;
        }

        /* البحث */
        .search-section {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-right: 30px;
            flex: 1;
            max-width: 600px;
        }

        .search-input {
            width: 100%;
            height: 40px;
            padding: 12px 20px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            background: rgba(255,255,255,0.9);
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .search-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .search-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .search-btn i {
            color: white;
            font-size: 24px;
            transition: all 0.3s;
        }

        /* قسم المستخدم - في الجهة اليسرى */
        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .menu-icon {
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background 0.3s;
        }

        .menu-icon:hover {
            background: rgba(255,255,255,0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            position: relative;
            cursor: pointer;
        }

        .user-avatar.online {
            border: 3px solid #28a745;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.6), 0 0 30px rgba(40, 167, 69, 0.4);
            animation: glow-green 2s ease-in-out infinite alternate;
        }

        .user-avatar.offline {
            border: 3px solid #6c757d;
            box-shadow: 0 0 10px rgba(108, 117, 125, 0.3);
        }

        @keyframes glow-green {
            from {
                box-shadow: 0 0 15px rgba(40, 167, 69, 0.6), 0 0 30px rgba(40, 167, 69, 0.4);
            }
            to {
                box-shadow: 0 0 20px rgba(40, 167, 69, 0.8), 0 0 40px rgba(40, 167, 69, 0.6);
            }
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-avatar-placeholder {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .user-name {
            color: white;
            font-size: 16px;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        /* شريط التنقل */
        .nav-bar {
            background: white;
            position: fixed;
            top: 70px;
            left: 0;
            right: 0;
            z-index: 999;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .nav-items {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 25px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 18px;
            cursor: pointer;
            transition: all 0.3s;
            color: #495057;
            font-weight: 500;
            position: relative;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item:hover i, .nav-item.active i {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item i {
            font-size: 24px;
        }

        .nav-item.home-item {
            margin-left: 80px;
        }

        /* ألوان الإشعارات - بدون حركة */
        .nav-item.has-notification i {
            color: #ff0000 !important;
            background: none !important;
            -webkit-text-fill-color: #ff0000 !important;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #ff0000;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
            display: none;
        }

        .notification-badge.show {
            display: flex;
        }

        .nav-item.has-notification {
            color: #495057;
        }

        .nav-item.has-notification:hover, .nav-item.has-notification.active {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* شريط الخدمات */
        .services-section {
            margin-bottom: 30px;
            position: relative;
        }

        .services-container {
            position: relative;
            overflow: hidden;
            padding: 10px 0;
        }

        .services-wrapper {
            display: flex;
            gap: 15px;
            transition: transform 1s ease;
            width: fit-content;
        }

        .nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 35px;
            height: 35px;
            border: none;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            font-size: 14px;
            z-index: 10;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .nav-arrow:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 4px 15px rgba(34, 127, 204, 0.4);
        }

        .nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: translateY(-50%);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .nav-arrow.prev {
            right: 10px;
        }

        .nav-arrow.next {
            left: 10px;
        }

        /* بطاقة الخدمة */
        .service-card {
            width: 180px;
            height: 260px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: transform 0.3s;
            position: relative;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
        }

        .service-card:hover {
            transform: translateY(-5px);
        }

        .service-image {
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .service-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .service-image-placeholder {
            font-size: 40px;
            color: #666;
        }

        .service-name {
            height: 60px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 15px;
            font-weight: bold;
            text-align: center;
            font-family: 'Cairo', sans-serif;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 130px;
            padding: 20px;
        }

        .content-area {
            display: flex;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .main-feed {
            flex: 1;
        }

        .sidebar {
            width: 300px;
        }

        /* القصص */
        .stories-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .stories-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
            scroll-behavior: smooth;
        }

        .stories-container::-webkit-scrollbar {
            display: none;
        }

        .story-item {
            flex-shrink: 0;
            text-align: center;
            cursor: pointer;
        }

        .story-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .story-avatar.has-story {
            border: 3px solid #16CCC8;
        }

        .story-avatar.no-story {
            border: 3px solid #ccc;
        }

        .story-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .story-inner {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 20px;
        }

        .story-name {
            font-size: 12px;
            color: #333;
            max-width: 70px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* البث المباشر */
        .live-section {
            margin-bottom: 30px;
        }

        .live-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
            scroll-behavior: smooth;
        }

        .live-container::-webkit-scrollbar {
            display: none;
        }

        .live-item {
            flex-shrink: 0;
            text-align: center;
            cursor: pointer;
        }

        .live-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 20px;
        }

        .live-avatar.is-live {
            border: 3px solid;
            border-image: linear-gradient(45deg, #ff0000, #ff8c00, #ffd700, #9932cc) 1;
            animation: live-pulse 2s ease-in-out infinite;
        }

        .live-avatar.no-live {
            border: 3px solid #ccc;
        }

        @keyframes live-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .live-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .live-indicator {
            position: absolute;
            bottom: -2px;
            right: -2px;
            background: #ff0000;
            color: white;
            font-size: 8px;
            padding: 2px 4px;
            border-radius: 8px;
            font-weight: bold;
        }

        /* الإعلانات */
        .ads-section {
            margin-bottom: 30px;
        }

        .ads-container {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            border-radius: 15px;
            padding: 20px;
            color: white;
            text-align: center;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .ads-container:hover {
            transform: translateY(-2px);
        }

        .ad-text {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .ad-subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        /* المنشورات المميزة */
        .featured-posts {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .featured-content {
            text-align: center;
            color: #666;
            padding: 40px 20px;
        }

        .featured-content i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <div class="header">
        <!-- قسم المستخدم - في الجهة اليسرى -->
        <div class="user-section">
            <div class="menu-icon" onclick="toggleUserMenu()">
                <i class="fas fa-bars"></i>
            </div>
            <div class="user-avatar online" id="userAvatar">
                <img src="images/Hussein Nihad.png" alt="Hussein Nihad" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="user-avatar-placeholder" style="display: none;">
                    <i class="fas fa-user"></i>
                </div>
            </div>
            <div class="user-name">Hussein Nihad</div>
        </div>

        <!-- البحث - في الوسط -->
        <div class="search-section">
            <input type="text" class="search-input" placeholder="اختر نوع الخدمة" id="searchInput">
            <button class="search-btn" onclick="performSearch()">
                <i class="fas fa-search"></i>
            </button>
        </div>

        <!-- اسم التطبيق - في الجهة اليمنى -->
        <div class="app-name">Get Me</div>
    </div>

    <!-- شريط التنقل -->
    <div class="nav-bar">
        <div class="nav-items">
            <div class="nav-item home-item active" onclick="switchTab('home')">
                <i class="fas fa-home"></i>
                <span>الرئيسية</span>
            </div>
            <div class="nav-item has-notification" id="notificationsBtn" onclick="openNotifications()">
                <i class="fas fa-bell"></i>
                <span>الإشعارات</span>
                <div class="notification-badge show" id="notificationsBadge">5</div>
            </div>
            <div class="nav-item has-notification" id="messagesBtn" onclick="openMessages()">
                <i class="fas fa-envelope"></i>
                <span>الرسائل</span>
                <div class="notification-badge show" id="messagesBadge">3</div>
            </div>
            <div class="nav-item" onclick="openProfile()">
                <i class="fas fa-user"></i>
                <span>الملف الشخصي</span>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط الخدمات -->
        <div class="services-section">
            <div class="services-container" id="servicesContainer">
                <button class="nav-arrow prev" id="prevBtn" onclick="moveRight()">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button class="nav-arrow next" id="nextBtn" onclick="moveLeft()">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="services-wrapper" id="servicesWrapper">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <div class="content-area">
            <div class="main-feed">
                <!-- القصص -->
                <div class="stories-section">
                    <div class="section-title">القصص</div>
                    <div class="stories-container" id="storiesContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- البث المباشر -->
                <div class="live-section">
                    <div class="section-title">البث المباشر</div>
                    <div class="live-container" id="liveContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <!-- الإعلانات -->
                <div class="ads-section">
                    <div class="section-title">عرض مميز</div>
                    <div class="ads-container" id="adsContainer" onclick="watchAd(0)">
                        <div class="ad-text" id="adText">إعلان رقم 1</div>
                        <div class="ad-subtitle">انقر لمشاهدة الإعلان</div>
                    </div>
                </div>

                <!-- المنشورات المميزة -->
                <div class="featured-posts">
                    <div class="section-title">المنشورات المميزة</div>
                    <div class="featured-content">
                        <i class="fas fa-star"></i>
                        <p>المنشورات المميزة ستظهر هنا</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات الخدمات
        const services = [
            { name: 'مطعم', icon: 'fas fa-utensils', image: 'مطعم.jpg' },
            { name: 'طبية', icon: 'fas fa-pills', image: 'طبية.jpg' },
            { name: 'مستحضرات تجميل', icon: 'fas fa-spa', image: 'مستحظرات تجميل.jpg' },
            { name: 'محلات', icon: 'fas fa-tshirt', image: 'محلات.jpg' },
            { name: 'السوبرماركت', icon: 'fas fa-shopping-cart', image: 'السوبرماركت.jpg' },
            { name: 'الطبية', icon: 'fas fa-user-md', image: 'الطبية.jpg' },
            { name: 'تعليم', icon: 'fas fa-graduation-cap', image: 'تعليم.jpg' },
            { name: 'سيارات', icon: 'fas fa-car', image: 'سيارات.jpg' },
            { name: 'عقارات', icon: 'fas fa-home', image: 'عقارات.jpg' },
            { name: 'كافيهات', icon: 'fas fa-coffee', image: 'كافيهات.jpg' },
            { name: 'مصارف وبنوك', icon: 'fas fa-university', image: 'مصارف وبنوك.jpg' },
            { name: 'زراعية', icon: 'fas fa-seedling', image: 'زراعية.jpg' },
            { name: 'توصيل', icon: 'fas fa-truck', image: 'توصيل.jpg' },
            { name: 'الصيرفة', icon: 'fas fa-coins', image: 'الصيرفة.jpg' },
            { name: 'الصناعة', icon: 'fas fa-industry', image: 'الصناعة.jpg' },
            { name: 'ذهب', icon: 'fas fa-gem', image: 'ذهب.jpg' },
            { name: 'قرطاسية', icon: 'fas fa-pen', image: 'قرطاسية.jpg' },
            { name: 'شركات السياحية', icon: 'fas fa-plane', image: 'شركات السياحية.jpg' },
            { name: 'مصايف', icon: 'fas fa-umbrella-beach', image: 'مصايف.jpg' },
            { name: 'استيراد وتصدير', icon: 'fas fa-ship', image: 'استيراد وتصدير.jpg' }
        ];

        // متغيرات التنقل في الخدمات
        let currentPosition = 0;
        const cardWidth = 180;
        const cardGap = 15;
        const visibleCards = 7;
        const moveDistance = cardWidth + cardGap;

        // ملء الخدمات
        function populateServices() {
            const wrapper = document.getElementById('servicesWrapper');
            wrapper.innerHTML = services.map(service => `
                <div class="service-card" onclick="openServicePage('${service.name}')">
                    <div class="service-image">
                        <img src="images/${service.image}" alt="${service.name}" 
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div class="service-image-placeholder" style="display: none;">
                            <i class="${service.icon}"></i>
                        </div>
                    </div>
                    <div class="service-name">${service.name}</div>
                </div>
            `).join('');
            
            updateNavigationButtons();
        }

        // تحديث أزرار التنقل
        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const maxPosition = (services.length - visibleCards) * moveDistance;
            
            prevBtn.disabled = currentPosition >= maxPosition;
            nextBtn.disabled = currentPosition <= 0;
        }

        // تحريك الشريط لليمين (السهم الأيمن)
        function moveRight() {
            const wrapper = document.getElementById('servicesWrapper');
            const maxPosition = (services.length - visibleCards) * moveDistance;
            
            if (currentPosition < maxPosition) {
                currentPosition += moveDistance;
            }
            
            currentPosition = Math.min(maxPosition, currentPosition);
            wrapper.style.transform = `translateX(-${currentPosition}px)`;
            updateNavigationButtons();
        }

        // تحريك الشريط لليسار (السهم الأيسر)
        function moveLeft() {
            const wrapper = document.getElementById('servicesWrapper');
            
            if (currentPosition > 0) {
                currentPosition -= moveDistance;
            }
            
            currentPosition = Math.max(0, currentPosition);
            wrapper.style.transform = `translateX(-${currentPosition}px)`;
            updateNavigationButtons();
        }

        // فتح صفحة الخدمة
        function openServicePage(serviceName) {
            // إنشاء صفحة المنشورات للخدمة
            const servicePageHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>${serviceName} - Get Me</title>
                    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                    <style>
                        * { margin: 0; padding: 0; box-sizing: border-box; }
                        body { font-family: 'Cairo', sans-serif; background: #f0f2f5; direction: rtl; }

                        .service-header {
                            background: white;
                            padding: 15px 20px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            position: sticky;
                            top: 0;
                            z-index: 1000;
                        }

                        .back-btn {
                            background: none;
                            border: none;
                            font-size: 24px;
                            cursor: pointer;
                            color: #227FCC;
                            padding: 8px;
                            border-radius: 50%;
                            transition: background 0.3s;
                        }

                        .back-btn:hover {
                            background: #f5f5f5;
                        }

                        .service-title {
                            flex: 1;
                            font-size: 24px;
                            font-weight: bold;
                            color: #333;
                        }

                        .logo-small {
                            width: 40px;
                            height: 40px;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            border-radius: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                        }

                        .stories-section, .live-section {
                            background: white;
                            padding: 15px 0;
                            margin-bottom: 10px;
                        }

                        .stories-container, .live-container {
                            display: flex;
                            gap: 15px;
                            overflow-x: auto;
                            padding: 0 20px;
                            scroll-behavior: smooth;
                        }

                        .stories-container::-webkit-scrollbar, .live-container::-webkit-scrollbar {
                            display: none;
                        }

                        .story-item, .live-item {
                            flex-shrink: 0;
                            text-align: center;
                            cursor: pointer;
                        }

                        .story-avatar, .live-avatar {
                            width: 60px;
                            height: 60px;
                            border-radius: 50%;
                            margin: 0 auto 8px;
                            position: relative;
                            background: #f0f0f0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }

                        .story-avatar.has-story {
                            border: 3px solid #16CCC8;
                        }

                        .story-avatar.no-story {
                            border: 3px solid #ccc;
                        }

                        .live-avatar.is-live {
                            border: 3px solid;
                            border-image: linear-gradient(45deg, #ff0000, #ff8c00, #ffd700, #9932cc) 1;
                            animation: live-pulse 2s ease-in-out infinite;
                        }

                        .live-avatar.no-live {
                            border: 3px solid #ccc;
                        }

                        @keyframes live-pulse {
                            0%, 100% { transform: scale(1); }
                            50% { transform: scale(1.05); }
                        }

                        .live-indicator {
                            position: absolute;
                            bottom: -2px;
                            right: -2px;
                            background: #ff0000;
                            color: white;
                            font-size: 8px;
                            padding: 2px 4px;
                            border-radius: 8px;
                            font-weight: bold;
                        }

                        .story-name {
                            font-size: 12px;
                            color: #333;
                            max-width: 70px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        .create-post {
                            background: white;
                            border-radius: 8px;
                            padding: 15px;
                            margin: 0 20px 15px;
                            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                        }

                        .create-post-top {
                            display: flex;
                            align-items: center;
                            gap: 12px;
                            margin-bottom: 15px;
                        }

                        .create-post-avatar {
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                        }

                        .create-post-input {
                            flex: 1;
                            background: #f0f2f5;
                            border: none;
                            border-radius: 25px;
                            padding: 12px 16px;
                            font-family: 'Cairo', sans-serif;
                            font-size: 16px;
                            cursor: pointer;
                        }

                        .create-post-actions {
                            display: flex;
                            justify-content: space-around;
                            border-top: 1px solid #dadde1;
                            padding-top: 10px;
                        }

                        .post-action {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            padding: 8px 12px;
                            border-radius: 6px;
                            cursor: pointer;
                            color: #65676b;
                            font-weight: 600;
                            transition: background 0.3s;
                        }

                        .post-action:hover {
                            background: #f0f2f5;
                        }

                        .posts-container {
                            max-width: 680px;
                            margin: 0 auto;
                            padding: 0 20px;
                        }

                        .post {
                            background: white;
                            border-radius: 8px;
                            margin-bottom: 20px;
                            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                        }

                        .post-header {
                            padding: 15px 15px 0;
                            display: flex;
                            align-items: center;
                            gap: 12px;
                        }

                        .post-avatar {
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                        }

                        .post-info {
                            flex: 1;
                        }

                        .post-author {
                            font-weight: 600;
                            color: #1c1e21;
                            font-size: 15px;
                        }

                        .post-time {
                            color: #65676b;
                            font-size: 13px;
                        }

                        .post-content {
                            padding: 15px;
                            color: #1c1e21;
                            line-height: 1.5;
                        }

                        .post-image {
                            width: 100%;
                            max-height: 400px;
                            object-fit: cover;
                        }

                        .post-stats {
                            padding: 0 15px;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            color: #65676b;
                            font-size: 15px;
                            border-bottom: 1px solid #dadde1;
                            padding-bottom: 10px;
                        }

                        .post-actions {
                            display: flex;
                            justify-content: space-around;
                            padding: 8px 0;
                        }

                        .post-action-btn {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            padding: 8px 12px;
                            border-radius: 6px;
                            cursor: pointer;
                            color: #65676b;
                            font-weight: 600;
                            transition: background 0.3s;
                            border: none;
                            background: none;
                            font-family: 'Cairo', sans-serif;
                        }

                        .post-action-btn:hover {
                            background: #f0f2f5;
                        }

                        .post-action-btn.liked {
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            background-clip: text;
                        }
                    </style>
                </head>
                <body>
                    <div class="service-header">
                        <button class="back-btn" onclick="history.back()">
                            <i class="fas fa-arrow-right"></i>
                        </button>
                        <div class="service-title">${serviceName}</div>
                        <div class="logo-small">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                    </div>

                    <!-- شريط القصص -->
                    <div class="stories-section">
                        <div class="stories-container">
                            <div class="story-item">
                                <div class="story-avatar has-story">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                </div>
                                <div class="story-name">أحمد محمد</div>
                            </div>
                            <div class="story-item">
                                <div class="story-avatar no-story">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                </div>
                                <div class="story-name">فاطمة علي</div>
                            </div>
                            <div class="story-item">
                                <div class="story-avatar has-story">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                </div>
                                <div class="story-name">محمد حسن</div>
                            </div>
                        </div>
                    </div>

                    <!-- شريط البث المباشر -->
                    <div class="live-section">
                        <div class="live-container">
                            <div class="live-item">
                                <div class="live-avatar is-live">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                    <div class="live-indicator">LIVE</div>
                                </div>
                                <div class="story-name">متجر ${serviceName}</div>
                            </div>
                            <div class="live-item">
                                <div class="live-avatar no-live">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                </div>
                                <div class="story-name">شركة النور</div>
                            </div>
                        </div>
                    </div>

                    <div class="posts-container">
                        <!-- منطقة إنشاء المنشور -->
                        <div class="create-post">
                            <div class="create-post-top">
                                <div class="create-post-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <input type="text" class="create-post-input" placeholder="ماذا تريد أن تشارك في ${serviceName}؟">
                            </div>
                            <div class="create-post-actions">
                                <div class="post-action">
                                    <i class="fas fa-images" style="color: #45bd62;"></i>
                                    <span>صورة/فيديو</span>
                                </div>
                                <div class="post-action">
                                    <i class="fas fa-map-marker-alt" style="color: #f02849;"></i>
                                    <span>موقع</span>
                                </div>
                                <div class="post-action">
                                    <i class="fas fa-tag" style="color: #f7b928;"></i>
                                    <span>عرض خاص</span>
                                </div>
                            </div>
                        </div>

                        <!-- المنشورات -->
                        <div class="post">
                            <div class="post-header">
                                <div class="post-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="post-info">
                                    <div class="post-author">شركة ${serviceName} المميزة</div>
                                    <div class="post-time">منذ ساعتين • <i class="fas fa-globe-americas"></i></div>
                                </div>
                            </div>
                            <div class="post-content">
                                عرض خاص في ${serviceName}!

                                ✨ خصم 20% على جميع المنتجات
                                🎯 جودة عالية وأسعار منافسة
                                🚚 توصيل مجاني داخل المدينة
                                📞 للطلب والاستفسار تواصل معنا

                                #${serviceName} #عروض_خاصة #جودة_عالية
                            </div>
                            <div class="post-stats">
                                <div>
                                    <i class="fas fa-heart" style="color: #16CCC8;"></i>
                                    45 إعجاب
                                </div>
                                <div>12 طلب سعر • 5 مشاركات</div>
                            </div>
                            <div class="post-actions">
                                <button class="post-action-btn" onclick="toggleLike(this)">
                                    <i class="fas fa-heart"></i>
                                    <span>إعجاب</span>
                                </button>
                                <button class="post-action-btn" onclick="requestPrice()">
                                    <i class="fas fa-comment-dollar"></i>
                                    <span>طلب السعر</span>
                                </button>
                                <button class="post-action-btn" onclick="sharePost()">
                                    <i class="fas fa-share"></i>
                                    <span>مشاركة</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <script>
                        function toggleLike(button) {
                            button.classList.toggle('liked');
                        }

                        function requestPrice() {
                            alert('تم إرسال طلب السعر - سيتم الرد عليك في رسالة خاصة');
                        }

                        function sharePost() {
                            alert('تم مشاركة المنشور');
                        }
                    </script>
                </body>
                </html>
            `;

            // فتح الصفحة في نافذة جديدة
            const newWindow = window.open('', '_blank');
            newWindow.document.write(servicePageHTML);
            newWindow.document.close();
        }

        // وظائف أخرى
        function toggleUserMenu() {
            alert('قائمة المستخدم - سيتم إضافة الخصائص لاحقاً');
        }

        function performSearch() {
            const searchInput = document.getElementById('searchInput');
            alert(`البحث عن: ${searchInput.value}`);
        }

        function switchTab(tab) {
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');
        }

        function openNotifications() {
            alert('فتح الإشعارات - سيتم تطويرها لاحقاً');
        }

        function openMessages() {
            alert('فتح الرسائل - سيتم تطويرها لاحقاً');
        }

        function openProfile() {
            alert('فتح الملف الشخصي - سيتم تطويرها لاحقاً');
        }

        function watchAd(index) {
            alert('مشاهدة الإعلان - تم إضافة نقاط إلى حسابك');
        }

        // بيانات القصص
        const stories = [
            { name: 'قصتك', hasStory: false, isAdd: true },
            { name: 'أحمد محمد', hasStory: true, isAdd: false },
            { name: 'فاطمة علي', hasStory: false, isAdd: false },
            { name: 'محمد حسن', hasStory: true, isAdd: false },
            { name: 'سارة أحمد', hasStory: false, isAdd: false },
            { name: 'علي حسين', hasStory: true, isAdd: false }
        ];

        // بيانات البث المباشر
        const liveStreams = [
            { name: 'متجر الإلكترونيات', isLive: true },
            { name: 'مطعم الأصالة', isLive: false },
            { name: 'صيدلية النور', isLive: true },
            { name: 'معرض السيارات', isLive: false },
            { name: 'مركز التجميل', isLive: true }
        ];

        // ملء القصص
        function populateStories() {
            const container = document.getElementById('storiesContainer');
            container.innerHTML = stories.map(story => `
                <div class="story-item" onclick="openStory('${story.name}')">
                    <div class="story-avatar ${story.hasStory ? 'has-story' : 'no-story'}">
                        <div class="story-inner">
                            ${story.isAdd ? '<i class="fas fa-plus" style="color: #227FCC;"></i>' : '<i class="fas fa-user"></i>'}
                        </div>
                    </div>
                    <div class="story-name">${story.name}</div>
                </div>
            `).join('');
        }

        // ملء البث المباشر
        function populateLiveStreams() {
            const container = document.getElementById('liveContainer');
            container.innerHTML = liveStreams.map(stream => `
                <div class="live-item" onclick="openLiveStream('${stream.name}')">
                    <div class="live-avatar ${stream.isLive ? 'is-live' : 'no-live'}">
                        <i class="fas fa-user"></i>
                        ${stream.isLive ? '<div class="live-indicator">LIVE</div>' : ''}
                    </div>
                    <div class="story-name">${stream.name}</div>
                </div>
            `).join('');
        }

        function openStory(userName) {
            alert(`فتح قصة ${userName}`);
        }

        function openLiveStream(userName) {
            alert(`فتح بث ${userName} المباشر`);
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            populateServices();
            populateStories();
            populateLiveStreams();
        });
    </script>
</body>
</html>
