import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [
    Locale('ar', ''), // Arabic
    Locale('en', ''), // English
    Locale('ku', ''), // Kurdish
  ];

  // App Title
  String get appTitle {
    switch (locale.languageCode) {
      case 'ar':
        return 'Get Me - وصّلني';
      case 'ku':
        return 'Get Me - بگەیەنە';
      default:
        return 'Get Me';
    }
  }

  // Business Categories
  String get restaurants {
    switch (locale.languageCode) {
      case 'ar':
        return 'مطاعم';
      case 'ku':
        return 'چێشتخانەکان';
      default:
        return 'Restaurants';
    }
  }

  String get pharmacies {
    switch (locale.languageCode) {
      case 'ar':
        return 'صيدليات';
      case 'ku':
        return 'دەرمانخانەکان';
      default:
        return 'Pharmacies';
    }
  }

  String get beautyCenter {
    switch (locale.languageCode) {
      case 'ar':
        return 'مراكز تجميل';
      case 'ku':
        return 'ناوەندەکانی جوانکاری';
      default:
        return 'Beauty Centers';
    }
  }

  String get clothing {
    switch (locale.languageCode) {
      case 'ar':
        return 'ملابس';
      case 'ku':
        return 'جلوبەرگ';
      default:
        return 'Clothing';
    }
  }

  String get electronics {
    switch (locale.languageCode) {
      case 'ar':
        return 'إلكترونيات';
      case 'ku':
        return 'ئەلیکترۆنیات';
      default:
        return 'Electronics';
    }
  }

  String get doctors {
    switch (locale.languageCode) {
      case 'ar':
        return 'أطباء';
      case 'ku':
        return 'پزیشکان';
      default:
        return 'Doctors';
    }
  }

  String get education {
    switch (locale.languageCode) {
      case 'ar':
        return 'تعليم';
      case 'ku':
        return 'پەروەردە';
      default:
        return 'Education';
    }
  }

  String get cars {
    switch (locale.languageCode) {
      case 'ar':
        return 'سيارات';
      case 'ku':
        return 'ئۆتۆمبێل';
      default:
        return 'Cars';
    }
  }

  String get realEstate {
    switch (locale.languageCode) {
      case 'ar':
        return 'عقارات';
      case 'ku':
        return 'خانووبەرە';
      default:
        return 'Real Estate';
    }
  }

  String get sports {
    switch (locale.languageCode) {
      case 'ar':
        return 'رياضة';
      case 'ku':
        return 'وەرزش';
      default:
        return 'Sports';
    }
  }

  String get travel {
    switch (locale.languageCode) {
      case 'ar':
        return 'سفر وسياحة';
      case 'ku':
        return 'گەشت و گەشتیاری';
      default:
        return 'Travel & Tourism';
    }
  }

  String get jewelry {
    switch (locale.languageCode) {
      case 'ar':
        return 'مجوهرات';
      case 'ku':
        return 'زێڕەوزیو';
      default:
        return 'Jewelry';
    }
  }

  String get furniture {
    switch (locale.languageCode) {
      case 'ar':
        return 'أثاث';
      case 'ku':
        return 'کەلوپەل';
      default:
        return 'Furniture';
    }
  }

  String get books {
    switch (locale.languageCode) {
      case 'ar':
        return 'كتب ومكتبات';
      case 'ku':
        return 'کتێب و کتێبخانە';
      default:
        return 'Books & Libraries';
    }
  }

  String get pets {
    switch (locale.languageCode) {
      case 'ar':
        return 'حيوانات أليفة';
      case 'ku':
        return 'ئاژەڵی ماڵی';
      default:
        return 'Pets';
    }
  }

  String get toys {
    switch (locale.languageCode) {
      case 'ar':
        return 'ألعاب';
      case 'ku':
        return 'یاری';
      default:
        return 'Toys';
    }
  }

  String get plants {
    switch (locale.languageCode) {
      case 'ar':
        return 'نباتات وحدائق';
      case 'ku':
        return 'ڕووەک و باخچە';
      default:
        return 'Plants & Gardens';
    }
  }

  String get homeServices {
    switch (locale.languageCode) {
      case 'ar':
        return 'خدمات منزلية';
      case 'ku':
        return 'خزمەتگوزاری ماڵەوە';
      default:
        return 'Home Services';
    }
  }

  String get groceries {
    switch (locale.languageCode) {
      case 'ar':
        return 'مواد غذائية';
      case 'ku':
        return 'کەلوپەلی خۆراک';
      default:
        return 'Groceries';
    }
  }

  String get otherServices {
    switch (locale.languageCode) {
      case 'ar':
        return 'خدمات أخرى';
      case 'ku':
        return 'خزمەتگوزاری تر';
      default:
        return 'Other Services';
    }
  }

  // UI Elements
  String get featuredOffers {
    switch (locale.languageCode) {
      case 'ar':
        return 'العروض المميزة';
      case 'ku':
        return 'پێشکەشکراوە تایبەتەکان';
      default:
        return 'Featured Offers';
    }
  }

  String get watchAds {
    switch (locale.languageCode) {
      case 'ar':
        return 'مشاهدة الإعلانات';
      case 'ku':
        return 'بینینی ڕیکلامەکان';
      default:
        return 'Watch Ads';
    }
  }

  String get categoryClicked {
    switch (locale.languageCode) {
      case 'ar':
        return 'تم النقر على';
      case 'ku':
        return 'کرتەکرا لەسەر';
      default:
        return 'Clicked on';
    }
  }
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['ar', 'en', 'ku'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
