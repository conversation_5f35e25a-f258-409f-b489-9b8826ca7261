<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - وصّلني | شبيه بالفيسبوك</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }

        /* Header */
        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #2196F3, #03DAC6);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #2196F3;
        }

        .search-bar {
            flex: 1;
            max-width: 400px;
            margin: 0 20px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 10px 40px 10px 15px;
            border: 1px solid #ddd;
            border-radius: 25px;
            background: #f5f5f5;
            font-family: 'Cairo', sans-serif;
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .header-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: #f5f5f5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.3s;
        }

        .header-btn:hover {
            background: #e0e0e0;
        }

        /* Main Content */
        .main-content {
            margin-top: 60px;
            display: flex;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
            gap: 20px;
            padding: 20px;
        }

        /* Feed */
        .feed {
            flex: 1;
            max-width: 600px;
        }

        /* Stories */
        .stories {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stories-list {
            display: flex;
            gap: 10px;
            overflow-x: auto;
        }

        .story-item {
            min-width: 80px;
            text-align: center;
            cursor: pointer;
        }

        .story-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #2196F3, #03DAC6);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            position: relative;
        }

        .story-avatar img {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .story-name {
            font-size: 12px;
            color: #333;
        }

        /* Create Post */
        .create-post {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .create-post-top {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
        }

        .post-input {
            flex: 1;
            padding: 12px 20px;
            border: 1px solid #ddd;
            border-radius: 25px;
            background: #f5f5f5;
            cursor: pointer;
            color: #666;
        }

        .create-post-actions {
            display: flex;
            justify-content: space-around;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .post-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .post-action:hover {
            background: #f5f5f5;
        }

        /* Post Card */
        .post-card {
            background: white;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .post-header {
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .post-user-info h4 {
            margin: 0;
            font-size: 16px;
            color: #333;
        }

        .post-time {
            font-size: 12px;
            color: #666;
        }

        .post-content {
            padding: 0 15px 15px;
            line-height: 1.5;
            color: #333;
        }

        .post-image {
            width: 100%;
            max-height: 400px;
            object-fit: cover;
        }

        .post-stats {
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            border-top: 1px solid #eee;
            border-bottom: 1px solid #eee;
            font-size: 14px;
            color: #666;
        }

        .post-actions {
            display: flex;
            justify-content: space-around;
            padding: 10px;
        }

        .post-action-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: none;
            background: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s;
            font-family: 'Cairo', sans-serif;
            color: #666;
        }

        .post-action-btn:hover {
            background: #f5f5f5;
        }

        .post-action-btn.liked {
            color: #e74c3c;
        }

        /* Sidebar */
        .sidebar {
            width: 300px;
        }

        .sidebar-section {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .category-item {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .category-item:hover {
            transform: translateY(-2px);
        }

        .category-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            color: white;
            font-size: 18px;
        }

        .category-name {
            font-size: 12px;
            color: #333;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            z-index: 1000;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: background 0.3s;
        }

        .nav-item:hover {
            background: #f5f5f5;
        }

        .nav-item.active {
            color: #2196F3;
        }

        .nav-icon {
            font-size: 24px;
        }

        .nav-label {
            font-size: 12px;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 8px;
            height: 8px;
            background: #e74c3c;
            border-radius: 50%;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
                padding: 10px;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .search-bar {
                display: none;
            }
            
            .header {
                padding: 0 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="logo">
            <div class="logo-icon">
                <i class="fas fa-shopping-bag"></i>
            </div>
            <div class="logo-text">Get Me</div>
        </div>
        
        <div class="search-bar">
            <input type="text" class="search-input" placeholder="البحث في Get Me...">
            <i class="fas fa-search search-icon"></i>
        </div>
        
        <div class="header-actions">
            <button class="header-btn">
                <i class="fas fa-bell"></i>
            </button>
            <button class="header-btn">
                <i class="fas fa-envelope"></i>
            </button>
            <button class="header-btn">
                <i class="fas fa-user-circle"></i>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Feed -->
        <div class="feed">
            <!-- Stories -->
            <div class="stories">
                <div class="stories-list">
                    <div class="story-item">
                        <div class="story-avatar">
                            <i class="fas fa-plus" style="color: white;"></i>
                        </div>
                        <div class="story-name">قصتك</div>
                    </div>
                    <div class="story-item">
                        <div class="story-avatar">
                            <img src="https://via.placeholder.com/60" alt="User">
                        </div>
                        <div class="story-name">أحمد محمد</div>
                    </div>
                    <div class="story-item">
                        <div class="story-avatar">
                            <img src="https://via.placeholder.com/60" alt="User">
                        </div>
                        <div class="story-name">فاطمة أحمد</div>
                    </div>
                    <div class="story-item">
                        <div class="story-avatar">
                            <img src="https://via.placeholder.com/60" alt="User">
                        </div>
                        <div class="story-name">محمد علي</div>
                    </div>
                </div>
            </div>

            <!-- Create Post -->
            <div class="create-post">
                <div class="create-post-top">
                    <div class="user-avatar"></div>
                    <div class="post-input" onclick="showCreatePost()">ماذا تريد أن تشارك؟</div>
                </div>
                <div class="create-post-actions">
                    <div class="post-action">
                        <i class="fas fa-image" style="color: #4CAF50;"></i>
                        <span>صورة/فيديو</span>
                    </div>
                    <div class="post-action">
                        <i class="fas fa-map-marker-alt" style="color: #F44336;"></i>
                        <span>موقع</span>
                    </div>
                    <div class="post-action">
                        <i class="fas fa-tag" style="color: #FF9800;"></i>
                        <span>سعر</span>
                    </div>
                </div>
            </div>

            <!-- Posts -->
            <div id="posts-container">
                <!-- Posts will be populated by JavaScript -->
            </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Categories -->
            <div class="sidebar-section">
                <div class="sidebar-title">الأقسام التجارية</div>
                <div class="category-grid" id="categories-grid">
                    <!-- Categories will be populated by JavaScript -->
                </div>
            </div>

            <!-- Featured Offers -->
            <div class="sidebar-section">
                <div class="sidebar-title">العروض المميزة</div>
                <div style="text-align: center; padding: 20px;">
                    <i class="fas fa-star" style="font-size: 48px; color: #FFD700; margin-bottom: 10px;"></i>
                    <p>اكتشف أفضل العروض والخصومات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="nav-item active" onclick="switchTab('feed')">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-label">الرئيسية</span>
        </div>
        <div class="nav-item" onclick="switchTab('categories')">
            <i class="fas fa-th nav-icon"></i>
            <span class="nav-label">الأقسام</span>
        </div>
        <div class="nav-item" onclick="switchTab('notifications')">
            <div style="position: relative;">
                <i class="fas fa-bell nav-icon"></i>
                <div class="notification-badge"></div>
            </div>
            <span class="nav-label">الإشعارات</span>
        </div>
        <div class="nav-item" onclick="switchTab('messages')">
            <div style="position: relative;">
                <i class="fas fa-envelope nav-icon"></i>
                <div class="notification-badge"></div>
            </div>
            <span class="nav-label">الرسائل</span>
        </div>
        <div class="nav-item" onclick="switchTab('profile')">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-label">الملف الشخصي</span>
        </div>
    </div>

    <script>
        // Sample data
        const categories = [
            { name: 'مطاعم', icon: 'fas fa-utensils', color: '#FF9800' },
            { name: 'صيدليات', icon: 'fas fa-pills', color: '#2196F3' },
            { name: 'تجميل', icon: 'fas fa-spa', color: '#E91E63' },
            { name: 'ملابس', icon: 'fas fa-tshirt', color: '#9C27B0' },
            { name: 'إلكترونيات', icon: 'fas fa-laptop', color: '#4CAF50' },
            { name: 'أطباء', icon: 'fas fa-user-md', color: '#F44336' },
            { name: 'تعليم', icon: 'fas fa-graduation-cap', color: '#3F51B5' },
            { name: 'سيارات', icon: 'fas fa-car', color: '#607D8B' },
            { name: 'عقارات', icon: 'fas fa-home', color: '#795548' }
        ];

        const posts = [
            {
                user: 'أحمد محمد',
                time: 'منذ ساعتين',
                content: 'هاتف iPhone 15 Pro Max جديد للبيع! حالة ممتازة مع جميع الملحقات الأصلية. السعر قابل للتفاوض.',
                image: 'https://via.placeholder.com/500x300',
                likes: 45,
                comments: 12,
                shares: 3,
                category: 'إلكترونيات'
            },
            {
                user: 'فاطمة أحمد',
                time: 'منذ 4 ساعات',
                content: 'مطعم جديد في منطقة الجادرية! نقدم أشهى الأطباق العراقية والعربية. افتتاح خاص مع خصم 20% على جميع الوجبات.',
                image: 'https://via.placeholder.com/500x300',
                likes: 89,
                comments: 23,
                shares: 15,
                category: 'مطاعم'
            }
        ];

        // Populate categories
        function populateCategories() {
            const grid = document.getElementById('categories-grid');
            grid.innerHTML = categories.map(cat => `
                <div class="category-item" onclick="showNotification('تم النقر على ${cat.name}')">
                    <div class="category-icon" style="background-color: ${cat.color}">
                        <i class="${cat.icon}"></i>
                    </div>
                    <div class="category-name">${cat.name}</div>
                </div>
            `).join('');
        }

        // Populate posts
        function populatePosts() {
            const container = document.getElementById('posts-container');
            container.innerHTML = posts.map(post => `
                <div class="post-card">
                    <div class="post-header">
                        <div class="user-avatar"></div>
                        <div class="post-user-info">
                            <h4>${post.user}</h4>
                            <div class="post-time">${post.time} • ${post.category}</div>
                        </div>
                    </div>
                    <div class="post-content">${post.content}</div>
                    <img src="${post.image}" alt="Post image" class="post-image">
                    <div class="post-stats">
                        <span>${post.likes} إعجاب</span>
                        <span>${post.comments} تعليق • ${post.shares} مشاركة</span>
                    </div>
                    <div class="post-actions">
                        <button class="post-action-btn" onclick="likePost(this)">
                            <i class="far fa-heart"></i>
                            <span>إعجاب</span>
                        </button>
                        <button class="post-action-btn" onclick="showNotification('فتح التعليقات')">
                            <i class="far fa-comment"></i>
                            <span>تعليق</span>
                        </button>
                        <button class="post-action-btn" onclick="showNotification('تم مشاركة المنشور')">
                            <i class="far fa-share"></i>
                            <span>مشاركة</span>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Functions
        function showCreatePost() {
            showNotification('فتح نافذة إنشاء منشور جديد');
        }

        function likePost(button) {
            const icon = button.querySelector('i');
            const span = button.querySelector('span');
            
            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                button.classList.add('liked');
                span.textContent = 'أعجبني';
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                button.classList.remove('liked');
                span.textContent = 'إعجاب';
            }
        }

        function switchTab(tab) {
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to clicked item
            event.currentTarget.classList.add('active');
            
            showNotification(`تم التبديل إلى تبويب ${getTabName(tab)}`);
        }

        function getTabName(tab) {
            const names = {
                'feed': 'الرئيسية',
                'categories': 'الأقسام',
                'notifications': 'الإشعارات',
                'messages': 'الرسائل',
                'profile': 'الملف الشخصي'
            };
            return names[tab] || tab;
        }

        function showNotification(message) {
            // Create notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                bottom: 80px;
                right: 20px;
                background: #333;
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 5px 20px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: 'Cairo', sans-serif;
                max-width: 300px;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            populateCategories();
            populatePosts();
        });
    </script>
</body>
</html>
