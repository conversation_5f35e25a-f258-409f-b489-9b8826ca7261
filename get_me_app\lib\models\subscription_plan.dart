import 'package:flutter/material.dart';

enum PlanType {
  buyer,
  seller,
  premium,
  guest,
}

class SubscriptionPlan {
  final String id;
  final String title;
  final String description;
  final double price;
  final PlanType type;
  final List<String> features;
  final Color primaryColor;
  final Color secondaryColor;
  final IconData icon;
  final int durationDays;
  final bool isPopular;

  SubscriptionPlan({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.type,
    required this.features,
    required this.primaryColor,
    required this.secondaryColor,
    required this.icon,
    this.durationDays = 30,
    this.isPopular = false,
  });

  static List<SubscriptionPlan> getPlans() {
    return [
      SubscriptionPlan(
        id: 'buyer',
        title: 'مشتري',
        description: 'للتسوق والشراء',
        price: 5.0,
        type: PlanType.buyer,
        features: [
          'تصفح جميع المنتجات',
          'إضافة للمفضلة',
          'التواصل مع البائعين',
          'تقييم المنتجات',
          'دعم فني أساسي',
        ],
        primaryColor: const Color(0xFF4CAF50),
        secondaryColor: const Color(0xFF81C784),
        icon: Icons.shopping_cart_rounded,
      ),
      SubscriptionPlan(
        id: 'seller',
        title: 'بائع',
        description: 'لبيع المنتجات والخدمات',
        price: 10.0,
        type: PlanType.seller,
        features: [
          'جميع مميزات المشتري',
          'إضافة منتجات غير محدودة',
          'إدارة المتجر',
          'تحليلات المبيعات',
          'دعم فني متقدم',
          'ترويج المنتجات',
        ],
        primaryColor: const Color(0xFF2196F3),
        secondaryColor: const Color(0xFF64B5F6),
        icon: Icons.store_rounded,
        isPopular: true,
      ),
      SubscriptionPlan(
        id: 'premium',
        title: 'بائع مميز',
        description: 'عرض خاص لمدة يومين',
        price: 25.0,
        type: PlanType.premium,
        features: [
          'جميع مميزات البائع',
          'عرض مميز في الصفحة الرئيسية',
          'أولوية في نتائج البحث',
          'شارة "بائع مميز"',
          'دعم فني VIP',
          'تحليلات متقدمة',
          'إعلانات مجانية',
        ],
        primaryColor: const Color(0xFFFF9800),
        secondaryColor: const Color(0xFFFFB74D),
        icon: Icons.star_rounded,
        durationDays: 2,
      ),
      SubscriptionPlan(
        id: 'guest',
        title: 'زائر',
        description: 'تجربة مجانية لمدة أسبوع',
        price: 0.0,
        type: PlanType.guest,
        features: [
          'تصفح محدود',
          'عرض المنتجات فقط',
          'بدون تفاعل',
          'بدون دعم فني',
        ],
        primaryColor: const Color(0xFF9E9E9E),
        secondaryColor: const Color(0xFFBDBDBD),
        icon: Icons.visibility_rounded,
        durationDays: 7,
      ),
    ];
  }

  String get formattedPrice {
    if (price == 0) return 'مجاني';
    return '\$${price.toStringAsFixed(0)}';
  }

  String get durationText {
    if (durationDays == 1) return 'يوم واحد';
    if (durationDays == 2) return 'يومين';
    if (durationDays == 7) return 'أسبوع';
    if (durationDays == 30) return 'شهر';
    return '$durationDays أيام';
  }
}

class UserRegistrationData {
  // Basic Info
  String? fullName;
  DateTime? birthDate;
  String? phone;
  String? email;
  String? location;
  double? latitude;
  double? longitude;

  // Business Info (for sellers)
  String? companyName;
  String? businessType;
  String? city;
  String? province;
  Map<String, String>? socialAccounts;

  // Identity Verification
  String? identityImagePath;
  bool isIdentityVerified = false;

  // Account Info
  String? username;
  String? password;

  // Selected Plan
  SubscriptionPlan? selectedPlan;

  // Terms & Conditions
  bool hasAcceptedTerms = false;

  UserRegistrationData();

  bool get isBasicInfoComplete {
    return fullName != null &&
        birthDate != null &&
        phone != null &&
        email != null &&
        location != null;
  }

  bool get isBusinessInfoComplete {
    if (selectedPlan?.type == PlanType.buyer || selectedPlan?.type == PlanType.guest) {
      return true;
    }
    return companyName != null &&
        businessType != null &&
        city != null &&
        province != null;
  }

  bool get isIdentityComplete {
    return identityImagePath != null && isIdentityVerified;
  }

  bool get isAccountInfoComplete {
    return username != null && password != null;
  }

  bool get isComplete {
    return isBasicInfoComplete &&
        isBusinessInfoComplete &&
        isIdentityComplete &&
        isAccountInfoComplete &&
        hasAcceptedTerms &&
        selectedPlan != null;
  }

  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'birthDate': birthDate?.toIso8601String(),
      'phone': phone,
      'email': email,
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
      'companyName': companyName,
      'businessType': businessType,
      'city': city,
      'province': province,
      'socialAccounts': socialAccounts,
      'identityImagePath': identityImagePath,
      'isIdentityVerified': isIdentityVerified,
      'username': username,
      'selectedPlan': selectedPlan?.id,
      'hasAcceptedTerms': hasAcceptedTerms,
    };
  }
}
