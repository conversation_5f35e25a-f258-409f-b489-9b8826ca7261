<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - وصّلني</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #2196F3 0%, #03DAC6 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .logo {
            display: inline-flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .logo-icon i {
            font-size: 30px;
            color: #2196F3;
        }

        .app-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 10px;
        }

        .main-content {
            background: white;
            border-radius: 30px 30px 0 0;
            padding: 30px 20px;
            min-height: 70vh;
            box-shadow: 0 -10px 30px rgba(0,0,0,0.1);
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .category-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .category-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
        }

        .category-name {
            font-size: 0.9rem;
            font-weight: 600;
            color: #333;
            line-height: 1.3;
        }

        .featured-section {
            background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            color: white;
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .featured-section:hover {
            transform: scale(1.02);
        }

        .ads-section {
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            color: white;
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .ads-section:hover {
            transform: scale(1.02);
        }

        .section-icon {
            font-size: 30px;
        }

        .section-content h3 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .section-content p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            transform: translateY(0);
            opacity: 1;
        }

        @media (max-width: 768px) {
            .categories-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
            }
            
            .category-card {
                padding: 15px 10px;
            }
            
            .category-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
            
            .category-name {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <h1 class="app-title">Get Me - وصّلني</h1>
            </div>
            <p class="subtitle">منصة تجارية متكاملة للخدمات والمنتجات</p>
        </div>

        <div class="main-content">
            <div class="categories-grid" id="categoriesGrid">
                <!-- Categories will be populated by JavaScript -->
            </div>

            <div class="featured-section" onclick="showNotification('تم النقر على العروض المميزة', '#FFD700')">
                <i class="fas fa-star section-icon"></i>
                <div class="section-content">
                    <h3>العروض المميزة</h3>
                    <p>اكتشف أفضل العروض والخصومات</p>
                </div>
                <i class="fas fa-chevron-left" style="margin-right: auto;"></i>
            </div>

            <div class="ads-section" onclick="showNotification('تم النقر على مشاهدة الإعلانات', '#4CAF50')">
                <i class="fas fa-play-circle section-icon"></i>
                <div class="section-content">
                    <h3>مشاهدة الإعلانات</h3>
                    <p>شاهد واربح مبالغ مالية</p>
                </div>
                <i class="fas fa-chevron-left" style="margin-right: auto;"></i>
            </div>
        </div>
    </div>

    <div class="notification" id="notification"></div>

    <script>
        const categories = [
            { name: 'مطاعم', icon: 'fas fa-utensils', color: '#FF9800' },
            { name: 'صيدليات', icon: 'fas fa-pills', color: '#2196F3' },
            { name: 'مراكز تجميل', icon: 'fas fa-spa', color: '#E91E63' },
            { name: 'ملابس', icon: 'fas fa-tshirt', color: '#9C27B0' },
            { name: 'إلكترونيات', icon: 'fas fa-laptop', color: '#4CAF50' },
            { name: 'أطباء', icon: 'fas fa-user-md', color: '#F44336' },
            { name: 'تعليم', icon: 'fas fa-graduation-cap', color: '#3F51B5' },
            { name: 'سيارات', icon: 'fas fa-car', color: '#607D8B' },
            { name: 'عقارات', icon: 'fas fa-home', color: '#795548' },
            { name: 'رياضة', icon: 'fas fa-futbol', color: '#FF5722' },
            { name: 'سفر وسياحة', icon: 'fas fa-plane', color: '#00BCD4' },
            { name: 'مجوهرات', icon: 'fas fa-gem', color: '#FFD700' },
            { name: 'أثاث', icon: 'fas fa-chair', color: '#8BC34A' },
            { name: 'كتب ومكتبات', icon: 'fas fa-book', color: '#673AB7' },
            { name: 'كافيهات', icon: 'fas fa-coffee', color: '#795548' },
            { name: 'مصارف وبنوك', icon: 'fas fa-university', color: '#607D8B' },
            { name: 'نباتات وحدائق', icon: 'fas fa-seedling', color: '#4CAF50' },
            { name: 'خدمات توصيل', icon: 'fas fa-truck', color: '#FF5722' },
            { name: 'مواد غذائية', icon: 'fas fa-shopping-cart', color: '#8BC34A' },
            { name: 'الصناعة', icon: 'fas fa-industry', color: '#9C27B0' }
        ];

        function createCategoryCard(category) {
            return `
                <div class="category-card" onclick="showNotification('تم النقر على ${category.name}', '${category.color}')">
                    <div class="category-icon" style="background-color: ${category.color}">
                        <i class="${category.icon}"></i>
                    </div>
                    <div class="category-name">${category.name}</div>
                </div>
            `;
        }

        function showNotification(message, color) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.style.backgroundColor = color;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Populate categories
        document.addEventListener('DOMContentLoaded', function() {
            const grid = document.getElementById('categoriesGrid');
            grid.innerHTML = categories.map(createCategoryCard).join('');
        });
    </script>
</body>
</html>
