<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - تطبيق الخدمات المحلية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }

        /* الشريط العلوي */
        .header {
            background: linear-gradient(90deg, #16CCC8, #227FCC);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .app-name {
            font-size: 40px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
            max-width: 600px;
            margin: 0 30px;
        }

        .search-input {
            width: 100%;
            height: 40px;
            padding: 12px 20px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            background: rgba(255,255,255,0.9);
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
        }

        .search-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            font-size: 24px;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .menu-icon {
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            position: relative;
        }

        /* القائمة المنسدلة الرئيسية */
        .main-menu-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            min-width: 250px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .main-menu-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: #4a4a4a;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
        }

        .menu-item:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateX(5px);
        }

        .menu-item i {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .menu-item.logout {
            color: #e74c3c;
        }

        .menu-item.logout:hover {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        /* أنماط خاصة لأيقونات الوضع النهاري والليلي */
        .menu-item:has(.fa-moon) i {
            color: #4a5568;
        }

        .menu-item:has(.fa-moon):hover i {
            color: #2d3748;
        }

        .menu-item:has(.fa-sun) i {
            color: #f6ad55;
        }

        .menu-item:has(.fa-sun):hover i {
            color: #ed8936;
        }

        .menu-divider {
            height: 1px;
            background: rgba(0, 0, 0, 0.1);
            margin: 8px 0;
        }

        /* رسوم متحركة للتوست */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid #28a745;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.6);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-name {
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        /* شريط التنقل */
        .nav-bar {
            background: white;
            position: fixed;
            top: 70px;
            left: 0;
            right: 0;
            z-index: 999;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .nav-items {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 25px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 18px;
            cursor: pointer;
            transition: all 0.3s;
            color: #495057;
            font-weight: 500;
            position: relative;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-item i {
            font-size: 24px;
        }

        .nav-item.home-item {
            margin-left: 80px;
        }

        .nav-item.has-notification i {
            color: #ff0000 !important;
            -webkit-text-fill-color: #ff0000 !important;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #ff0000;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 130px;
            padding: 20px;
        }

        /* شريط الخدمات */
        .services-section {
            margin-bottom: 30px;
            position: relative;
        }

        .services-container {
            position: relative;
            overflow: hidden;
            padding: 10px 0;
        }

        .services-wrapper {
            display: flex;
            gap: 15px;
            transition: transform 1s ease;
            width: fit-content;
        }

        .nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 35px;
            height: 35px;
            border: none;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            z-index: 10;
        }

        .nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .nav-arrow.prev {
            right: 10px;
        }

        .nav-arrow.next {
            left: 10px;
        }

        .service-card {
            width: 180px;
            height: 260px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: transform 0.3s;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
        }

        .service-card:hover {
            transform: translateY(-5px);
        }

        .service-image {
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .service-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .service-image-placeholder {
            font-size: 40px;
            color: #666;
        }

        .service-name {
            height: 60px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 15px;
            font-weight: bold;
            text-align: center;
        }

        /* المحتوى */
        .content-area {
            display: flex;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .main-feed {
            flex: 1;
        }

        .sidebar {
            width: 300px;
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        /* القصص */
        .stories-section {
            margin-bottom: 30px;
        }

        .stories-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .stories-container::-webkit-scrollbar {
            display: none;
        }

        .story-item {
            flex-shrink: 0;
            text-align: center;
            cursor: pointer;
        }

        .story-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .story-avatar.has-story {
            border: 3px solid #16CCC8;
        }

        .story-avatar.no-story {
            border: 3px solid #ccc;
        }

        .story-inner {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 20px;
        }

        .story-name {
            font-size: 12px;
            color: #333;
            max-width: 70px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* البث المباشر */
        .live-section {
            margin-bottom: 30px;
        }

        .live-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .live-container::-webkit-scrollbar {
            display: none;
        }

        .live-item {
            flex-shrink: 0;
            text-align: center;
            cursor: pointer;
        }

        .live-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 20px;
        }

        .live-avatar.is-live {
            border: 3px solid;
            border-image: linear-gradient(45deg, #ff0000, #ff8c00, #ffd700, #9932cc) 1;
            animation: live-pulse 2s ease-in-out infinite;
        }

        .live-avatar.no-live {
            border: 3px solid #ccc;
        }

        @keyframes live-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .live-indicator {
            position: absolute;
            bottom: -2px;
            right: -2px;
            background: #ff0000;
            color: white;
            font-size: 8px;
            padding: 2px 4px;
            border-radius: 8px;
            font-weight: bold;
        }

        /* الإعلانات */
        .ads-section {
            margin-bottom: 30px;
        }

        .ads-container {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            border-radius: 15px;
            padding: 20px;
            color: white;
            text-align: center;
            cursor: pointer;
        }

        .ad-text {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .ad-subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        /* المنشورات المميزة */
        .featured-posts {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .featured-content {
            text-align: center;
            color: #666;
            padding: 40px 20px;
        }

        .featured-content i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <div class="header">
        <div class="user-section">
            <div class="menu-icon" onclick="toggleMainMenu()">
                <i class="fas fa-bars"></i>
                <!-- القائمة المنسدلة -->
                <div class="main-menu-dropdown" id="mainMenuDropdown">
                    <div class="menu-item" onclick="toggleDarkMode()">
                        <i class="fas fa-moon"></i>
                        <span>الوضع الليلي</span>
                    </div>
                    <div class="menu-item" onclick="toggleLightMode()">
                        <i class="fas fa-sun"></i>
                        <span>الوضع النهاري</span>
                    </div>
                    <div class="menu-item" onclick="openNotificationSettings()">
                        <i class="fas fa-bell"></i>
                        <span>إعدادات الإشعارات</span>
                    </div>
                    <div class="menu-item" onclick="changeLanguage()">
                        <i class="fas fa-globe"></i>
                        <span>تغيير اللغة</span>
                    </div>
                    <div class="menu-item" onclick="openPrivacySettings()">
                        <i class="fas fa-shield-alt"></i>
                        <span>الخصوصية والأمان</span>
                    </div>
                    <div class="menu-divider"></div>
                    <div class="menu-item" onclick="viewStatistics()">
                        <i class="fas fa-chart-bar"></i>
                        <span>إحصائيات الملف الشخصي</span>
                    </div>
                    <div class="menu-item" onclick="viewEarnings()">
                        <i class="fas fa-dollar-sign"></i>
                        <span>تقرير الأرباح</span>
                    </div>
                    <div class="menu-item" onclick="viewTransactions()">
                        <i class="fas fa-receipt"></i>
                        <span>سجل المعاملات</span>
                    </div>
                    <div class="menu-divider"></div>
                    <div class="menu-item" onclick="upgradeToPremium()">
                        <i class="fas fa-crown"></i>
                        <span>الحساب المميز</span>
                    </div>
                    <div class="menu-item" onclick="customizeProfile()">
                        <i class="fas fa-palette"></i>
                        <span>تخصيص الملف الشخصي</span>
                    </div>
                    <div class="menu-item" onclick="syncData()">
                        <i class="fas fa-sync"></i>
                        <span>مزامنة البيانات</span>
                    </div>
                    <div class="menu-divider"></div>
                    <div class="menu-item" onclick="openHelpCenter()">
                        <i class="fas fa-question-circle"></i>
                        <span>مركز المساعدة</span>
                    </div>
                    <div class="menu-item" onclick="contactSupport()">
                        <i class="fas fa-headset"></i>
                        <span>اتصل بنا</span>
                    </div>
                    <div class="menu-item" onclick="viewTerms()">
                        <i class="fas fa-file-contract"></i>
                        <span>الشروط والأحكام</span>
                    </div>
                    <div class="menu-divider"></div>
                    <div class="menu-item logout" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل الخروج</span>
                    </div>
                </div>
            </div>
            <div class="user-avatar">
                <img src="images/Hussein Nihad.png" alt="Hussein Nihad">
            </div>
            <div class="user-name">Hussein Nihad</div>
        </div>

        <div class="search-section">
            <input type="text" class="search-input" placeholder="اختر نوع الخدمة">
            <button class="search-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>

        <div class="app-name">Get Me</div>
    </div>

    <!-- شريط التنقل -->
    <div class="nav-bar">
        <div class="nav-items">
            <div class="nav-item home-item active">
                <i class="fas fa-home"></i>
                <span>الرئيسية</span>
            </div>
            <div class="nav-item has-notification">
                <i class="fas fa-bell"></i>
                <span>الإشعارات</span>
                <div class="notification-badge">5</div>
            </div>
            <div class="nav-item has-notification">
                <i class="fas fa-envelope"></i>
                <span>الرسائل</span>
                <div class="notification-badge">3</div>
            </div>
            <div class="nav-item">
                <i class="fas fa-user"></i>
                <span>الملف الشخصي</span>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط الخدمات -->
        <div class="services-section">
            <div class="services-container">
                <button class="nav-arrow prev" id="prevBtn">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button class="nav-arrow next" id="nextBtn">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="services-wrapper" id="servicesWrapper">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <div class="content-area">
            <div class="main-feed">
                <!-- القصص -->
                <div class="stories-section">
                    <div class="section-title">القصص</div>
                    <div class="stories-container" id="storiesContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- البث المباشر -->
                <div class="live-section">
                    <div class="section-title">البث المباشر</div>
                    <div class="live-container" id="liveContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <!-- الإعلانات -->
                <div class="ads-section">
                    <div class="section-title">عرض مميز</div>
                    <div class="ads-container">
                        <div class="ad-text">إعلان رقم 1</div>
                        <div class="ad-subtitle">انقر لمشاهدة الإعلان</div>
                    </div>
                </div>

                <!-- المنشورات المميزة -->
                <div class="featured-posts">
                    <div class="section-title">المنشورات المميزة</div>
                    <div class="featured-content">
                        <i class="fas fa-star"></i>
                        <p>المنشورات المميزة ستظهر هنا</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات الخدمات
        const services = [
            { name: 'مطعم', icon: 'fas fa-utensils', image: 'مطعم.jpg' },
            { name: 'طبية', icon: 'fas fa-pills', image: 'طبية.jpg' },
            { name: 'مستحضرات تجميل', icon: 'fas fa-spa', image: 'مستحظرات تجميل.jpg' },
            { name: 'محلات', icon: 'fas fa-tshirt', image: 'محلات.jpg' },
            { name: 'السوبرماركت', icon: 'fas fa-shopping-cart', image: 'السوبرماركت.jpg' },
            { name: 'الطبية', icon: 'fas fa-user-md', image: 'الطبية.jpg' },
            { name: 'تعليم', icon: 'fas fa-graduation-cap', image: 'تعليم.jpg' },
            { name: 'سيارات', icon: 'fas fa-car', image: 'سيارات.jpg' },
            { name: 'عقارات', icon: 'fas fa-home', image: 'عقارات.jpg' },
            { name: 'كافيهات', icon: 'fas fa-coffee', image: 'كافيهات.jpg' },
            { name: 'مصارف وبنوك', icon: 'fas fa-university', image: 'مصارف وبنوك.jpg' },
            { name: 'زراعية', icon: 'fas fa-seedling', image: 'زراعية.jpg' },
            { name: 'توصيل', icon: 'fas fa-truck', image: 'توصيل.jpg' },
            { name: 'الصيرفة', icon: 'fas fa-coins', image: 'الصيرفة.jpg' },
            { name: 'الصناعة', icon: 'fas fa-industry', image: 'الصناعة.jpg' },
            { name: 'ذهب', icon: 'fas fa-gem', image: 'ذهب.jpg' },
            { name: 'قرطاسية', icon: 'fas fa-pen', image: 'قرطاسية.jpg' },
            { name: 'شركات السياحية', icon: 'fas fa-plane', image: 'شركات السياحية.jpg' },
            { name: 'مصايف', icon: 'fas fa-umbrella-beach', image: 'مصايف.jpg' },
            { name: 'استيراد وتصدير', icon: 'fas fa-ship', image: 'استيراد وتصدير.jpg' }
        ];

        // بيانات القصص
        const stories = [
            { name: 'قصتك', hasStory: false, isAdd: true },
            { name: 'أحمد محمد', hasStory: true, isAdd: false },
            { name: 'فاطمة علي', hasStory: false, isAdd: false },
            { name: 'محمد حسن', hasStory: true, isAdd: false },
            { name: 'سارة أحمد', hasStory: false, isAdd: false },
            { name: 'علي حسين', hasStory: true, isAdd: false }
        ];

        // بيانات البث المباشر
        const liveStreams = [
            { name: 'متجر الإلكترونيات', isLive: true },
            { name: 'مطعم الأصالة', isLive: false },
            { name: 'صيدلية النور', isLive: true },
            { name: 'معرض السيارات', isLive: false },
            { name: 'مركز التجميل', isLive: true }
        ];

        // متغيرات التنقل
        let currentPosition = 0;
        const cardWidth = 180;
        const cardGap = 15;
        const visibleCards = 7;
        const moveDistance = cardWidth + cardGap;

        // ملء الخدمات
        function populateServices() {
            const wrapper = document.getElementById('servicesWrapper');
            wrapper.innerHTML = services.map(service =>
                '<div class="service-card" onclick="openServicePage(\'' + service.name + '\')">' +
                    '<div class="service-image">' +
                        '<img src="images/' + service.image + '" alt="' + service.name + '" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">' +
                        '<div class="service-image-placeholder" style="display: none;">' +
                            '<i class="' + service.icon + '"></i>' +
                        '</div>' +
                    '</div>' +
                    '<div class="service-name">' + service.name + '</div>' +
                '</div>'
            ).join('');

            updateNavigationButtons();
        }

        // ملء القصص
        function populateStories() {
            const container = document.getElementById('storiesContainer');
            container.innerHTML = stories.map(story =>
                '<div class="story-item" onclick="openStory(\'' + story.name + '\')">' +
                    '<div class="story-avatar ' + (story.hasStory ? 'has-story' : 'no-story') + '">' +
                        '<div class="story-inner">' +
                            (story.isAdd ? '<i class="fas fa-plus" style="color: #227FCC;"></i>' : '<i class="fas fa-user"></i>') +
                        '</div>' +
                    '</div>' +
                    '<div class="story-name">' + story.name + '</div>' +
                '</div>'
            ).join('');
        }

        // ملء البث المباشر
        function populateLiveStreams() {
            const container = document.getElementById('liveContainer');
            container.innerHTML = liveStreams.map(stream =>
                '<div class="live-item" onclick="openLiveStream(\'' + stream.name + '\')">' +
                    '<div class="live-avatar ' + (stream.isLive ? 'is-live' : 'no-live') + '">' +
                        '<i class="fas fa-user"></i>' +
                        (stream.isLive ? '<div class="live-indicator">LIVE</div>' : '') +
                    '</div>' +
                    '<div class="story-name">' + stream.name + '</div>' +
                '</div>'
            ).join('');
        }

        // تحديث أزرار التنقل
        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const maxPosition = (services.length - visibleCards) * moveDistance;

            prevBtn.disabled = currentPosition >= maxPosition;
            nextBtn.disabled = currentPosition <= 0;
        }

        // تحريك الخدمات
        function moveServices(direction) {
            const wrapper = document.getElementById('servicesWrapper');
            const maxPosition = (services.length - visibleCards) * moveDistance;

            if (direction === 'right' && currentPosition < maxPosition) {
                currentPosition += moveDistance;
            } else if (direction === 'left' && currentPosition > 0) {
                currentPosition -= moveDistance;
            }

            currentPosition = Math.max(0, Math.min(currentPosition, maxPosition));
            wrapper.style.transform = 'translateX(-' + currentPosition + 'px)';
            updateNavigationButtons();
        }

        // فتح صفحة الخدمة
        function openServicePage(serviceName) {
            // إنشاء صفحة المنشورات للخدمة
            const servicePageHTML = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${serviceName} - Get Me</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Cairo', sans-serif; background: #f0f2f5; direction: rtl; }

        .service-header {
            background: white;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #227FCC;
            padding: 8px;
            border-radius: 50%;
            transition: background 0.3s;
        }

        .back-btn:hover {
            background: #f5f5f5;
        }

        .service-title {
            flex: 1;
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .logo-small {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .stories-section, .live-section {
            background: white;
            padding: 15px 0;
            margin-bottom: 10px;
        }

        .stories-container, .live-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 0 20px;
            scroll-behavior: smooth;
        }

        .stories-container::-webkit-scrollbar, .live-container::-webkit-scrollbar {
            display: none;
        }

        .story-item, .live-item {
            flex-shrink: 0;
            text-align: center;
            cursor: pointer;
        }

        .story-avatar, .live-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .story-avatar.has-story {
            border: 3px solid #16CCC8;
        }

        .story-avatar.no-story {
            border: 3px solid #ccc;
        }

        .live-avatar.is-live {
            border: 3px solid;
            border-image: linear-gradient(45deg, #ff0000, #ff8c00, #ffd700, #9932cc) 1;
            animation: live-pulse 2s ease-in-out infinite;
        }

        .live-avatar.no-live {
            border: 3px solid #ccc;
        }

        @keyframes live-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .live-indicator {
            position: absolute;
            bottom: -2px;
            right: -2px;
            background: #ff0000;
            color: white;
            font-size: 8px;
            padding: 2px 4px;
            border-radius: 8px;
            font-weight: bold;
        }

        .story-name {
            font-size: 12px;
            color: #333;
            max-width: 70px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .create-post {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 0 20px 15px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .create-post-top {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .create-post-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .create-post-input {
            flex: 1;
            background: #f0f2f5;
            border: none;
            border-radius: 25px;
            padding: 12px 16px;
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
            cursor: pointer;
        }

        .create-post-actions {
            display: flex;
            justify-content: space-around;
            border-top: 1px solid #dadde1;
            padding-top: 10px;
        }

        .post-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            color: #65676b;
            font-weight: 600;
            transition: background 0.3s;
        }

        .post-action:hover {
            background: #f0f2f5;
        }

        .posts-container {
            max-width: 680px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .post {
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .post-header {
            padding: 15px 15px 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .post-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .post-info {
            flex: 1;
        }

        .post-author {
            font-weight: 600;
            color: #1c1e21;
            font-size: 15px;
        }

        .post-time {
            color: #65676b;
            font-size: 13px;
        }

        .post-content {
            padding: 15px;
            color: #1c1e21;
            line-height: 1.5;
        }

        .post-image {
            width: 100%;
            max-height: 400px;
            object-fit: cover;
        }

        .post-stats {
            padding: 0 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #65676b;
            font-size: 15px;
            border-bottom: 1px solid #dadde1;
            padding-bottom: 10px;
        }

        .post-actions {
            display: flex;
            justify-content: space-around;
            padding: 8px 0;
        }

        .post-action-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            color: #65676b;
            font-weight: 600;
            transition: background 0.3s;
            border: none;
            background: none;
            font-family: 'Cairo', sans-serif;
        }

        .post-action-btn:hover {
            background: #f0f2f5;
        }

        .post-action-btn.liked {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <div class="service-header">
        <button class="back-btn" onclick="history.back()">
            <i class="fas fa-arrow-right"></i>
        </button>
        <div class="service-title">${serviceName}</div>
        <div class="logo-small">
            <i class="fas fa-shopping-bag"></i>
        </div>
    </div>

    <!-- شريط القصص -->
    <div class="stories-section">
        <div class="stories-container">
            <div class="story-item">
                <div class="story-avatar has-story">
                    <i class="fas fa-user" style="color: #666;"></i>
                </div>
                <div class="story-name">أحمد محمد</div>
            </div>
            <div class="story-item">
                <div class="story-avatar no-story">
                    <i class="fas fa-user" style="color: #666;"></i>
                </div>
                <div class="story-name">فاطمة علي</div>
            </div>
            <div class="story-item">
                <div class="story-avatar has-story">
                    <i class="fas fa-user" style="color: #666;"></i>
                </div>
                <div class="story-name">محمد حسن</div>
            </div>
        </div>
    </div>

    <!-- شريط البث المباشر -->
    <div class="live-section">
        <div class="live-container">
            <div class="live-item">
                <div class="live-avatar is-live">
                    <i class="fas fa-user" style="color: #666;"></i>
                    <div class="live-indicator">LIVE</div>
                </div>
                <div class="story-name">متجر ${serviceName}</div>
            </div>
            <div class="live-item">
                <div class="live-avatar no-live">
                    <i class="fas fa-user" style="color: #666;"></i>
                </div>
                <div class="story-name">شركة النور</div>
            </div>
        </div>
    </div>

    <div class="posts-container">
        <!-- منطقة إنشاء المنشور -->
        <div class="create-post">
            <div class="create-post-top">
                <div class="create-post-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <input type="text" class="create-post-input" placeholder="ماذا تريد أن تشارك في ${serviceName}؟">
            </div>
            <div class="create-post-actions">
                <div class="post-action">
                    <i class="fas fa-images" style="color: #45bd62;"></i>
                    <span>صورة/فيديو</span>
                </div>
                <div class="post-action">
                    <i class="fas fa-map-marker-alt" style="color: #f02849;"></i>
                    <span>موقع</span>
                </div>
                <div class="post-action">
                    <i class="fas fa-tag" style="color: #f7b928;"></i>
                    <span>عرض خاص</span>
                </div>
            </div>
        </div>

        <!-- المنشورات -->
        <div class="post">
            <div class="post-header">
                <div class="post-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="post-info">
                    <div class="post-author">شركة ${serviceName} المميزة</div>
                    <div class="post-time">منذ ساعتين • <i class="fas fa-globe-americas"></i></div>
                </div>
            </div>
            <div class="post-content">
                عرض خاص في ${serviceName}!

                ✨ خصم 20% على جميع المنتجات
                🎯 جودة عالية وأسعار منافسة
                🚚 توصيل مجاني داخل المدينة
                📞 للطلب والاستفسار تواصل معنا

                #${serviceName} #عروض_خاصة #جودة_عالية
            </div>
            <div class="post-stats">
                <div>
                    <i class="fas fa-heart" style="color: #16CCC8;"></i>
                    45 إعجاب
                </div>
                <div>12 طلب سعر • 5 مشاركات</div>
            </div>
            <div class="post-actions">
                <button class="post-action-btn" onclick="toggleLike(this)">
                    <i class="fas fa-heart"></i>
                    <span>إعجاب</span>
                </button>
                <button class="post-action-btn" onclick="requestPrice()">
                    <i class="fas fa-comment-dollar"></i>
                    <span>طلب السعر</span>
                </button>
                <button class="post-action-btn" onclick="sharePost()">
                    <i class="fas fa-share"></i>
                    <span>مشاركة</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        function toggleLike(button) {
            button.classList.toggle('liked');
        }

        function requestPrice() {
            alert('تم إرسال طلب السعر - سيتم الرد عليك في رسالة خاصة');
        }

        function sharePost() {
            alert('تم مشاركة المنشور');
        }
    </script>
</body>
</html>`;

            // فتح الصفحة في نافذة جديدة
            const newWindow = window.open('', '_blank');
            newWindow.document.write(servicePageHTML);
            newWindow.document.close();
        }

        function openStory(userName) {
            alert('فتح قصة ' + userName);
        }

        function openLiveStream(userName) {
            alert('فتح بث ' + userName + ' المباشر');
        }

        // إضافة أحداث الأزرار
        document.addEventListener('DOMContentLoaded', function() {
            populateServices();
            populateStories();
            populateLiveStreams();

            document.getElementById('prevBtn').addEventListener('click', function() {
                moveServices('right');
            });

            document.getElementById('nextBtn').addEventListener('click', function() {
                moveServices('left');
            });
        });

        // تبديل القائمة الرئيسية
        function toggleMainMenu() {
            const dropdown = document.getElementById('mainMenuDropdown');
            dropdown.classList.toggle('show');
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const menuIcon = document.querySelector('.menu-icon');
            const dropdown = document.getElementById('mainMenuDropdown');

            if (!menuIcon.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });

        // متغيرات الإعدادات
        let isDarkMode = false;

        // وظائف القائمة
        function toggleDarkMode() {
            if (!isDarkMode) {
                isDarkMode = true;
                localStorage.setItem('darkMode', 'true');
                showToast('🌙 تم تفعيل الوضع الليلي');
                applyDarkModeStyles();
            }
        }

        function toggleLightMode() {
            if (isDarkMode) {
                isDarkMode = false;
                localStorage.setItem('darkMode', 'false');
                showToast('☀️ تم تفعيل الوضع النهاري');
                removeDarkModeStyles();
            }
        }

        function openNotificationSettings() {
            alert('🔔 إعدادات الإشعارات\n\nيمكنك تخصيص أنواع الإشعارات التي تريد استلامها.');
        }

        function changeLanguage() {
            alert('🌐 تغيير اللغة\n\nاللغات المتاحة: العربية، الإنجليزية، الكردية');
        }

        function applyDarkModeStyles() {
            // إضافة أنماط الوضع الليلي
            const darkModeCSS = `
                body {
                    background: #1a1a1a !important;
                    color: #e0e0e0 !important;
                }
                .header {
                    background: linear-gradient(90deg, #2d3748, #4a5568) !important;
                }
                .main-content {
                    background: #1a1a1a !important;
                    color: #e0e0e0 !important;
                }
                .category-card {
                    background: #2d3748 !important;
                    color: #e0e0e0 !important;
                    border: 1px solid #4a5568 !important;
                }
                .category-card:hover {
                    background: #4a5568 !important;
                    transform: translateY(-5px) !important;
                }
                .search-section input {
                    background: #2d3748 !important;
                    color: #e0e0e0 !important;
                    border: 1px solid #4a5568 !important;
                }
                .search-section input::placeholder {
                    color: #a0aec0 !important;
                }
                .nav-section {
                    background: #2d3748 !important;
                    border-bottom: 1px solid #4a5568 !important;
                }
                .nav-item {
                    color: #a0aec0 !important;
                }
                .nav-item:hover, .nav-item.active {
                    color: #16CCC8 !important;
                }
                .main-menu-dropdown {
                    background: #2d3748 !important;
                    border: 1px solid #4a5568 !important;
                    color: #e0e0e0 !important;
                }
                .menu-item {
                    color: #e0e0e0 !important;
                }
                .menu-item:hover {
                    background: linear-gradient(135deg, #4a5568, #2d3748) !important;
                }
            `;

            let styleElement = document.getElementById('darkModeStyles');
            if (!styleElement) {
                styleElement = document.createElement('style');
                styleElement.id = 'darkModeStyles';
                document.head.appendChild(styleElement);
            }
            styleElement.textContent = darkModeCSS;
        }

        function removeDarkModeStyles() {
            const styleElement = document.getElementById('darkModeStyles');
            if (styleElement) {
                styleElement.remove();
            }
        }

        function showToast(message) {
            // إنشاء عنصر التوست
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-family: 'Cairo', sans-serif;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideIn 0.3s ease-out;
            `;
            toast.textContent = message;

            // إضافة التوست للصفحة
            document.body.appendChild(toast);

            // إزالة التوست بعد 3 ثوان
            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // تحميل الإعدادات المحفوظة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('darkMode') === 'true') {
                isDarkMode = true;
                applyDarkModeStyles();
            }
        });

        function openPrivacySettings() {
            alert('🔒 الخصوصية والأمان\n\nإعدادات الحماية وخصوصية البيانات.');
        }

        function viewStatistics() {
            alert('📈 إحصائيات الملف الشخصي\n\nمشاهدات: 1,234\nتفاعلات: 567\nمتابعين جدد: 89');
        }

        function viewEarnings() {
            alert('💰 تقرير الأرباح\n\nالأرباح هذا الشهر: 150,000 د.ع\nمن الإعلانات: 100,000 د.ع\nمن الخدمات: 50,000 د.ع');
        }

        function viewTransactions() {
            alert('📋 سجل المعاملات\n\nآخر المعاملات:\n- دفع إعلان: -25,000 د.ع\n- ربح من خدمة: +75,000 د.ع');
        }

        function upgradeToPremium() {
            alert('⭐ الحساب المميز\n\nاحصل على ميزات إضافية:\n- إعلانات بلا حدود\n- إحصائيات متقدمة\n- دعم أولوية');
        }

        function customizeProfile() {
            alert('🎨 تخصيص الملف الشخصي\n\nغير ألوان وثيمات ملفك الشخصي.');
        }

        function syncData() {
            alert('🔄 مزامنة البيانات\n\nتم مزامنة بياناتك مع السحابة بنجاح!');
        }

        function openHelpCenter() {
            alert('❓ مركز المساعدة\n\nدليل الاستخدام والأسئلة الشائعة.');
        }

        function contactSupport() {
            alert('📞 اتصل بنا\n\nالدعم الفني متاح 24/7\nواتساب: +964 123 456 789');
        }

        function viewTerms() {
            alert('📋 الشروط والأحكام\n\nسياسات الاستخدام وحماية البيانات.');
        }

        function logout() {
            if (confirm('🚪 هل تريد تسجيل الخروج من التطبيق؟')) {
                alert('تم تسجيل الخروج بنجاح!');
                // يمكن إضافة كود التوجيه لصفحة تسجيل الدخول هنا
                // window.location.href = 'stage3_login.html';
            }
        }
    </script>
</body>
</html>
