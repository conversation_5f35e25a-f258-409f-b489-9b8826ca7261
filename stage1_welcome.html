<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - المرحلة الأولى</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            overflow: hidden;
        }

        /* Welcome Screen */
        .welcome-screen {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6), rgba(0,0,0,0.8)),
                        url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80');
            background-size: cover;
            background-position: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            position: relative;
            animation: fadeIn 2s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .welcome-content {
            animation: slideUp 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            max-width: 600px;
            padding: 0 20px;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(100px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .app-logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .app-logo img {
            width: 110%;
            height: 110%;
            object-fit: cover;
            border-radius: 30px;
        }

        .app-logo i {
            color: white;
            font-size: 85px;
        }

        .welcome-title {
            font-size: 24px;
            color: rgba(255,255,255,0.7);
            margin-bottom: 8px;
            font-weight: 300;
        }

        .app-name {
            font-size: 48px;
            font-weight: bold;
            letter-spacing: 2px;
            margin-bottom: 16px;
        }

        .app-description {
            font-size: 18px;
            color: rgba(255,255,255,0.7);
            line-height: 1.5;
            margin-bottom: 60px;
        }

        .start-button {
            background: linear-gradient(135deg, #667eea, #764ba2, #667eea);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 30px;
            padding: 16px 32px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            display: inline-flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
            font-family: 'Cairo', sans-serif;
            margin: 0 auto;
        }

        .start-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
        }



        /* Responsive Design */
        @media (max-width: 768px) {
            .app-name {
                font-size: 36px;
            }
            
            .welcome-title {
                font-size: 20px;
            }
            
            .app-description {
                font-size: 16px;
                margin-bottom: 40px;
            }
            
            .start-button {
                padding: 16px 32px;
                font-size: 18px;
            }
            
            .app-logo {
                width: 100px;
                height: 100px;
                margin-bottom: 30px;
            }
        }

        @media (max-width: 480px) {
            .welcome-content {
                padding: 0 15px;
            }
            
            .app-name {
                font-size: 32px;
            }
            
            .app-description {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <!-- Welcome Screen -->
    <div class="welcome-screen">
        <div class="welcome-content">
            <div class="app-logo">
                <img src="assets/logo.png" alt="Get Me Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <i class="fas fa-shopping-bag" style="display: none;"></i>
            </div>
            
            <h2 class="welcome-title">مرحباً بك في</h2>
            <h1 class="app-name">Get Me</h1>
            <p class="app-description">
                منصة التجارة الإلكترونية الشاملة<br>
                حيث تجد كل ما تحتاجه
            </p>
            
            <button class="start-button" onclick="startApp()">
                ابدأ الآن
                <i class="fas fa-arrow-left"></i>
            </button>
        </div>
    </div>

    <script>
        function startApp() {
            // تأثير انتقال جميل
            const button = document.querySelector('.start-button');
            button.style.transform = 'scale(0.95)';
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';

            setTimeout(() => {
                // الانتقال إلى شاشة الوصف
                window.location.href = 'stage2_description.html';
            }, 1500);
        }


    </script>
</body>
</html>
