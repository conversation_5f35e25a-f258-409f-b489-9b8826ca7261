import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';
import '../models/user.dart';
import '../models/post.dart';
import '../widgets/post_card.dart';
import '../widgets/create_post_widget.dart';
import '../widgets/stories_widget.dart';

class FeedScreen extends StatefulWidget {
  final User currentUser;

  const FeedScreen({
    super.key,
    required this.currentUser,
  });

  @override
  State<FeedScreen> createState() => _FeedScreenState();
}

class _FeedScreenState extends State<FeedScreen> {
  final List<Post> _posts = Post.getDemoPosts();
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF2196F3), Color(0xFF03DAC6)],
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.shopping_bag,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'Get Me',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2196F3),
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(
              Icons.search,
              color: Colors.black87,
              size: 28,
            ),
            onPressed: () {
              // TODO: Implement search
            },
          ),
          IconButton(
            icon: Stack(
              children: [
                const Icon(
                  Icons.notifications_outlined,
                  color: Colors.black87,
                  size: 28,
                ),
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshFeed,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // Stories Section
            SliverToBoxAdapter(
              child: Container(
                color: Colors.white,
                child: StoriesWidget(currentUser: widget.currentUser),
              ),
            ),

            // Create Post Section
            SliverToBoxAdapter(
              child: Container(
                color: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: CreatePostWidget(currentUser: widget.currentUser),
              ),
            ),

            const SliverToBoxAdapter(
              child: SizedBox(height: 8),
            ),

            // Posts List
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index >= _posts.length) {
                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(20),
                        child: CircularProgressIndicator(),
                      ),
                    );
                  }

                  return Column(
                    children: [
                      PostCard(
                        post: _posts[index],
                        currentUser: widget.currentUser,
                        onLike: () => _likePost(_posts[index]),
                        onComment: () => _commentPost(_posts[index]),
                        onShare: () => _sharePost(_posts[index]),
                      ),
                      const SizedBox(height: 8),
                    ],
                  );
                },
                childCount: _posts.length + 1, // +1 for loading indicator
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showCreatePostDialog();
        },
        backgroundColor: const Color(0xFF2196F3),
        child: const Icon(
          Icons.add,
          color: Colors.white,
          size: 28,
        ),
      ),
    );
  }

  Future<void> _refreshFeed() async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      // In a real app, you would fetch new posts from the server
      _posts.shuffle();
    });
  }

  void _likePost(Post post) {
    setState(() {
      // Toggle like status
      // In a real app, you would send this to the server
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          post.isLiked ? 'تم إلغاء الإعجاب' : 'تم الإعجاب بالمنشور',
          style: const TextStyle(),
        ),
        duration: const Duration(seconds: 1),
        backgroundColor: const Color(0xFF2196F3),
      ),
    );
  }

  void _commentPost(Post post) {
    // TODO: Navigate to comments screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'فتح التعليقات...',
          style: const TextStyle(),
        ),
        duration: const Duration(seconds: 1),
        backgroundColor: const Color(0xFF2196F3),
      ),
    );
  }

  void _sharePost(Post post) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم مشاركة المنشور',
          style: const TextStyle(),
        ),
        duration: const Duration(seconds: 1),
        backgroundColor: const Color(0xFF4CAF50),
      ),
    );
  }

  void _showCreatePostDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'إنشاء منشور جديد',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const Divider(height: 1),
            // TODO: Add create post form
            const Expanded(
              child: Center(
                child: Text('نموذج إنشاء المنشور سيتم إضافته قريباً'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
