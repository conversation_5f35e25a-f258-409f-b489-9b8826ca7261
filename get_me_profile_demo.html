<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - الملف الشخصي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f5f5f5;
            direction: rtl;
            overflow-x: hidden;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .profile-header {
            background: white;
            padding: 40px 20px 20px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .profile-picture {
            position: relative;
            display: inline-block;
            margin-bottom: 20px;
        }

        .profile-avatar {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            border: 4px solid #2196F3;
            object-fit: cover;
        }

        .verified-badge {
            position: absolute;
            bottom: 10px;
            right: 10px;
            width: 35px;
            height: 35px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .camera-btn {
            position: absolute;
            bottom: 10px;
            left: 10px;
            width: 35px;
            height: 35px;
            background: #2196F3;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
        }

        .profile-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .profile-type {
            background: rgba(33, 150, 243, 0.1);
            color: #2196F3;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }

        .stats-row {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #2196F3;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }

        .btn-primary {
            flex: 1;
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
        }

        .btn-secondary {
            background: #f5f5f5;
            color: #333;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
        }

        .tabs-container {
            background: white;
            border-bottom: 1px solid #eee;
        }

        .tabs {
            display: flex;
            justify-content: space-around;
        }

        .tab {
            flex: 1;
            padding: 16px;
            text-align: center;
            background: none;
            border: none;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            color: #666;
            cursor: pointer;
            border-bottom: 3px solid transparent;
        }

        .tab.active {
            color: #2196F3;
            border-bottom-color: #2196F3;
        }

        .tab-content {
            padding: 16px;
            min-height: 400px;
        }

        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        .album-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .album-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .album-info {
            flex: 1;
        }

        .album-name {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .album-count {
            color: #666;
            font-size: 14px;
        }

        .info-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .info-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 12px;
        }

        .info-title {
            font-weight: 600;
            font-size: 16px;
        }

        .edit-btn {
            background: none;
            border: none;
            color: #2196F3;
            cursor: pointer;
            padding: 4px;
        }

        .info-content {
            color: #666;
            line-height: 1.5;
        }

        .add-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-icon {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 16px;
        }

        @media (max-width: 480px) {
            .app-container {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-picture">
                <img src="https://via.placeholder.com/160" alt="صورة الملف الشخصي" class="profile-avatar">
                <div class="verified-badge">
                    <i class="fas fa-check" style="color: #2196F3;"></i>
                </div>
                <div class="camera-btn" onclick="changeProfilePicture()">
                    <i class="fas fa-camera"></i>
                </div>
            </div>
            
            <div class="profile-name">أحمد محمد</div>
            <div class="profile-type">بائع</div>
            
            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-number">89</div>
                    <div class="stat-label">المنشورات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">1250</div>
                    <div class="stat-label">المتابعون</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">340</div>
                    <div class="stat-label">المتابَعون</div>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn-primary" onclick="editProfile()">
                    <i class="fas fa-edit"></i> تعديل الملف الشخصي
                </button>
                <button class="btn-secondary" onclick="shareProfile()">
                    <i class="fas fa-share"></i>
                </button>
            </div>
        </div>

        <!-- Tabs -->
        <div class="tabs-container">
            <div class="tabs">
                <button class="tab active" onclick="showTab('posts')">المنشورات</button>
                <button class="tab" onclick="showTab('photos')">الصور</button>
                <button class="tab" onclick="showTab('videos')">الفيديوهات</button>
                <button class="tab" onclick="showTab('info')">المعلومات</button>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Posts Tab -->
            <div id="posts" class="tab-panel active">
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div>لا توجد منشورات بعد</div>
                    <div style="font-size: 14px; margin-top: 8px;">ابدأ بمشاركة أول منشور لك</div>
                </div>
            </div>

            <!-- Photos Tab -->
            <div id="photos" class="tab-panel">
                <button class="add-btn" onclick="addPhotoAlbum()">
                    <i class="fas fa-plus"></i>
                    إضافة ألبوم صور
                </button>
                
                <div class="album-item">
                    <div class="album-icon" style="background: rgba(33, 150, 243, 0.1); color: #2196F3;">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="album-info">
                        <div class="album-name">الألبوم الرئيسي</div>
                        <div class="album-count">5 صور</div>
                    </div>
                    <button class="edit-btn">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
                
                <div class="album-item">
                    <div class="album-icon" style="background: rgba(33, 150, 243, 0.1); color: #2196F3;">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="album-info">
                        <div class="album-name">منتجاتنا</div>
                        <div class="album-count">10 صور</div>
                    </div>
                    <button class="edit-btn">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <!-- Videos Tab -->
            <div id="videos" class="tab-panel">
                <button class="add-btn" onclick="addVideoAlbum()">
                    <i class="fas fa-plus"></i>
                    إضافة ألبوم فيديوهات
                </button>
                
                <div class="album-item">
                    <div class="album-icon" style="background: rgba(244, 67, 54, 0.1); color: #f44336;">
                        <i class="fas fa-video"></i>
                    </div>
                    <div class="album-info">
                        <div class="album-name">فيديوهات تعريفية</div>
                        <div class="album-count">3 فيديوهات</div>
                    </div>
                    <button class="edit-btn">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <!-- Info Tab -->
            <div id="info" class="tab-panel">
                <div class="info-card">
                    <div class="info-header">
                        <div class="info-title">معلومات الشركة</div>
                        <button class="edit-btn" onclick="editCompanyInfo()">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                    <div class="info-content">
                        شركة رائدة في مجال التكنولوجيا والحلول الرقمية. نقدم خدمات متميزة لعملائنا منذ أكثر من 10 سنوات.
                    </div>
                </div>
                
                <div class="info-card">
                    <div class="info-header">
                        <div class="info-title">الشهادات والمؤهلات</div>
                        <button class="edit-btn" onclick="editCertificates()">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                    <div class="info-content">
                        • شهادة ISO 9001<br>
                        • شهادة الجودة الدولية<br>
                        • عضوية غرفة التجارة
                    </div>
                </div>
                
                <div class="info-card">
                    <div class="info-header">
                        <div class="info-title">نبذة شخصية</div>
                    </div>
                    <div class="info-content">
                        بائع في مجال الإلكترونيات والهواتف الذكية. خبرة أكثر من 5 سنوات في السوق.
                    </div>
                </div>
                
                <div class="info-card">
                    <div class="info-header">
                        <div class="info-title">البريد الإلكتروني</div>
                    </div>
                    <div class="info-content">
                        <EMAIL>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab panels
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab panel
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        function changeProfilePicture() {
            alert('سيتم إضافة ميزة تغيير الصورة قريباً');
        }

        function editProfile() {
            alert('سيتم إضافة ميزة تعديل الملف الشخصي قريباً');
        }

        function shareProfile() {
            alert('تم نسخ رابط الملف الشخصي');
        }

        function addPhotoAlbum() {
            const name = prompt('اسم الألبوم الجديد:');
            if (name) {
                alert(`تم إضافة ألبوم: ${name}`);
            }
        }

        function addVideoAlbum() {
            const name = prompt('اسم ألبوم الفيديوهات الجديد:');
            if (name) {
                alert(`تم إضافة ألبوم فيديوهات: ${name}`);
            }
        }

        function editCompanyInfo() {
            const info = prompt('تعديل معلومات الشركة:', 'شركة رائدة في مجال التكنولوجيا والحلول الرقمية');
            if (info) {
                alert('تم حفظ معلومات الشركة');
            }
        }

        function editCertificates() {
            const certs = prompt('تعديل الشهادات والمؤهلات:', 'شهادة ISO 9001\nشهادة الجودة الدولية\nعضوية غرفة التجارة');
            if (certs) {
                alert('تم حفظ الشهادات والمؤهلات');
            }
        }
    </script>
</body>
</html>
