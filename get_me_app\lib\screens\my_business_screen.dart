import 'package:flutter/material.dart';
import 'package:animations/animations.dart';
import '../models/user.dart';
import '../models/business_analytics.dart';
import '../models/booking_request.dart';
import '../widgets/analytics_chart_widget.dart';
import '../widgets/market_news_widget.dart';
import '../widgets/business_messages_widget.dart';
import '../widgets/personal_notes_widget.dart';
import '../widgets/booking_requests_widget.dart';
import '../widgets/financial_reports_widget.dart';
import '../widgets/create_content_widget.dart';

class MyBusinessScreen extends StatefulWidget {
  final User currentUser;

  const MyBusinessScreen({
    super.key,
    required this.currentUser,
  });

  @override
  State<MyBusinessScreen> createState() => _MyBusinessScreenState();
}

class _MyBusinessScreenState extends State<MyBusinessScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: _buildAppBar(),
      body: _buildExpandedView(), // دائماً عرض مفصل عند فتح الشاشة
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [Color(0xFF16CCC8), Color(0xFF227FCC)], // نفس ألوان الصفحة الرئيسية
          ),
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 8,
      shadowColor: Colors.black.withOpacity(0.15),
      title: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.business_center,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          const Text(
            'أعمالي',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications, color: Colors.white),
          onPressed: () {
            // عرض الإشعارات
          },
        ),
        IconButton(
          icon: const Icon(Icons.settings, color: Colors.white),
          onPressed: () {
            // إعدادات الأعمال
          },
        ),
      ],
    );
  }



  Widget _buildExpandedView() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // تحليل السوق
            _buildSectionTitle('تحليل السوق', Icons.analytics),
            const SizedBox(height: 12),
            const AnalyticsChartWidget(),
            const SizedBox(height: 24),

            // أخبار السوق
            _buildSectionTitle('أخبار السوق', Icons.newspaper),
            const SizedBox(height: 12),
            const MarketNewsWidget(),
            const SizedBox(height: 24),

            // الرسائل والإشعارات
            _buildSectionTitle('الرسائل والإشعارات', Icons.message),
            const SizedBox(height: 12),
            const BusinessMessagesWidget(),
            const SizedBox(height: 24),

            // المذكرة الشخصية
            _buildSectionTitle('المذكرة الشخصية', Icons.note),
            const SizedBox(height: 12),
            const PersonalNotesWidget(),
            const SizedBox(height: 24),

            // قوائم الحجز والطلبات
            _buildSectionTitle('طلبات الحجز', Icons.list_alt),
            const SizedBox(height: 12),
            const BookingRequestsWidget(),
            const SizedBox(height: 24),

            // التقارير المالية
            _buildSectionTitle('التقارير المالية', Icons.account_balance),
            const SizedBox(height: 12),
            const FinancialReportsWidget(),
            const SizedBox(height: 24),

            // إنشاء المحتوى
            _buildSectionTitle('إنشاء المحتوى', Icons.add_circle),
            const SizedBox(height: 12),
            const CreateContentWidget(),
            const SizedBox(height: 100), // مساحة إضافية للزر العائم
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF16CCC8), Color(0xFF227FCC)], // نفس ألوان الشريط العلوي
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 20),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }


}
