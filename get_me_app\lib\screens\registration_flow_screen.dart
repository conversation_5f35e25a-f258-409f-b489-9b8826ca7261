import 'dart:ui';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:animations/animations.dart';
import 'package:getwidget/getwidget.dart';
import '../models/subscription_plan.dart';
import '../models/user.dart';
import 'basic_info_screen.dart';
import 'business_info_screen.dart';
import 'identity_verification_screen.dart';
import 'terms_screen.dart';
import 'account_creation_screen.dart';
import 'login_screen.dart';
import 'main_screen.dart';

class RegistrationFlowScreen extends StatefulWidget {
  final UserRegistrationData registrationData;
  final bool isGuestMode;

  const RegistrationFlowScreen({
    super.key,
    required this.registrationData,
    this.isGuestMode = false,
  });

  @override
  State<RegistrationFlowScreen> createState() => _RegistrationFlowScreenState();
}

class _RegistrationFlowScreenState extends State<RegistrationFlowScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _backgroundController;
  int _currentStep = 0;

  final List<String> _stepTitles = [
    'البيانات الأساسية',
    'بيانات العمل',
    'التحقق من الهوية',
    'الشروط والأحكام',
    'إنشاء الحساب',
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 25),
      vsync: this,
    )..repeat();

    // Skip to login if guest mode
    if (widget.isGuestMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _navigateToMainApp();
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isGuestMode) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      body: Stack(
        children: [
          _buildAnimatedBackground(),
          SafeArea(
            child: Column(
              children: [
                _buildProgressHeader(),
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      BasicInfoScreen(
                        registrationData: widget.registrationData,
                        onNext: _nextStep,
                      ),
                      if (_needsBusinessInfo())
                        BusinessInfoScreen(
                          registrationData: widget.registrationData,
                          onNext: _nextStep,
                          onBack: _previousStep,
                        ),
                      IdentityVerificationScreen(
                        registrationData: widget.registrationData,
                        onNext: _nextStep,
                        onBack: _previousStep,
                      ),
                      TermsScreen(
                        registrationData: widget.registrationData,
                        onNext: _nextStep,
                        onBack: _previousStep,
                      ),
                      AccountCreationScreen(
                        registrationData: widget.registrationData,
                        onComplete: _completeRegistration,
                        onBack: _previousStep,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            widget.registrationData.selectedPlan?.primaryColor
                    .withOpacity(0.8) ??
                const Color(0xFF667eea),
            widget.registrationData.selectedPlan?.secondaryColor
                    .withOpacity(0.6) ??
                const Color(0xFF764ba2),
            const Color(0xFF667eea).withOpacity(0.4),
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
      ),
      child: AnimatedBuilder(
        animation: _backgroundController,
        builder: (context, child) {
          return CustomPaint(
            painter: FlowBackgroundPainter(_backgroundController.value),
            size: Size.infinite,
          );
        },
      ),
    );
  }

  Widget _buildProgressHeader() {
    return Container(
      margin: const EdgeInsets.all(24),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.25),
            Colors.white.withOpacity(0.1),
          ],
        ),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(18),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          widget.registrationData.selectedPlan?.primaryColor ??
                              const Color(0xFF667eea),
                          widget.registrationData.selectedPlan
                                  ?.secondaryColor ??
                              const Color(0xFF764ba2),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      widget.registrationData.selectedPlan?.icon ??
                          Icons.person,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.registrationData.selectedPlan?.title ??
                              'تسجيل جديد',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'الخطوة ${_currentStep + 1} من ${_getMaxSteps()}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                _stepTitles[_currentStep],
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 12),
              _buildProgressBar(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return Container(
      height: 6,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(3),
        color: Colors.white.withOpacity(0.2),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: (_currentStep + 1) / _getMaxSteps(),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            gradient: LinearGradient(
              colors: [
                Colors.white,
                Colors.white.withOpacity(0.8),
              ],
            ),
          ),
        ),
      ),
    );
  }

  bool _needsBusinessInfo() {
    final planType = widget.registrationData.selectedPlan?.type;
    return planType == PlanType.seller || planType == PlanType.premium;
  }

  int _getMaxSteps() {
    return _needsBusinessInfo() ? 5 : 4;
  }

  void _nextStep() {
    if (_currentStep < _getMaxSteps() - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeRegistration() {
    // Show success animation and navigate to login
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildSuccessDialog(),
    );
  }

  Widget _buildSuccessDialog() {
    return AlertDialog(
      backgroundColor: Colors.transparent,
      content: Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          gradient: LinearGradient(
            colors: [
              Colors.white.withOpacity(0.9),
              Colors.white.withOpacity(0.8),
            ],
          ),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF4CAF50), Color(0xFF81C784)],
                ),
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.check_rounded,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'تم إنشاء حسابك بنجاح!',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1C1E21),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'يمكنك الآن تسجيل الدخول والاستمتاع بجميع المميزات',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _navigateToLogin();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4CAF50),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'تسجيل الدخول',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToLogin() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const LoginScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 800),
      ),
    );
  }

  void _navigateToMainApp() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            MainScreen(currentUser: User.demo()),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 1000),
      ),
    );
  }
}

class FlowBackgroundPainter extends CustomPainter {
  final double animationValue;

  FlowBackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.08)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final glowPaint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke
      ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 4);

    // Create flowing wave patterns
    final path = Path();
    final glowPath = Path();

    for (int i = 0; i < 3; i++) {
      final waveHeight = 40.0 + i * 20;
      final frequency = 0.02 + i * 0.01;
      final phase = animationValue * 2 * 3.14159 + i * 1.5;

      path.reset();
      glowPath.reset();

      path.moveTo(0, size.height * 0.3 + i * size.height * 0.2);
      glowPath.moveTo(0, size.height * 0.3 + i * size.height * 0.2);

      for (double x = 0; x <= size.width; x += 2) {
        final y = size.height * 0.3 +
            i * size.height * 0.2 +
            waveHeight * sin(x * frequency + phase);
        path.lineTo(x, y);
        glowPath.lineTo(x, y);
      }

      if (i % 2 == 0) {
        canvas.drawPath(glowPath, glowPaint);
      }
      canvas.drawPath(path, paint);
    }

    // Draw floating geometric shapes
    final shapePaint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    final shapeStrokePaint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    for (int i = 0; i < 8; i++) {
      final x = (size.width * 0.1 +
              i * size.width * 0.12 +
              animationValue * 50 +
              i * 30) %
          (size.width + 100);
      final y = (size.height * 0.1 +
              i * size.height * 0.1 +
              sin(animationValue * 2 * 3.14159 + i) * 60) %
          size.height;

      final rect = Rect.fromCenter(
        center: Offset(x, y),
        width: 20 + i * 5,
        height: 20 + i * 5,
      );

      if (i % 3 == 0) {
        // Draw circles
        canvas.drawCircle(Offset(x, y), 10 + i * 2, shapePaint);
        canvas.drawCircle(Offset(x, y), 10 + i * 2, shapeStrokePaint);
      } else if (i % 3 == 1) {
        // Draw squares
        canvas.drawRect(rect, shapePaint);
        canvas.drawRect(rect, shapeStrokePaint);
      } else {
        // Draw triangles
        final path = Path();
        path.moveTo(x, y - 10 - i * 2);
        path.lineTo(x - 10 - i * 2, y + 10 + i * 2);
        path.lineTo(x + 10 + i * 2, y + 10 + i * 2);
        path.close();
        canvas.drawPath(path, shapePaint);
        canvas.drawPath(path, shapeStrokePaint);
      }
    }
  }

  @override
  bool shouldRepaint(FlowBackgroundPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }

  double sin(double value) {
    return math.sin(value);
  }
}
