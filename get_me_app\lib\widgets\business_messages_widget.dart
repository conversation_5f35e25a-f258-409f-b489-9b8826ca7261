import 'package:flutter/material.dart';

class BusinessMessagesWidget extends StatefulWidget {
  const BusinessMessagesWidget({super.key});

  @override
  State<BusinessMessagesWidget> createState() => _BusinessMessagesWidgetState();
}

class _BusinessMessagesWidgetState extends State<BusinessMessagesWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildTabBar(),
          const SizedBox(height: 16),
          _buildTabContent(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Row(
          children: [
            Icon(Icons.message, color: Color(0xFF667eea), size: 24),
            SizedBox(width: 8),
            Text(
              'الرسائل والإشعارات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Text(
                '5',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: () {
                // فتح صفحة الرسائل الكاملة
              },
              icon: const Icon(Icons.open_in_new, size: 20),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: const Color(0xFF667eea),
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey[600],
        labelStyle: const TextStyle(fontWeight: FontWeight.w600),
        tabs: const [
          Tab(text: 'الرسائل'),
          Tab(text: 'الإشعارات'),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return SizedBox(
      height: 300,
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildMessagesList(),
          _buildNotificationsList(),
        ],
      ),
    );
  }

  Widget _buildMessagesList() {
    final messages = _getDemoMessages();
    
    return ListView.builder(
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        return _buildMessageItem(message);
      },
    );
  }

  Widget _buildNotificationsList() {
    final notifications = _getDemoNotifications();
    
    return ListView.builder(
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return _buildNotificationItem(notification);
      },
    );
  }

  Widget _buildMessageItem(BusinessMessage message) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: message.isRead ? Colors.grey[50] : const Color(0xFF667eea).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: message.isRead ? Colors.grey[200]! : const Color(0xFF667eea).withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          // صورة المرسل
          CircleAvatar(
            radius: 20,
            backgroundImage: AssetImage(message.senderImage),
            onBackgroundImageError: (exception, stackTrace) {
              // في حالة عدم وجود الصورة
            },
            child: message.senderImage.isEmpty
                ? const Icon(Icons.person, size: 20)
                : null,
          ),
          const SizedBox(width: 12),
          
          // محتوى الرسالة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      message.senderName,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      _formatTime(message.timestamp),
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  message.content,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (message.postTitle.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF667eea).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'بخصوص: ${message.postTitle}',
                      style: const TextStyle(
                        fontSize: 10,
                        color: Color(0xFF667eea),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // حالة القراءة
          if (!message.isRead)
            Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Color(0xFF667eea),
                shape: BoxShape.circle,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNotificationItem(BusinessNotification notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: notification.isRead ? Colors.grey[50] : notification.type.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: notification.isRead ? Colors.grey[200]! : notification.type.color.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          // أيقونة النوع
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: notification.type.color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              notification.type.icon,
              color: notification.type.color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          
          // محتوى الإشعار
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      notification.title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      _formatTime(notification.timestamp),
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  notification.content,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          
          // حالة القراءة
          if (!notification.isRead)
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: notification.type.color,
                shape: BoxShape.circle,
              ),
            ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} د';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} س';
    } else {
      return '${time.day}/${time.month}';
    }
  }

  List<BusinessMessage> _getDemoMessages() {
    return [
      BusinessMessage(
        id: '1',
        senderName: 'أحمد محمد',
        senderImage: 'assets/images/Hussein Nihad.png',
        content: 'مرحباً، أريد الاستفسار عن الشقة المعروضة للإيجار',
        timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        isRead: false,
        postTitle: 'شقة للإيجار - 3 غرف وصالة',
      ),
      BusinessMessage(
        id: '2',
        senderName: 'فاطمة علي',
        senderImage: 'assets/images/Hussein Nihad.png',
        content: 'شكراً لك، تم تأكيد الحجز بنجاح',
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        isRead: true,
        postTitle: 'محل تجاري للإيجار',
      ),
      BusinessMessage(
        id: '3',
        senderName: 'محمد حسن',
        senderImage: 'assets/images/Hussein Nihad.png',
        content: 'هل يمكن ترتيب موعد للمعاينة غداً؟',
        timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        isRead: false,
        postTitle: 'مكتب للإيجار',
      ),
    ];
  }

  List<BusinessNotification> _getDemoNotifications() {
    return [
      BusinessNotification(
        id: '1',
        title: 'طلب حجز جديد',
        content: 'تم استلام طلب حجز جديد من أحمد محمد',
        timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
        type: NotificationType.booking,
        isRead: false,
      ),
      BusinessNotification(
        id: '2',
        title: 'تم تأكيد الدفع',
        content: 'تم تأكيد دفعة بقيمة 850,000 د.ع',
        timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        type: NotificationType.payment,
        isRead: false,
      ),
      BusinessNotification(
        id: '3',
        title: 'تقييم جديد',
        content: 'حصلت على تقييم 5 نجوم من فاطمة علي',
        timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        type: NotificationType.review,
        isRead: true,
      ),
    ];
  }
}

class BusinessMessage {
  final String id;
  final String senderName;
  final String senderImage;
  final String content;
  final DateTime timestamp;
  final bool isRead;
  final String postTitle;

  BusinessMessage({
    required this.id,
    required this.senderName,
    required this.senderImage,
    required this.content,
    required this.timestamp,
    required this.isRead,
    required this.postTitle,
  });
}

class BusinessNotification {
  final String id;
  final String title;
  final String content;
  final DateTime timestamp;
  final NotificationType type;
  final bool isRead;

  BusinessNotification({
    required this.id,
    required this.title,
    required this.content,
    required this.timestamp,
    required this.type,
    required this.isRead,
  });
}

enum NotificationType {
  booking,
  payment,
  review,
  system,
}

extension NotificationTypeExtension on NotificationType {
  Color get color {
    switch (this) {
      case NotificationType.booking:
        return Colors.blue;
      case NotificationType.payment:
        return Colors.green;
      case NotificationType.review:
        return Colors.orange;
      case NotificationType.system:
        return Colors.purple;
    }
  }

  IconData get icon {
    switch (this) {
      case NotificationType.booking:
        return Icons.event_available;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.review:
        return Icons.star;
      case NotificationType.system:
        return Icons.info;
    }
  }
}
