import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';
import '../models/user.dart';

class StoriesWidget extends StatelessWidget {
  final User currentUser;

  const StoriesWidget({
    super.key,
    required this.currentUser,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        children: [
          // Add Story Button
          _buildAddStoryCard(),

          // Demo Stories
          ..._getDemoStories().map((story) => _buildStoryCard(story)),
        ],
      ),
    );
  }

  Widget _buildAddStoryCard() {
    return Container(
      width: 80,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.grey[300]!,
                width: 2,
              ),
            ),
            child: Stack(
              children: [
                Center(
                  child: CircleAvatar(
                    radius: 25,
                    backgroundImage: currentUser.profileImage.isNotEmpty
                        ? NetworkImage(currentUser.profileImage)
                        : null,
                    child: currentUser.profileImage.isEmpty
                        ? const Icon(Icons.person, size: 25)
                        : null,
                  ),
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: const BoxDecoration(
                      color: Color(0xFF2196F3),
                      shape: BoxShape.circle,
                      border: Border.fromBorderSide(
                        BorderSide(color: Colors.white, width: 2),
                      ),
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قصتك',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildStoryCard(StoryData story) {
    return Container(
      width: 80,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [Color(0xFF2196F3), Color(0xFF03DAC6)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            padding: const EdgeInsets.all(2),
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              padding: const EdgeInsets.all(2),
              child: CircleAvatar(
                radius: 25,
                backgroundImage: story.userImage.isNotEmpty
                    ? NetworkImage(story.userImage)
                    : null,
                child: story.userImage.isEmpty
                    ? const Icon(Icons.person, size: 25)
                    : null,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            story.userName,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  List<StoryData> _getDemoStories() {
    return [
      StoryData(
        userName: 'سارة أحمد',
        userImage: 'https://via.placeholder.com/150',
      ),
      StoryData(
        userName: 'محمد علي',
        userImage: 'https://via.placeholder.com/150',
      ),
      StoryData(
        userName: 'فاطمة محمد',
        userImage: 'https://via.placeholder.com/150',
      ),
      StoryData(
        userName: 'أحمد حسن',
        userImage: 'https://via.placeholder.com/150',
      ),
      StoryData(
        userName: 'نور الدين',
        userImage: 'https://via.placeholder.com/150',
      ),
      StoryData(
        userName: 'ليلى عبدالله',
        userImage: 'https://via.placeholder.com/150',
      ),
    ];
  }
}

class StoryData {
  final String userName;
  final String userImage;

  StoryData({
    required this.userName,
    required this.userImage,
  });
}
