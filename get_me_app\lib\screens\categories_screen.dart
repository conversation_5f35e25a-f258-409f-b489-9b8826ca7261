import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';
import '../models/business_category.dart';
import '../widgets/category_card.dart';
import 'category_feed_screen.dart';

class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen> {
  final List<BusinessCategory> categories = BusinessCategory.getAllCategories();
  String searchQuery = '';

  @override
  Widget build(BuildContext context) {
    final filteredCategories = categories.where((category) {
      return category.nameAr.contains(searchQuery) ||
          category.nameEn.toLowerCase().contains(searchQuery.toLowerCase());
    }).toList();

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: Text(
          'الأقسام التجارية',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              style: const TextStyle(),
              decoration: InputDecoration(
                hintText: 'البحث في الأقسام...',
                hintStyle: const TextStyle(color: Colors.grey),
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                filled: true,
                fillColor: Colors.grey[100],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Categories Grid
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 0.85,
                  crossAxisSpacing: 15,
                  mainAxisSpacing: 15,
                ),
                itemCount: filteredCategories.length,
                itemBuilder: (context, index) {
                  final category = filteredCategories[index];
                  return CategoryCard(
                    category: category,
                    onTap: () => _navigateToCategoryFeed(category),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToCategoryFeed(BusinessCategory category) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CategoryFeedScreen(category: category),
      ),
    );
  }
}
