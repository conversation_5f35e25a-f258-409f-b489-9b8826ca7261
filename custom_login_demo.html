<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - Custom Login Design</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            overflow: hidden;
        }

        /* Welcome Screen */
        .welcome-screen {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6), rgba(0,0,0,0.8)), 
                        url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80');
            background-size: cover;
            background-position: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            animation: fadeIn 2s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .welcome-content {
            text-align: center;
            color: white;
            animation: slideUp 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(100px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .app-logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .app-logo i {
            color: white;
            font-size: 60px;
        }

        .welcome-title {
            font-size: 24px;
            color: rgba(255,255,255,0.7);
            margin-bottom: 8px;
            font-weight: 300;
        }

        .app-name {
            font-size: 48px;
            font-weight: bold;
            letter-spacing: 2px;
            margin-bottom: 16px;
        }

        .app-description {
            font-size: 18px;
            color: rgba(255,255,255,0.7);
            line-height: 1.5;
            margin-bottom: 60px;
        }

        .start-button {
            background: linear-gradient(135deg, #667eea, #764ba2, #667eea);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 30px;
            padding: 20px 40px;
            color: white;
            font-size: 20px;
            font-weight: bold;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .start-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
        }

        /* Login Screen */
        .login-screen {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(to left, rgba(0,0,0,0.1), rgba(0,0,0,0.7), rgba(0,0,0,0.9)), 
                        url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80');
            background-size: cover;
            background-position: center;
            display: none;
            align-items: center;
            position: relative;
        }

        .login-panel {
            width: 500px;
            margin: 50px;
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 35px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: slideInLeft 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .login-logo i {
            color: white;
            font-size: 40px;
        }

        .login-title {
            font-size: 32px;
            font-weight: bold;
            color: #1C1E21;
            margin-bottom: 8px;
        }

        .login-subtitle {
            font-size: 16px;
            color: #65676B;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            font-size: 16px;
            color: #1C1E21;
            background: #F5F6F7;
            border: none;
            border-radius: 12px;
            font-family: 'Cairo', sans-serif;
            direction: ltr;
            text-align: left;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            background: white;
            border: 2px solid #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }

        .form-input::placeholder {
            color: #8A8D91;
            direction: rtl;
            text-align: right;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .demo-info {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.3);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            margin-bottom: 20px;
        }

        .demo-info h4 {
            color: #667eea;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .demo-info p {
            color: #1C1E21;
            font-size: 12px;
        }

        .social-login {
            margin: 20px 0;
            text-align: center;
        }

        .social-title {
            font-size: 14px;
            color: #65676B;
            margin-bottom: 15px;
        }

        .social-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .social-btn {
            width: 50px;
            height: 50px;
            border: 2px solid #667eea;
            border-radius: 12px;
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .social-btn:hover {
            border-color: #764ba2;
            box-shadow: 0 0 15px rgba(102, 126, 234, 0.4);
            transform: translateY(-2px);
        }

        .social-btn i {
            font-size: 20px;
            color: #667eea;
        }

        .social-btn.email i { color: #667eea; }
        .social-btn.facebook i { color: #1877F2; }
        .social-btn.instagram i { color: #E4405F; }
        .social-btn.tiktok i { color: #000000; }

        .plans-link {
            text-align: center;
            margin-top: 20px;
        }

        .plans-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .plans-link a:hover {
            text-decoration: underline;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .login-panel {
                width: 90%;
                margin: 20px;
                padding: 30px;
            }
            
            .app-name {
                font-size: 36px;
            }
            
            .start-button {
                padding: 16px 32px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <!-- Welcome Screen -->
    <div class="welcome-screen" id="welcomeScreen">
        <div class="welcome-content">
            <div class="app-logo">
                <i class="fas fa-shopping-bag"></i>
            </div>
            <h2 class="welcome-title">مرحباً بك في</h2>
            <h1 class="app-name">Get Me</h1>
            <p class="app-description">
                منصة التجارة الإلكترونية الشاملة<br>
                حيث تجد كل ما تحتاجه
            </p>
            <button class="start-button" onclick="showLoginScreen()">
                ابدأ الآن
                <i class="fas fa-arrow-left"></i>
            </button>
        </div>
    </div>

    <!-- Login Screen -->
    <div class="login-screen" id="loginScreen">
        <div class="login-panel">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <h1 class="login-title">Get Me</h1>
                <p class="login-subtitle">سجل دخولك للمتابعة</p>
            </div>

            <div class="demo-info">
                <h4>بيانات تجريبية</h4>
                <p>اسم المستخدم: admin<br>كلمة المرور: 123456</p>
            </div>

            <form onsubmit="handleLogin(event)">
                <div class="form-group">
                    <input
                        type="text"
                        class="form-input"
                        placeholder="اسم المستخدم"
                        value="admin"
                        required
                    >
                </div>

                <div class="form-group">
                    <input
                        type="password"
                        class="form-input"
                        placeholder="كلمة المرور"
                        value="123456"
                        required
                    >
                </div>

                <button type="submit" class="login-btn">
                    تسجيل الدخول
                </button>
            </form>

            <div class="social-login">
                <div class="social-title">أو تسجيل الدخول بواسطة</div>
                <div class="social-buttons">
                    <div class="social-btn email" onclick="socialLogin('email')">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="social-btn facebook" onclick="socialLogin('facebook')">
                        <i class="fab fa-facebook-f"></i>
                    </div>
                    <div class="social-btn instagram" onclick="socialLogin('instagram')">
                        <i class="fab fa-instagram"></i>
                    </div>
                    <div class="social-btn tiktok" onclick="socialLogin('tiktok')">
                        <i class="fab fa-tiktok"></i>
                    </div>
                </div>
            </div>

            <div class="plans-link">
                <a href="#" onclick="showPlans()">عرض خطط الاشتراك</a>
            </div>
        </div>
    </div>

    <script>
        function showLoginScreen() {
            const welcomeScreen = document.getElementById('welcomeScreen');
            const loginScreen = document.getElementById('loginScreen');
            
            welcomeScreen.style.animation = 'fadeOut 0.8s ease-in-out forwards';
            
            setTimeout(() => {
                welcomeScreen.style.display = 'none';
                loginScreen.style.display = 'flex';
            }, 800);
        }

        function handleLogin(event) {
            event.preventDefault();
            
            const btn = event.target.querySelector('.login-btn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
            btn.disabled = true;
            
            setTimeout(() => {
                alert('تم تسجيل الدخول بنجاح!\n\nهذا هو التصميم المخصص الذي طلبته:\n\n✅ شاشة ترحيبية مع صورة خلفية جميلة\n✅ زر أنيق بألوان متدرجة\n✅ شاشة تسجيل دخول مع مستطيل على اليسار\n✅ شعار التطبيق\n✅ تصميم احترافي مع رسوم متحركة\n✅ استخدام مكتبة flutter_login\n✅ دعم كامل للغة العربية');
                btn.innerHTML = 'تسجيل الدخول';
                btn.disabled = false;
            }, 2000);
        }

        function socialLogin(platform) {
            const platformNames = {
                'email': 'البريد الإلكتروني',
                'facebook': 'فيسبوك',
                'instagram': 'انستجرام',
                'tiktok': 'تيكتوك'
            };

            alert(`سيتم تسجيل الدخول عبر ${platformNames[platform]}\n\nسيتم توجيهك لصفحة ${platformNames[platform]}...`);
        }

        function showPlans() {
            alert('سيتم الانتقال إلى شاشة خطط الاشتراك:\n\n💰 مشتري - $5\n🏪 بائع - $10 (الأكثر شعبية)\n⭐ بائع مميز - $25\n👁️ زائر - مجاني لأسبوع\n\nكل خطة مع مميزاتها وتصميم بطاقات احترافي!');
        }

        // Add fadeOut animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
