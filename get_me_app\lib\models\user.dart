class User {
  final String id;
  final String name;
  final String email;
  final String profileImage;
  final UserType userType;
  final bool isPremium;
  final DateTime joinDate;
  final String bio;
  final int followersCount;
  final int followingCount;
  final int postsCount;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.profileImage,
    required this.userType,
    this.isPremium = false,
    required this.joinDate,
    this.bio = '',
    this.followersCount = 0,
    this.followingCount = 0,
    this.postsCount = 0,
  });

  factory User.guest() {
    return User(
      id: 'guest',
      name: 'زائر',
      email: '',
      profileImage: '',
      userType: UserType.guest,
      joinDate: DateTime.now(),
    );
  }

  factory User.demo() {
    return User(
      id: '1',
      name: 'أحمد محمد',
      email: '<EMAIL>',
      profileImage: 'https://via.placeholder.com/150',
      userType: UserType.seller,
      isPremium: true,
      joinDate: DateTime.now().subtract(const Duration(days: 30)),
      bio: 'بائع في مجال الإلكترونيات والهواتف الذكية. خبرة أكثر من 5 سنوات في السوق.',
      followersCount: 1250,
      followingCount: 340,
      postsCount: 89,
    );
  }
}

enum UserType {
  guest,
  buyer,
  seller,
  promoter,
  teacher,
}

extension UserTypeExtension on UserType {
  String get displayName {
    switch (this) {
      case UserType.guest:
        return 'زائر';
      case UserType.buyer:
        return 'مشتري';
      case UserType.seller:
        return 'بائع';
      case UserType.promoter:
        return 'مروج';
      case UserType.teacher:
        return 'مدرس';
    }
  }

  String get description {
    switch (this) {
      case UserType.guest:
        return 'يمكنك تصفح المحتوى فقط';
      case UserType.buyer:
        return 'يمكنك الشراء والتفاعل مع المنشورات';
      case UserType.seller:
        return 'يمكنك بيع المنتجات والخدمات';
      case UserType.promoter:
        return 'يمكنك الترويج والربح من التسويق';
      case UserType.teacher:
        return 'يمكنك نشر المحتوى التعليمي والربح';
    }
  }
}
