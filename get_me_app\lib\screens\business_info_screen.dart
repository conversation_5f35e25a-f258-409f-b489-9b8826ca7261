import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import '../models/subscription_plan.dart';

class BusinessInfoScreen extends StatefulWidget {
  final UserRegistrationData registrationData;
  final VoidCallback onNext;
  final VoidCallback onBack;

  const BusinessInfoScreen({
    super.key,
    required this.registrationData,
    required this.onNext,
    required this.onBack,
  });

  @override
  State<BusinessInfoScreen> createState() => _BusinessInfoScreenState();
}

class _BusinessInfoScreenState extends State<BusinessInfoScreen> {
  final _formKey = GlobalKey<FormState>();
  final _companyController = TextEditingController();
  final _businessTypeController = TextEditingController();
  final _cityController = TextEditingController();
  final _provinceController = TextEditingController();
  final _facebookController = TextEditingController();
  final _instagramController = TextEditingController();
  final _twitterController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    _companyController.text = widget.registrationData.companyName ?? '';
    _businessTypeController.text = widget.registrationData.businessType ?? '';
    _cityController.text = widget.registrationData.city ?? '';
    _provinceController.text = widget.registrationData.province ?? '';
    
    final social = widget.registrationData.socialAccounts ?? {};
    _facebookController.text = social['facebook'] ?? '';
    _instagramController.text = social['instagram'] ?? '';
    _twitterController.text = social['twitter'] ?? '';
  }

  @override
  void dispose() {
    _companyController.dispose();
    _businessTypeController.dispose();
    _cityController.dispose();
    _provinceController.dispose();
    _facebookController.dispose();
    _instagramController.dispose();
    _twitterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 32),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        _buildCompanyField(),
                        const SizedBox(height: 20),
                        _buildBusinessTypeField(),
                        const SizedBox(height: 20),
                        _buildLocationFields(),
                        const SizedBox(height: 32),
                        _buildSocialSection(),
                        const SizedBox(height: 40),
                      ],
                    ),
                  ),
                ),
                _buildButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    widget.registrationData.selectedPlan?.primaryColor ??
                        const Color(0xFF667eea),
                    widget.registrationData.selectedPlan?.secondaryColor ??
                        const Color(0xFF764ba2),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                widget.registrationData.selectedPlan?.icon ?? Icons.business,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.registrationData.selectedPlan?.title ?? 'بائع',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1C1E21),
                    ),
                  ),
                  Text(
                    'بيانات العمل',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          'أدخل بيانات شركتك أو عملك',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }

  Widget _buildCompanyField() {
    return GFCard(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      content: TextFormField(
        controller: _companyController,
        decoration: const InputDecoration(
          labelText: 'اسم الشركة/المكتب',
          prefixIcon: Icon(Icons.business_outlined),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'يرجى إدخال اسم الشركة أو المكتب';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildBusinessTypeField() {
    return GFCard(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      content: TextFormField(
        controller: _businessTypeController,
        decoration: const InputDecoration(
          labelText: 'نوع النشاط',
          prefixIcon: Icon(Icons.work_outline),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
          hintText: 'مثال: تجارة إلكترونية، مطعم، خدمات...',
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'يرجى إدخال نوع النشاط';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildLocationFields() {
    return Column(
      children: [
        GFCard(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          content: TextFormField(
            controller: _cityController,
            decoration: const InputDecoration(
              labelText: 'المدينة',
              prefixIcon: Icon(Icons.location_city_outlined),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(16),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال المدينة';
              }
              return null;
            },
          ),
        ),
        const SizedBox(height: 20),
        GFCard(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          content: TextFormField(
            controller: _provinceController,
            decoration: const InputDecoration(
              labelText: 'المحافظة',
              prefixIcon: Icon(Icons.map_outlined),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(16),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال المحافظة';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSocialSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'حسابات التواصل الاجتماعي (اختياري)',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 16),
        GFCard(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          content: TextFormField(
            controller: _facebookController,
            decoration: const InputDecoration(
              labelText: 'Facebook',
              prefixIcon: Icon(Icons.facebook, color: Color(0xFF1877F2)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(16),
              hintText: 'https://facebook.com/yourpage',
            ),
          ),
        ),
        const SizedBox(height: 16),
        GFCard(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          content: TextFormField(
            controller: _instagramController,
            decoration: const InputDecoration(
              labelText: 'Instagram',
              prefixIcon: Icon(Icons.camera_alt, color: Color(0xFFE4405F)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(16),
              hintText: 'https://instagram.com/yourpage',
            ),
          ),
        ),
        const SizedBox(height: 16),
        GFCard(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          content: TextFormField(
            controller: _twitterController,
            decoration: const InputDecoration(
              labelText: 'Twitter',
              prefixIcon: Icon(Icons.alternate_email, color: Color(0xFF1DA1F2)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(16),
              hintText: 'https://twitter.com/yourpage',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: widget.onBack,
            style: OutlinedButton.styleFrom(
              foregroundColor: widget.registrationData.selectedPlan?.primaryColor ??
                  const Color(0xFF667eea),
              side: BorderSide(
                color: widget.registrationData.selectedPlan?.primaryColor ??
                    const Color(0xFF667eea),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'رجوع',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _handleContinue,
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.registrationData.selectedPlan?.primaryColor ??
                  const Color(0xFF667eea),
              foregroundColor: Colors.white,
              elevation: 8,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'متابعة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _handleContinue() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Save data
    widget.registrationData.companyName = _companyController.text;
    widget.registrationData.businessType = _businessTypeController.text;
    widget.registrationData.city = _cityController.text;
    widget.registrationData.province = _provinceController.text;
    
    widget.registrationData.socialAccounts = {
      'facebook': _facebookController.text,
      'instagram': _instagramController.text,
      'twitter': _twitterController.text,
    };

    widget.onNext();
  }
}
