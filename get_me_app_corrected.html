<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - وصّلني</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }

        /* الشريط العلوي */
        .header {
            background: linear-gradient(90deg, #16CCC8, #227FCC);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15), 0 4px 10px rgba(0,0,0,0.1);
        }

        /* اسم التطبيق */
        .app-name {
            font-size: 40px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-right: 20px;
        }

        .search-input {
            width: 100%;
            height: 40px;
            padding: 12px 20px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            background: rgba(255,255,255,0.9);
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .search-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            transition: all 0.3s;
        }

        .search-btn:hover {
            transform: scale(1.1);
        }

        .search-btn i {
            color: white;
            font-size: 24px;
            transition: all 0.3s;
        }

        /* قسم المستخدم - في الجهة اليسرى */
        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .menu-icon {
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background 0.3s;
        }

        .menu-icon:hover {
            background: rgba(255,255,255,0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            position: relative;
            cursor: pointer;
        }

        .user-avatar.online {
            border: 3px solid #28a745;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.6), 0 0 30px rgba(40, 167, 69, 0.4);
            animation: glow-green 2s ease-in-out infinite alternate;
        }

        .user-avatar.offline {
            border: 3px solid #6c757d;
            box-shadow: 0 0 10px rgba(108, 117, 125, 0.3);
        }

        @keyframes glow-green {
            from {
                box-shadow: 0 0 15px rgba(40, 167, 69, 0.6), 0 0 30px rgba(40, 167, 69, 0.4);
            }
            to {
                box-shadow: 0 0 20px rgba(40, 167, 69, 0.8), 0 0 40px rgba(40, 167, 69, 0.6);
            }
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-avatar-placeholder {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .user-name {
            color: white;
            font-size: 16px;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        /* البحث - في الوسط */
        .search-section {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
            justify-content: center;
            max-width: 600px;
            margin: 0 50px;
        }

        /* شريط التنقل */
        .nav-bar {
            background: white;
            position: fixed;
            top: 70px;
            left: 0;
            right: 0;
            z-index: 999;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .nav-items {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 25px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 18px;
            cursor: pointer;
            transition: all 0.3s;
            color: #495057;
            font-weight: 500;
            position: relative;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item:hover i, .nav-item.active i {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item i {
            font-size: 24px;
        }

        .nav-item.home-item {
            margin-left: 80px;
        }

        /* ألوان الإشعارات - بدون حركة */
        .nav-item.has-notification i {
            color: #ff0000 !important;
            background: none !important;
            -webkit-text-fill-color: #ff0000 !important;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #ff0000;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
            display: none;
        }

        .notification-badge.show {
            display: flex;
        }

        .nav-item.has-notification {
            color: #495057;
        }

        .nav-item.has-notification:hover, .nav-item.has-notification.active {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* قائمة الخطوط الثلاثة */
        .menu-dropdown {
            position: relative;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            min-width: 200px;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 1001;
            margin-top: 10px;
        }

        .dropdown-content.show {
            display: block;
        }

        .dropdown-item {
            padding: 12px 20px;
            color: #333;
            cursor: pointer;
            transition: background 0.3s;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .dropdown-item:hover {
            background: #f5f5f5;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 130px;
            padding: 20px;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* شريط الخدمات */
        .services-section {
            margin-bottom: 30px;
            position: relative;
        }

        .services-container {
            position: relative;
            overflow: hidden;
            padding: 10px 0;
        }

        .services-wrapper {
            display: flex;
            gap: 15px;
            transition: transform 1s ease;
            width: fit-content;
        }

        .nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 35px;
            height: 35px;
            border: none;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            font-size: 14px;
            z-index: 10;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .nav-arrow:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 4px 15px rgba(34, 127, 204, 0.4);
        }

        .nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: translateY(-50%);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .nav-arrow.prev {
            right: 10px;
        }

        .nav-arrow.next {
            left: 10px;
        }

        /* بطاقة الخدمة */
        .service-card {
            width: 180px;
            height: 260px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: transform 0.3s;
            position: relative;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
        }

        .service-card:hover {
            transform: translateY(-5px);
        }

        .service-image {
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .service-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .service-image-placeholder {
            font-size: 40px;
            color: #666;
        }

        .service-name {
            height: 60px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 15px;
            font-weight: bold;
            text-align: center;
            font-family: 'Cairo', sans-serif;
        }

        /* العروض المميزة */
        .featured-offers {
            margin: 30px 0;
        }

        .featured-card {
            width: 100%;
            max-width: 800px;
            height: 200px;
            margin: 0 auto;
            border: 3px solid #FFD700;
            border-radius: 20px;
            background: linear-gradient(135deg, #FFD700, #FFA000);
            position: relative;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.4);
            cursor: pointer;
            transition: transform 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .featured-card:hover {
            transform: scale(1.02);
        }

        .featured-badge {
            background: linear-gradient(135deg, #FFD700, #FF8F00);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        /* المحتوى الرئيسي */
        .content-area {
            display: flex;
            gap: 30px;
            margin-top: 30px;
        }

        /* منطقة المنشورات */
        .posts-area {
            flex: 1;
        }

        /* القصص */
        .stories-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .stories-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .story-item {
            min-width: 80px;
            text-align: center;
            cursor: pointer;
        }

        .story-avatar {
            width: 65px;
            height: 65px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
            background: #ddd;
        }

        .story-avatar.has-story {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            padding: 3px;
        }

        .story-avatar.no-story {
            background: #ccc;
            padding: 3px;
        }

        .story-inner {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .story-name {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        /* البث المباشر */
        .live-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .live-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .live-item {
            min-width: 80px;
            text-align: center;
            cursor: pointer;
        }

        .live-avatar {
            width: 65px;
            height: 65px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
        }

        .live-avatar.is-live {
            background: linear-gradient(45deg, #ff0000, #ff8c00, #ffd700, #9932cc);
            padding: 3px;
            animation: pulse 2s infinite;
        }

        .live-avatar.not-live {
            background: #ccc;
            padding: 3px;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* الشريط الجانبي */
        .sidebar {
            width: 350px;
        }

        /* مربع الإعلانات */
        .ads-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: 400px;
        }

        .ads-header {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .ads-container {
            height: 320px;
            overflow-y: auto;
        }

        .ad-item {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .ad-item:hover {
            transform: translateX(-5px);
        }

        /* المنشورات المميزة */
        .featured-posts {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        /* النوافذ المنبثقة */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.3);
            z-index: 2000;
        }

        .modal-content {
            background: white;
            width: 420px;
            position: absolute;
            top: 130px;
            right: 20px;
            border-radius: 12px;
            padding: 0;
            max-height: 70vh;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        /* نافذة الإشعارات المخصصة */
        #notificationsModal .modal-content {
            right: 120px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
        }

        .modal-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            flex: 1;
            text-align: center;
        }

        .modal-menu {
            position: relative;
        }

        .modal-menu-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
            padding: 5px;
        }

        .modal-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            min-width: 200px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 2001;
            margin-top: 5px;
        }

        .modal-dropdown.show {
            display: block;
        }

        .modal-dropdown-item {
            padding: 10px 14px;
            cursor: pointer;
            transition: background 0.3s;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .modal-dropdown-item:hover {
            background: #f5f5f5;
        }

        .modal-dropdown-item:last-child {
            border-bottom: none;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        /* فلاتر الإشعارات */
        .notification-filters {
            display: flex;
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }

        .filter-btn {
            background: none;
            border: none;
            padding: 6px 12px;
            margin-right: 8px;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s;
            font-family: 'Cairo', sans-serif;
            font-size: 12px;
            color: #666;
            white-space: nowrap;
        }

        .filter-btn.active {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
        }

        .filter-btn:hover {
            background: #e9ecef;
        }

        .filter-btn.active:hover {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
        }

        /* قائمة الإشعارات */
        .notifications-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-item {
            display: flex;
            align-items: flex-start;
            padding: 12px 16px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.3s;
            position: relative;
        }

        .notification-item:hover {
            background: #f8f9fa;
        }

        .notification-item.unread {
            background: #f0f8ff;
        }

        .notification-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            color: white;
            font-size: 14px;
        }

        .notification-content {
            flex: 1;
        }

        .notification-text {
            font-size: 13px;
            color: #333;
            line-height: 1.3;
            margin-bottom: 4px;
        }

        .notification-time {
            font-size: 11px;
            color: #666;
        }

        .notification-heart {
            margin-right: 12px;
            cursor: pointer;
            font-size: 16px;
            color: #ddd;
            transition: all 0.3s;
        }

        .notification-heart.read {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .notification-heart:hover {
            transform: scale(1.1);
        }

        /* نظام الرسائل */
        .messages-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.3);
            z-index: 2000;
        }

        .messages-window {
            background: white;
            width: 400px;
            position: absolute;
            top: 130px;
            right: 200px;
            border-radius: 12px;
            padding: 0;
            max-height: 70vh;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .messages-window.fullscreen {
            width: 100%;
            height: 100%;
            top: 0;
            right: 0;
            border-radius: 0;
            max-height: 100vh;
            display: flex;
        }

        .messages-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
        }

        .messages-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            flex: 1;
            text-align: center;
        }

        .messages-controls {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #666;
            padding: 5px;
            border-radius: 4px;
            transition: background 0.3s;
        }

        .control-btn:hover {
            background: #e9ecef;
        }

        .messages-search {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }

        .search-messages-input {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 20px;
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            background: #f8f9fa;
        }

        /* قائمة المحادثات */
        .conversations-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .conversation-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.3s;
        }

        .conversation-item:hover {
            background: #f8f9fa;
        }

        .conversation-item.active {
            background: #e3f2fd;
        }

        .conversation-item.unread {
            background: #f0f8ff;
        }

        .conversation-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            margin-left: 12px;
            position: relative;
        }

        .conversation-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .conversation-avatar-placeholder {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .conversation-info {
            flex: 1;
        }

        .conversation-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .conversation-preview {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-meta {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 5px;
        }

        .conversation-time {
            font-size: 11px;
            color: #999;
        }

        .conversation-badge {
            background: #ff0000;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }

        /* واجهة المحادثة الكاملة */
        .chat-interface {
            display: none;
            flex: 1;
            flex-direction: column;
            height: 100%;
        }

        .chat-sidebar {
            width: 350px;
            border-left: 1px solid #eee;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
        }

        .chat-user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-left: 12px;
        }

        .chat-user-info {
            flex: 1;
        }

        .chat-user-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .chat-user-status {
            font-size: 12px;
            color: #28a745;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            display: flex;
            margin-bottom: 15px;
        }

        .message.sent {
            justify-content: flex-end;
        }

        .message-bubble {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 18px;
            position: relative;
        }

        .message.received .message-bubble {
            background: white;
            color: #333;
            border-bottom-right-radius: 4px;
        }

        .message.sent .message-bubble {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-bottom-left-radius: 4px;
        }

        .message-text {
            font-size: 14px;
            line-height: 1.4;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .chat-input-area {
            padding: 15px 20px;
            border-top: 1px solid #eee;
            background: white;
        }

        .quick-messages {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .quick-msg-btn {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
        }

        .quick-msg-btn:hover {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-color: transparent;
        }

        .chat-input-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 20px;
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            resize: none;
            max-height: 100px;
        }

        .chat-attachments {
            display: flex;
            gap: 5px;
        }

        .attachment-btn {
            width: 35px;
            height: 35px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            font-size: 16px;
        }

        .attachment-btn.photo {
            background: #28a745;
            color: white;
        }

        .attachment-btn.video {
            background: #dc3545;
            color: white;
        }

        .attachment-btn.audio {
            background: #ffc107;
            color: white;
        }

        .attachment-btn.file {
            background: #6c757d;
            color: white;
        }

        .send-btn {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: transform 0.3s;
        }

        .send-btn:hover {
            transform: scale(1.1);
        }

        /* تجاوبية */
        @media (max-width: 768px) {
            .header {
                padding: 0 10px;
                height: 60px;
            }

            .nav-bar {
                top: 60px;
                height: 50px;
                padding: 0 10px;
            }

            .main-content {
                margin-top: 110px;
                padding: 10px;
            }

            .content-area {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .app-name {
                font-size: 20px;
            }

            .nav-items {
                gap: 15px;
            }

            .nav-item span {
                display: none;
            }

            /* تجاوبية النوافذ المنبثقة */
            .modal-content {
                width: 90%;
                right: 5%;
                left: 5%;
                max-width: none;
            }

            #notificationsModal .modal-content {
                right: 5%;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <div class="header">
        <!-- قسم المستخدم - في الجهة اليسرى -->
        <div class="user-section">
            <div class="menu-icon" onclick="toggleUserMenu()">
                <i class="fas fa-bars"></i>
            </div>
            <div class="user-avatar online" id="userAvatar">
                <img src="images/Hussein Nihad.png" alt="Hussein Nihad" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="user-avatar-placeholder" style="display: none;">
                    <i class="fas fa-user"></i>
                </div>
            </div>
            <div class="user-name">Hussein Nihad</div>
        </div>

        <!-- البحث - في الوسط -->
        <div class="search-section">
            <input type="text" class="search-input" placeholder="اختر نوع الخدمة" id="searchInput">
            <button class="search-btn" onclick="performSearch()">
                <i class="fas fa-search"></i>
            </button>
        </div>

        <!-- اسم التطبيق - في الجهة اليمنى -->
        <div class="app-name">Get Me</div>
    </div>

    <!-- شريط التنقل -->
    <div class="nav-bar">
        <div class="nav-items">
            <div class="nav-item active home-item" onclick="switchTab('home')">
                <i class="fas fa-home"></i>
                <span>الرئيسية</span>
            </div>
            <div class="nav-item" onclick="openNotifications()" id="notificationsBtn">
                <i class="fas fa-bell"></i>
                <span>الإشعارات</span>
                <div class="notification-badge" id="notificationsBadge">5</div>
            </div>
            <div class="nav-item" onclick="openMessages()" id="messagesBtn">
                <i class="fas fa-envelope"></i>
                <span>الرسائل</span>
                <div class="notification-badge show" id="messagesBadge">3</div>
            </div>
            <div class="nav-item" onclick="openProfile()">
                <i class="fas fa-user"></i>
                <span>الملف الشخصي</span>
            </div>
            <div class="nav-item menu-dropdown" onclick="toggleMenu()">
                <i class="fas fa-bars"></i>
                <div class="dropdown-content" id="menuDropdown">
                    <div class="dropdown-item" onclick="showNotification('المساعدة والدعم')">
                        <i class="fas fa-question-circle"></i> المساعدة والدعم
                    </div>
                    <div class="dropdown-item" onclick="showNotification('الإعدادات والخصوصية')">
                        <i class="fas fa-cog"></i> الإعدادات والخصوصية
                    </div>
                    <div class="dropdown-item" onclick="showNotification('تسجيل الخروج')">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط الخدمات -->
        <div class="services-section">
            <div class="services-container" id="servicesContainer">
                <button class="nav-arrow prev" id="prevBtn" onclick="moveRight()">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button class="nav-arrow next" id="nextBtn" onclick="moveLeft()">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="services-wrapper" id="servicesWrapper">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- العروض المميزة -->
        <div class="featured-offers">
            <div class="featured-card" onclick="showNotification('تم النقر على العرض المميز')">
                <div class="featured-badge">عرض مميز</div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content-area">
            <!-- منطقة المنشورات -->
            <div class="posts-area">
                <!-- القصص -->
                <div class="stories-section">
                    <h3 style="margin-bottom: 15px;">القصص</h3>
                    <div class="stories-container" id="storiesContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- البث المباشر -->
                <div class="live-section">
                    <h3 style="margin-bottom: 15px;">البث المباشر</h3>
                    <div class="live-container" id="liveContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="sidebar">
                <!-- مربع الإعلانات -->
                <div class="ads-section">
                    <div class="ads-header">مشاهدة الإعلانات</div>
                    <div class="ads-container" id="adsContainer">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- المنشورات المميزة -->
                <div class="featured-posts">
                    <div class="ads-header">المنشورات المميزة</div>
                    <div id="featuredPostsContainer">
                        <p style="text-align: center; color: #666; padding: 20px;">
                            المنشورات المميزة ستظهر هنا
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة الإشعارات -->
    <div class="modal" id="notificationsModal">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close-btn" onclick="closeModal('notificationsModal')">&times;</button>
                <div class="modal-title">الإشعارات</div>
                <div class="modal-menu">
                    <button class="modal-menu-btn" onclick="toggleNotificationMenu()">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="modal-dropdown" id="notificationMenuDropdown">
                        <div class="modal-dropdown-item" onclick="openNotificationSettings()">
                            <i class="fas fa-cog"></i> إعدادات الإشعارات
                        </div>
                        <div class="modal-dropdown-item" onclick="markAllAsRead()">
                            <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                        </div>
                        <div class="modal-dropdown-item" onclick="clearAllNotifications()">
                            <i class="fas fa-trash"></i> مسح جميع الإشعارات
                        </div>
                    </div>
                </div>
            </div>

            <div class="notification-filters">
                <button class="filter-btn active" onclick="filterNotifications('all')">الكل</button>
                <button class="filter-btn" onclick="filterNotifications('unread')">غير مقروءة</button>
                <button class="filter-btn" onclick="filterNotifications('messages')">الرسائل</button>
                <button class="filter-btn" onclick="filterNotifications('likes')">الإعجابات</button>
                <button class="filter-btn" onclick="filterNotifications('follows')">المتابعات</button>
            </div>

            <div class="notifications-list" id="notificationsContainer">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>

    <!-- نافذة الرسائل -->
    <div class="messages-modal" id="messagesModal">
        <div class="messages-window" id="messagesWindow">
            <!-- واجهة قائمة المحادثات -->
            <div id="conversationsList">
                <div class="messages-header">
                    <button class="close-btn" onclick="closeModal('messagesModal')">&times;</button>
                    <div class="messages-title">الرسائل</div>
                    <div class="messages-controls">
                        <button class="control-btn" onclick="toggleMessagesFullscreen()" title="ملء الشاشة">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="control-btn" onclick="showMessagesMenu()" title="المزيد">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>

                <div class="messages-search">
                    <input type="text" class="search-messages-input" placeholder="البحث في الرسائل..." id="messagesSearchInput">
                </div>

                <div class="conversations-list" id="conversationsContainer">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- واجهة المحادثة الكاملة -->
            <div class="chat-interface" id="chatInterface">
                <div class="chat-sidebar">
                    <div class="messages-header">
                        <button class="close-btn" onclick="toggleMessagesFullscreen()">&times;</button>
                        <div class="messages-title">الرسائل</div>
                        <div class="messages-controls">
                            <button class="control-btn" onclick="showMessagesMenu()">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>

                    <div class="messages-search">
                        <input type="text" class="search-messages-input" placeholder="البحث في الرسائل...">
                    </div>

                    <div class="conversations-list" id="conversationsContainerFull">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <div style="flex: 1; display: flex; flex-direction: column;">
                    <div class="chat-header" id="chatHeader">
                        <!-- سيتم ملؤها عند اختيار محادثة -->
                    </div>

                    <div class="chat-messages" id="chatMessages">
                        <!-- سيتم ملؤها بالرسائل -->
                    </div>

                    <div class="chat-input-area">
                        <div class="quick-messages">
                            <button class="quick-msg-btn" onclick="sendQuickMessage('مرحباً، كيف يمكنني مساعدتك؟')">مرحباً</button>
                            <button class="quick-msg-btn" onclick="sendQuickMessage('ما هو سعر هذا المنتج؟')">طلب السعر</button>
                            <button class="quick-msg-btn" onclick="sendQuickMessage('هل يمكنك إرسال تفاصيل العرض المميز؟')">العرض المميز</button>
                            <button class="quick-msg-btn" onclick="sendQuickMessage('أريد معرفة العروض المتاحة')">طلب عروض</button>
                            <button class="quick-msg-btn" onclick="sendQuickMessage('متى يمكن التسليم؟')">موعد التسليم</button>
                            <button class="quick-msg-btn" onclick="sendQuickMessage('شكراً لك')">شكراً</button>
                        </div>

                        <div class="chat-input-container">
                            <div class="chat-attachments">
                                <button class="attachment-btn photo" onclick="attachFile('photo')" title="صورة">
                                    <i class="fas fa-camera"></i>
                                </button>
                                <button class="attachment-btn video" onclick="attachFile('video')" title="فيديو">
                                    <i class="fas fa-video"></i>
                                </button>
                                <button class="attachment-btn audio" onclick="attachFile('audio')" title="صوت">
                                    <i class="fas fa-microphone"></i>
                                </button>
                                <button class="attachment-btn file" onclick="attachFile('file')" title="ملف">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                            </div>

                            <textarea class="chat-input" id="chatInput" placeholder="اكتب رسالتك..." rows="1"></textarea>

                            <button class="send-btn" onclick="sendMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات الخدمات
        const services = [
            { name: 'مطعم', icon: 'fas fa-utensils', image: 'مطعم.jpg' },
            { name: 'طبية', icon: 'fas fa-pills', image: 'طبية.jpg' },
            { name: 'مستحضرات تجميل', icon: 'fas fa-spa', image: 'مستحظرات تجميل.jpg' },
            { name: 'محلات', icon: 'fas fa-tshirt', image: 'محلات.jpg' },
            { name: 'السوبرماركت', icon: 'fas fa-shopping-cart', image: 'السوبرماركت.jpg' },
            { name: 'الطبية', icon: 'fas fa-user-md', image: 'الطبية.jpg' },
            { name: 'تعليم', icon: 'fas fa-graduation-cap', image: 'تعليم.jpg' },
            { name: 'سيارات', icon: 'fas fa-car', image: 'سيارات.jpg' },
            { name: 'عقارات', icon: 'fas fa-home', image: 'عقارات.jpg' },
            { name: 'كافيهات', icon: 'fas fa-coffee', image: 'كافيهات.jpg' },
            { name: 'مصارف وبنوك', icon: 'fas fa-university', image: 'مصارف وبنوك.jpg' },
            { name: 'زراعية', icon: 'fas fa-seedling', image: 'زراعية.jpg' },
            { name: 'توصيل', icon: 'fas fa-truck', image: 'توصيل.jpg' },
            { name: 'الصيرفة', icon: 'fas fa-coins', image: 'الصيرفة.jpg' },
            { name: 'الصناعة', icon: 'fas fa-industry', image: 'الصناعة.jpg' },
            { name: 'ذهب', icon: 'fas fa-gem', image: 'ذهب.jpg' },
            { name: 'قرطاسية', icon: 'fas fa-pen', image: 'قرطاسية.jpg' },
            { name: 'شركات السياحية', icon: 'fas fa-plane', image: 'شركات السياحية.jpg' },
            { name: 'مصايف', icon: 'fas fa-umbrella-beach', image: 'مصايف.jpg' },
            { name: 'استيراد وتصدير', icon: 'fas fa-ship', image: 'استيراد وتصدير.jpg' }
        ];

        // بيانات القصص
        const stories = [
            { name: 'قصتك', hasStory: false, isAdd: true },
            { name: 'أحمد محمد', hasStory: true },
            { name: 'فاطمة أحمد', hasStory: true },
            { name: 'محمد علي', hasStory: false },
            { name: 'سارة حسن', hasStory: true },
            { name: 'عبدالله أحمد', hasStory: false }
        ];

        // بيانات البث المباشر
        const liveStreams = [
            { name: 'أحمد محمد', isLive: true },
            { name: 'فاطمة أحمد', isLive: false },
            { name: 'محمد علي', isLive: true },
            { name: 'سارة حسن', isLive: false },
            { name: 'عبدالله أحمد', isLive: true }
        ];

        // بيانات الإعلانات
        let ads = [
            'إعلان رقم 1 - عرض خاص على الهواتف الذكية',
            'إعلان رقم 2 - خصم 50% على الملابس',
            'إعلان رقم 3 - مطعم جديد في المنطقة',
            'إعلان رقم 4 - دورة تعليمية مجانية',
            'إعلان رقم 5 - عقارات بأسعار مميزة',
            'إعلان رقم 6 - خدمات توصيل سريعة'
        ];

        // بيانات الإشعارات
        let notifications = [
            {
                id: 1,
                type: 'message',
                icon: 'fas fa-envelope',
                iconColor: '#007bff',
                text: 'رسالة جديدة من أحمد محمد',
                time: 'منذ 5 دقائق',
                read: false
            },
            {
                id: 2,
                type: 'like',
                icon: 'fas fa-heart',
                iconColor: '#dc3545',
                text: 'أعجب فاطمة أحمد بتعليقك على منشور مطعم الأصالة',
                time: 'منذ 15 دقيقة',
                read: false
            },
            {
                id: 3,
                type: 'comment',
                icon: 'fas fa-comment',
                iconColor: '#28a745',
                text: 'رد محمد علي على تعليقك: "شكراً لك على المعلومات المفيدة"',
                time: 'منذ 30 دقيقة',
                read: true
            },
            {
                id: 4,
                type: 'follow',
                icon: 'fas fa-user-plus',
                iconColor: '#17a2b8',
                text: 'بدأ سارة حسن بمتابعتك',
                time: 'منذ ساعة',
                read: false
            },
            {
                id: 5,
                type: 'like',
                icon: 'fas fa-thumbs-up',
                iconColor: '#6f42c1',
                text: 'أعجب 5 أشخاص بمنشورك الأخير',
                time: 'منذ ساعتين',
                read: true
            },
            {
                id: 6,
                type: 'message',
                icon: 'fas fa-envelope',
                iconColor: '#007bff',
                text: 'رسالة جديدة من عبدالله أحمد حول طلب الخدمة',
                time: 'منذ 3 ساعات',
                read: false
            }
        ];

        let currentFilter = 'all';

        // بيانات المحادثات
        let conversations = [
            {
                id: 1,
                name: 'أحمد محمد',
                avatar: null,
                lastMessage: 'شكراً لك على الخدمة الممتازة',
                time: 'منذ 5 دقائق',
                unreadCount: 2,
                online: true,
                messages: [
                    { id: 1, text: 'مرحباً، أريد الاستفسار عن المنتج', sent: false, time: '14:30' },
                    { id: 2, text: 'مرحباً بك، كيف يمكنني مساعدتك؟', sent: true, time: '14:32' },
                    { id: 3, text: 'ما هو سعر هذا المنتج؟', sent: false, time: '14:35' },
                    { id: 4, text: 'السعر 50 دينار مع التوصيل المجاني', sent: true, time: '14:36' },
                    { id: 5, text: 'شكراً لك على الخدمة الممتازة', sent: false, time: '14:40' }
                ]
            },
            {
                id: 2,
                name: 'فاطمة أحمد',
                avatar: null,
                lastMessage: 'متى يمكن التسليم؟',
                time: 'منذ 15 دقيقة',
                unreadCount: 1,
                online: false,
                messages: [
                    { id: 1, text: 'هل المنتج متوفر؟', sent: false, time: '13:45' },
                    { id: 2, text: 'نعم متوفر، كم تريد؟', sent: true, time: '13:46' },
                    { id: 3, text: 'قطعتين من فضلك', sent: false, time: '13:47' },
                    { id: 4, text: 'تمام، المجموع 100 دينار', sent: true, time: '13:48' },
                    { id: 5, text: 'متى يمكن التسليم؟', sent: false, time: '13:50' }
                ]
            },
            {
                id: 3,
                name: 'محمد علي',
                avatar: null,
                lastMessage: 'تم استلام الطلب بنجاح',
                time: 'منذ ساعة',
                unreadCount: 0,
                online: true,
                messages: [
                    { id: 1, text: 'أريد طلب من المطعم', sent: false, time: '12:30' },
                    { id: 2, text: 'أهلاً وسهلاً، ما تريد؟', sent: true, time: '12:31' },
                    { id: 3, text: 'وجبة مشاوي مع الأرز', sent: false, time: '12:32' },
                    { id: 4, text: 'تمام، سيصل خلال 30 دقيقة', sent: true, time: '12:33' },
                    { id: 5, text: 'تم استلام الطلب بنجاح', sent: false, time: '13:00' }
                ]
            }
        ];

        let currentConversation = null;
        let isMessagesFullscreen = false;

        // متغيرات التنقل في الخدمات
        let currentPosition = 0;
        const cardWidth = 180; // عرض البطاقة
        const cardGap = 15; // المسافة بين البطاقات
        const visibleCards = 7; // عدد البطاقات المرئية
        const moveDistance = cardWidth + cardGap; // المسافة للحركة الواحدة

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            populateServices();
            populateStories();
            populateLiveStreams();
            populateAds();
            startAdRotation();
            initializeNotifications();
            populateNotifications();
            populateConversations();
            updateMessagesBadge();

            // إضافة البحث عند الضغط على Enter
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        performSearch();
                    }
                });
            }

            // إضافة Enter للرسائل
            const chatInput = document.getElementById('chatInput');
            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
            }
        });

        // تهيئة الإشعارات
        function initializeNotifications() {
            // تفعيل إشعارات الرسائل
            const messagesBtn = document.getElementById('messagesBtn');
            const messagesBadge = document.getElementById('messagesBadge');
            if (messagesBtn && messagesBadge) {
                messagesBtn.classList.add('has-notification');
                messagesBadge.classList.add('show');
            }

            // تفعيل إشعارات التنبيهات
            const notificationsBtn = document.getElementById('notificationsBtn');
            const notificationsBadge = document.getElementById('notificationsBadge');
            if (notificationsBtn && notificationsBadge) {
                notificationsBtn.classList.add('has-notification');
                notificationsBadge.classList.add('show');
            }

            // تبديل حالة الاتصال كل 30 ثانية (للعرض التوضيحي)
            setInterval(toggleConnectionStatus, 30000);
        }

        // تبديل حالة الاتصال
        function toggleConnectionStatus() {
            const userAvatar = document.getElementById('userAvatar');
            if (userAvatar) {
                if (userAvatar.classList.contains('online')) {
                    userAvatar.classList.remove('online');
                    userAvatar.classList.add('offline');
                } else {
                    userAvatar.classList.remove('offline');
                    userAvatar.classList.add('online');
                }
            }
        }

        // قائمة المستخدم
        function toggleUserMenu() {
            showNotification('قائمة المستخدم - سيتم إضافة الخصائص لاحقاً');
        }

        // ملء الإشعارات
        function populateNotifications() {
            const container = document.getElementById('notificationsContainer');
            if (!container) return;

            const filteredNotifications = filterNotificationsByType(currentFilter);

            if (filteredNotifications.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-bell-slash" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p>لا توجد إشعارات</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredNotifications.map(notification => `
                <div class="notification-item ${notification.read ? '' : 'unread'}" onclick="markAsRead(${notification.id})">
                    <div class="notification-icon" style="background: ${notification.iconColor};">
                        <i class="${notification.icon}"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-text">${notification.text}</div>
                        <div class="notification-time">${notification.time}</div>
                    </div>
                    <div class="notification-heart ${notification.read ? 'read' : ''}" onclick="toggleNotificationRead(event, ${notification.id})">
                        <i class="fas fa-heart"></i>
                    </div>
                </div>
            `).join('');
        }

        // فلترة الإشعارات
        function filterNotificationsByType(type) {
            switch(type) {
                case 'unread':
                    return notifications.filter(n => !n.read);
                case 'messages':
                    return notifications.filter(n => n.type === 'message');
                case 'likes':
                    return notifications.filter(n => n.type === 'like');
                case 'follows':
                    return notifications.filter(n => n.type === 'follow');
                default:
                    return notifications;
            }
        }

        // تبديل فلتر الإشعارات
        function filterNotifications(type) {
            currentFilter = type;

            // تحديث أزرار الفلتر
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            populateNotifications();
        }

        // تحديد إشعار كمقروء
        function markAsRead(notificationId) {
            const notification = notifications.find(n => n.id === notificationId);
            if (notification && !notification.read) {
                notification.read = true;
                populateNotifications();
                updateNotificationBadge();
            }
        }

        // تبديل حالة قراءة الإشعار
        function toggleNotificationRead(event, notificationId) {
            event.stopPropagation();
            const notification = notifications.find(n => n.id === notificationId);
            if (notification) {
                notification.read = !notification.read;
                populateNotifications();
                updateNotificationBadge();
            }
        }

        // تحديث شارة الإشعارات
        function updateNotificationBadge() {
            const unreadCount = notifications.filter(n => !n.read).length;
            const badge = document.getElementById('notificationsBadge');
            const btn = document.getElementById('notificationsBtn');

            if (unreadCount > 0) {
                badge.textContent = unreadCount;
                badge.classList.add('show');
                btn.classList.add('has-notification');
            } else {
                badge.classList.remove('show');
                btn.classList.remove('has-notification');
            }
        }

        // قائمة إعدادات الإشعارات
        function toggleNotificationMenu() {
            const dropdown = document.getElementById('notificationMenuDropdown');
            dropdown.classList.toggle('show');
        }

        // إعدادات الإشعارات
        function openNotificationSettings() {
            showNotification('إعدادات الإشعارات - سيتم تطويرها لاحقاً');
            document.getElementById('notificationMenuDropdown').classList.remove('show');
        }

        // تحديد جميع الإشعارات كمقروءة
        function markAllAsRead() {
            notifications.forEach(n => n.read = true);
            populateNotifications();
            updateNotificationBadge();
            showNotification('تم تحديد جميع الإشعارات كمقروءة');
            document.getElementById('notificationMenuDropdown').classList.remove('show');
        }

        // مسح جميع الإشعارات
        function clearAllNotifications() {
            if (confirm('هل أنت متأكد من مسح جميع الإشعارات؟')) {
                notifications.length = 0;
                populateNotifications();
                updateNotificationBadge();
                showNotification('تم مسح جميع الإشعارات');
                document.getElementById('notificationMenuDropdown').classList.remove('show');
            }
        }

        // ===== وظائف الرسائل =====

        // ملء قائمة المحادثات
        function populateConversations() {
            const containers = [
                document.getElementById('conversationsContainer'),
                document.getElementById('conversationsContainerFull')
            ];

            containers.forEach(container => {
                if (!container) return;

                if (conversations.length === 0) {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-comments" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                            <p>لا توجد محادثات</p>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = conversations.map(conv => `
                    <div class="conversation-item ${conv.unreadCount > 0 ? 'unread' : ''}" onclick="openConversation(${conv.id})">
                        <div class="conversation-avatar">
                            ${conv.avatar ?
                                `<img src="${conv.avatar}" alt="${conv.name}">` :
                                `<div class="conversation-avatar-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>`
                            }
                        </div>
                        <div class="conversation-info">
                            <div class="conversation-name">${conv.name}</div>
                            <div class="conversation-preview">${conv.lastMessage}</div>
                        </div>
                        <div class="conversation-meta">
                            <div class="conversation-time">${conv.time}</div>
                            ${conv.unreadCount > 0 ? `<div class="conversation-badge">${conv.unreadCount}</div>` : ''}
                        </div>
                    </div>
                `).join('');
            });
        }

        // فتح محادثة
        function openConversation(conversationId) {
            currentConversation = conversations.find(c => c.id === conversationId);
            if (!currentConversation) return;

            // تحديد المحادثة كمقروءة
            currentConversation.unreadCount = 0;
            updateMessagesBadge();
            populateConversations();

            if (isMessagesFullscreen) {
                // إظهار واجهة المحادثة
                loadChatInterface();
            } else {
                // التبديل لملء الشاشة
                toggleMessagesFullscreen();
                setTimeout(() => loadChatInterface(), 300);
            }
        }

        // تحميل واجهة المحادثة
        function loadChatInterface() {
            if (!currentConversation) return;

            // تحديث رأس المحادثة
            const chatHeader = document.getElementById('chatHeader');
            chatHeader.innerHTML = `
                <div class="chat-user-avatar">
                    ${currentConversation.avatar ?
                        `<img src="${currentConversation.avatar}" alt="${currentConversation.name}">` :
                        `<div class="conversation-avatar-placeholder">
                            <i class="fas fa-user"></i>
                        </div>`
                    }
                </div>
                <div class="chat-user-info">
                    <div class="chat-user-name">${currentConversation.name}</div>
                    <div class="chat-user-status">${currentConversation.online ? 'متصل الآن' : 'غير متصل'}</div>
                </div>
                <div class="messages-controls">
                    <button class="control-btn" onclick="callUser()" title="مكالمة">
                        <i class="fas fa-phone"></i>
                    </button>
                    <button class="control-btn" onclick="videoCall()" title="مكالمة فيديو">
                        <i class="fas fa-video"></i>
                    </button>
                </div>
            `;

            // تحميل الرسائل
            loadMessages();
        }

        // تحميل الرسائل
        function loadMessages() {
            if (!currentConversation) return;

            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = currentConversation.messages.map(msg => `
                <div class="message ${msg.sent ? 'sent' : 'received'}">
                    <div class="message-bubble">
                        <div class="message-text">${msg.text}</div>
                        <div class="message-time">${msg.time}</div>
                    </div>
                </div>
            `).join('');

            // التمرير للأسفل
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // تبديل ملء الشاشة
        function toggleMessagesFullscreen() {
            const messagesWindow = document.getElementById('messagesWindow');
            const conversationsList = document.getElementById('conversationsList');
            const chatInterface = document.getElementById('chatInterface');

            isMessagesFullscreen = !isMessagesFullscreen;

            if (isMessagesFullscreen) {
                messagesWindow.classList.add('fullscreen');
                conversationsList.style.display = 'none';
                chatInterface.style.display = 'flex';
                populateConversations(); // تحديث القائمة في الشريط الجانبي
            } else {
                messagesWindow.classList.remove('fullscreen');
                conversationsList.style.display = 'block';
                chatInterface.style.display = 'none';
                currentConversation = null;
            }
        }

        // إرسال رسالة
        function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const messageText = chatInput.value.trim();

            if (!messageText || !currentConversation) return;

            // إضافة الرسالة للمحادثة
            const newMessage = {
                id: currentConversation.messages.length + 1,
                text: messageText,
                sent: true,
                time: new Date().toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })
            };

            currentConversation.messages.push(newMessage);
            currentConversation.lastMessage = messageText;
            currentConversation.time = 'الآن';

            // تحديث الواجهة
            loadMessages();
            populateConversations();

            // مسح النص
            chatInput.value = '';

            // محاكاة رد تلقائي
            setTimeout(() => {
                const autoReply = {
                    id: currentConversation.messages.length + 1,
                    text: 'شكراً لك، سأرد عليك قريباً',
                    sent: false,
                    time: new Date().toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })
                };
                currentConversation.messages.push(autoReply);
                currentConversation.lastMessage = autoReply.text;
                loadMessages();
                populateConversations();
            }, 2000);
        }

        // إرسال رسالة سريعة
        function sendQuickMessage(message) {
            const chatInput = document.getElementById('chatInput');
            chatInput.value = message;
            sendMessage();
        }

        // تحديث شارة الرسائل
        function updateMessagesBadge() {
            const totalUnread = conversations.reduce((sum, conv) => sum + conv.unreadCount, 0);
            const badge = document.getElementById('messagesBadge');
            const btn = document.getElementById('messagesBtn');

            if (totalUnread > 0) {
                badge.textContent = totalUnread;
                badge.classList.add('show');
                btn.classList.add('has-notification');
            } else {
                badge.classList.remove('show');
                btn.classList.remove('has-notification');
            }
        }

        // إرفاق ملف
        function attachFile(type) {
            const fileTypes = {
                photo: 'image/*',
                video: 'video/*',
                audio: 'audio/*',
                file: '*/*'
            };

            const input = document.createElement('input');
            input.type = 'file';
            input.accept = fileTypes[type];
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    showNotification(`تم اختيار ${file.name} - سيتم إضافة رفع الملفات لاحقاً`);
                }
            };
            input.click();
        }

        // عرض قائمة الرسائل
        function showMessagesMenu() {
            showNotification('قائمة خصائص الرسائل - سيتم إضافتها لاحقاً');
        }

        // مكالمة صوتية
        function callUser() {
            if (currentConversation) {
                showNotification(`مكالمة صوتية مع ${currentConversation.name} - سيتم إضافتها لاحقاً`);
            }
        }

        // مكالمة فيديو
        function videoCall() {
            if (currentConversation) {
                showNotification(`مكالمة فيديو مع ${currentConversation.name} - سيتم إضافتها لاحقاً`);
            }
        }

        // ملء الخدمات
        function populateServices() {
            const wrapper = document.getElementById('servicesWrapper');
            wrapper.innerHTML = services.map(service => `
                <div class="service-card" onclick="openServicePage('${service.name}')">
                    <div class="service-image">
                        <img src="images/${service.image}" alt="${service.name}"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div class="service-image-placeholder" style="display: none;">
                            <i class="${service.icon}"></i>
                        </div>
                    </div>
                    <div class="service-name">${service.name}</div>
                </div>
            `).join('');

            updateNavigationButtons();
        }

        // تحديث أزرار التنقل
        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn'); // السهم الأيمن
            const nextBtn = document.getElementById('nextBtn'); // السهم الأيسر
            const maxPosition = (services.length - visibleCards) * moveDistance;

            // السهم الأيمن معطل عندما نصل للنهاية (لا يمكن إظهار المزيد)
            prevBtn.disabled = currentPosition >= maxPosition;

            // السهم الأيسر معطل عندما نكون في البداية (لا يمكن العودة أكثر)
            nextBtn.disabled = currentPosition <= 0;

            console.log('تحديث الأزرار - الموضع:', currentPosition, 'أقصى موضع:', maxPosition);
            console.log('السهم الأيمن معطل:', prevBtn.disabled, 'السهم الأيسر معطل:', nextBtn.disabled);
        }

        // تحريك الشريط لليمين (السهم الأيمن)
        function moveRight() {
            const wrapper = document.getElementById('servicesWrapper');
            const maxPosition = (services.length - visibleCards) * moveDistance;

            console.log('=== تحريك لليمين ===');
            console.log('الموضع الحالي:', currentPosition);
            console.log('أقصى موضع:', maxPosition);

            // تحريك الشريط لليمين (زيادة currentPosition لإظهار المزيد من الخدمات)
            if (currentPosition < maxPosition) {
                currentPosition += moveDistance;
                console.log('تم التحريك لليمين - إظهار المزيد من الخدمات');
            } else {
                console.log('وصلنا لآخر الخدمات');
            }

            // التأكد من عدم تجاوز الحدود
            currentPosition = Math.min(maxPosition, currentPosition);

            console.log('الموضع الجديد:', currentPosition);
            wrapper.style.transform = `translateX(-${currentPosition}px)`;
            updateNavigationButtons();
        }

        // تحريك الشريط لليسار (السهم الأيسر)
        function moveLeft() {
            const wrapper = document.getElementById('servicesWrapper');
            const maxPosition = (services.length - visibleCards) * moveDistance;

            console.log('=== تحريك لليسار ===');
            console.log('الموضع الحالي:', currentPosition);

            // تحريك الشريط لليسار (تقليل currentPosition للعودة للخدمات السابقة)
            if (currentPosition > 0) {
                currentPosition -= moveDistance;
                console.log('تم التحريك لليسار - العودة للخدمات السابقة');
            } else {
                console.log('نحن في بداية الخدمات');
            }

            // التأكد من عدم تجاوز الحدود
            currentPosition = Math.max(0, currentPosition);

            console.log('الموضع الجديد:', currentPosition);
            wrapper.style.transform = `translateX(-${currentPosition}px)`;
            updateNavigationButtons();
        }

        // ملء القصص
        function populateStories() {
            const container = document.getElementById('storiesContainer');
            container.innerHTML = stories.map(story => `
                <div class="story-item" onclick="openStory('${story.name}')">
                    <div class="story-avatar ${story.hasStory ? 'has-story' : 'no-story'}">
                        <div class="story-inner">
                            ${story.isAdd ? '<i class="fas fa-plus" style="color: #227FCC;"></i>' : '<i class="fas fa-user"></i>'}
                        </div>
                    </div>
                    <div class="story-name">${story.name}</div>
                </div>
            `).join('');
        }

        // ملء البث المباشر
        function populateLiveStreams() {
            const container = document.getElementById('liveContainer');
            container.innerHTML = liveStreams.map(stream => `
                <div class="live-item" onclick="openLiveStream('${stream.name}')">
                    <div class="live-avatar ${stream.isLive ? 'is-live' : 'not-live'}">
                        <div class="story-inner">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="story-name">${stream.name}</div>
                </div>
            `).join('');
        }

        // ملء الإعلانات
        function populateAds() {
            const container = document.getElementById('adsContainer');
            const visibleAds = ads.slice(0, 4);
            container.innerHTML = visibleAds.map((ad, index) => `
                <div class="ad-item" onclick="watchAd(${index})">
                    ${ad}
                </div>
            `).join('');
        }

        // الوظائف
        function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            if (query) {
                // البحث في الخدمات
                const service = services.find(s =>
                    s.name.includes(query) ||
                    s.name.toLowerCase().includes(query.toLowerCase())
                );

                if (service) {
                    showNotification(`جاري فتح صفحة ${service.name}...`);
                    setTimeout(() => {
                        openServicePage(service.name);
                    }, 1000);
                } else {
                    showNotification('لم يتم العثور على الخدمة المطلوبة. جرب: مطاعم، صيدليات، ملابس، إلخ...');
                }
            } else {
                showNotification('يرجى كتابة اسم الخدمة المطلوبة');
            }
        }



        function scrollServices(direction) {
            const container = document.getElementById('servicesContainer');
            const scrollAmount = 300;
            if (direction === 'left') {
                container.scrollLeft -= scrollAmount;
            } else {
                container.scrollLeft += scrollAmount;
            }
        }

        function switchTab(tab) {
            // إزالة الفئة النشطة من جميع العناصر
            document.querySelectorAll('.nav-item').forEach(item => {
                if (!item.classList.contains('has-notification')) {
                    item.classList.remove('active');
                }
            });

            // إضافة الفئة النشطة للعنصر المحدد
            if (event && event.currentTarget && !event.currentTarget.classList.contains('has-notification')) {
                event.currentTarget.classList.add('active');
            }

            showNotification(`تم التبديل إلى ${getTabName(tab)}`);
        }

        function toggleMenu() {
            const dropdown = document.getElementById('menuDropdown');
            dropdown.classList.toggle('show');
        }

        function openNotifications() {
            document.getElementById('notificationsModal').style.display = 'block';
            populateNotifications();
        }

        function openMessages() {
            document.getElementById('messagesModal').style.display = 'block';
            populateConversations();
        }

        function openProfile() {
            // إنشاء صفحة الملف الشخصي مثل الفيسبوك
            const profilePageHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Hussein Nihad - Get Me</title>
                    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                    <style>
                        * { margin: 0; padding: 0; box-sizing: border-box; }
                        body { font-family: 'Cairo', sans-serif; background: #f0f2f5; direction: rtl; }

                        /* الشريط العلوي */
                        .top-bar {
                            background: white;
                            padding: 10px 20px;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            position: sticky;
                            top: 0;
                            z-index: 1000;
                        }

                        .back-btn {
                            background: #f0f2f5;
                            border: none;
                            border-radius: 50%;
                            width: 40px;
                            height: 40px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            color: #65676b;
                        }

                        .profile-title {
                            font-size: 20px;
                            font-weight: bold;
                            color: #1c1e21;
                        }

                        /* غلاف الملف الشخصي */
                        .profile-cover {
                            position: relative;
                            height: 350px;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            display: flex;
                            align-items: flex-end;
                            padding: 20px;
                        }

                        .cover-content {
                            display: flex;
                            align-items: flex-end;
                            gap: 20px;
                            width: 100%;
                            max-width: 1200px;
                            margin: 0 auto;
                        }

                        .profile-picture {
                            width: 170px;
                            height: 170px;
                            border-radius: 50%;
                            border: 5px solid white;
                            background: white;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 60px;
                            color: #227FCC;
                            position: relative;
                            margin-bottom: -85px;
                        }

                        .profile-info {
                            flex: 1;
                            color: white;
                            margin-bottom: 20px;
                        }

                        .profile-name {
                            font-size: 32px;
                            font-weight: bold;
                            margin-bottom: 8px;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                        }

                        .profile-stats {
                            display: flex;
                            gap: 20px;
                            font-size: 15px;
                            opacity: 0.9;
                        }

                        /* شريط التنقل */
                        .profile-nav {
                            background: white;
                            border-bottom: 1px solid #dadde1;
                            padding: 0 20px;
                            display: flex;
                            justify-content: center;
                        }

                        .nav-container {
                            display: flex;
                            max-width: 1200px;
                            width: 100%;
                            padding-right: 190px;
                        }

                        .nav-item {
                            padding: 15px 20px;
                            cursor: pointer;
                            border-bottom: 3px solid transparent;
                            color: #65676b;
                            font-weight: 600;
                            transition: all 0.3s;
                        }

                        .nav-item.active {
                            color: #1877f2;
                            border-bottom-color: #1877f2;
                        }

                        .nav-item:hover {
                            background: #f0f2f5;
                        }

                        /* المحتوى الرئيسي */
                        .main-content {
                            max-width: 1200px;
                            margin: 20px auto;
                            padding: 0 20px;
                            display: flex;
                            gap: 20px;
                        }

                        .left-column {
                            width: 360px;
                        }

                        .right-column {
                            flex: 1;
                        }

                        /* بطاقة المعلومات */
                        .info-card {
                            background: white;
                            border-radius: 8px;
                            padding: 20px;
                            margin-bottom: 20px;
                            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                        }

                        .card-title {
                            font-size: 20px;
                            font-weight: bold;
                            color: #1c1e21;
                            margin-bottom: 15px;
                        }

                        .info-item {
                            display: flex;
                            align-items: center;
                            gap: 12px;
                            padding: 8px 0;
                            color: #65676b;
                        }

                        .info-icon {
                            width: 20px;
                            color: #65676b;
                        }

                        /* منطقة إنشاء المنشور */
                        .create-post {
                            background: white;
                            border-radius: 8px;
                            padding: 15px;
                            margin-bottom: 20px;
                            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                        }

                        .create-post-top {
                            display: flex;
                            align-items: center;
                            gap: 12px;
                            margin-bottom: 15px;
                        }

                        .create-post-avatar {
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                        }

                        .create-post-input {
                            flex: 1;
                            background: #f0f2f5;
                            border: none;
                            border-radius: 25px;
                            padding: 12px 16px;
                            font-family: 'Cairo', sans-serif;
                            font-size: 16px;
                            cursor: pointer;
                        }

                        .create-post-actions {
                            display: flex;
                            justify-content: space-around;
                            border-top: 1px solid #dadde1;
                            padding-top: 10px;
                        }

                        .post-action {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            padding: 8px 12px;
                            border-radius: 6px;
                            cursor: pointer;
                            color: #65676b;
                            font-weight: 600;
                            transition: background 0.3s;
                        }

                        .post-action:hover {
                            background: #f0f2f5;
                        }

                        /* المنشورات */
                        .post {
                            background: white;
                            border-radius: 8px;
                            margin-bottom: 20px;
                            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                        }

                        .post-header {
                            padding: 15px 15px 0;
                            display: flex;
                            align-items: center;
                            gap: 12px;
                        }

                        .post-avatar {
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                        }

                        .post-info {
                            flex: 1;
                        }

                        .post-author {
                            font-weight: 600;
                            color: #1c1e21;
                            font-size: 15px;
                        }

                        .post-time {
                            color: #65676b;
                            font-size: 13px;
                        }

                        .post-menu {
                            color: #65676b;
                            cursor: pointer;
                            padding: 8px;
                            border-radius: 50%;
                            transition: background 0.3s;
                        }

                        .post-menu:hover {
                            background: #f0f2f5;
                        }

                        .post-content {
                            padding: 15px;
                            color: #1c1e21;
                            line-height: 1.5;
                        }

                        .post-image {
                            width: 100%;
                            max-height: 400px;
                            object-fit: cover;
                        }

                        .post-stats {
                            padding: 0 15px;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            color: #65676b;
                            font-size: 15px;
                            border-bottom: 1px solid #dadde1;
                            padding-bottom: 10px;
                        }

                        .post-actions {
                            display: flex;
                            justify-content: space-around;
                            padding: 8px 0;
                        }

                        .post-action-btn {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            padding: 8px 12px;
                            border-radius: 6px;
                            cursor: pointer;
                            color: #65676b;
                            font-weight: 600;
                            transition: background 0.3s;
                            border: none;
                            background: none;
                            font-family: 'Cairo', sans-serif;
                        }

                        .post-action-btn:hover {
                            background: #f0f2f5;
                        }

                        .post-action-btn.liked {
                            color: #1877f2;
                        }

                        /* تجاوبية */
                        @media (max-width: 768px) {
                            .main-content {
                                flex-direction: column;
                                padding: 0 10px;
                            }

                            .left-column {
                                width: 100%;
                            }

                            .nav-container {
                                padding-right: 0;
                            }

                            .profile-cover {
                                height: 250px;
                            }

                            .profile-picture {
                                width: 120px;
                                height: 120px;
                                margin-bottom: -60px;
                            }

                            .profile-name {
                                font-size: 24px;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="top-bar">
                        <button class="back-btn" onclick="window.close()">
                            <i class="fas fa-arrow-right"></i>
                        </button>
                        <div class="profile-title">Hussein Nihad</div>
                    </div>

                    <div class="profile-cover">
                        <div class="cover-content">
                            <div class="profile-picture">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="profile-info">
                                <div class="profile-name">Hussein Nihad</div>
                                <div class="profile-stats">
                                    <span>1,250 متابع</span>
                                    <span>•</span>
                                    <span>45 منشور</span>
                                    <span>•</span>
                                    <span>⭐ 4.8 تقييم</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="profile-nav">
                        <div class="nav-container">
                            <div class="nav-item active">المنشورات</div>
                            <div class="nav-item">معلومات</div>
                            <div class="nav-item">الأصدقاء</div>
                            <div class="nav-item">الصور</div>
                            <div class="nav-item">المزيد</div>
                        </div>
                    </div>

                    <div class="main-content">
                        <div class="left-column">
                            <div class="info-card">
                                <div class="card-title">معلومات</div>
                                <div class="info-item">
                                    <i class="fas fa-briefcase info-icon"></i>
                                    <span>بائع مميز في Get Me</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-map-marker-alt info-icon"></i>
                                    <span>بغداد، العراق</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-calendar-alt info-icon"></i>
                                    <span>انضم في يناير 2023</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-phone info-icon"></i>
                                    <span>+964 ************</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-envelope info-icon"></i>
                                    <span><EMAIL></span>
                                </div>
                            </div>

                            <div class="info-card">
                                <div class="card-title">الإحصائيات</div>
                                <div class="info-item">
                                    <i class="fas fa-eye info-icon"></i>
                                    <span>15,420 مشاهدة للملف الشخصي</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-heart info-icon"></i>
                                    <span>2,340 إعجاب إجمالي</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-comment info-icon"></i>
                                    <span>890 تعليق</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-share info-icon"></i>
                                    <span>156 مشاركة</span>
                                </div>
                            </div>
                        </div>

                        <div class="right-column">
                            <div class="create-post">
                                <div class="create-post-top">
                                    <div class="create-post-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <input type="text" class="create-post-input" placeholder="ماذا تريد أن تشارك؟" readonly>
                                </div>
                                <div class="create-post-actions">
                                    <div class="post-action">
                                        <i class="fas fa-video" style="color: #f02849;"></i>
                                        <span>بث مباشر</span>
                                    </div>
                                    <div class="post-action">
                                        <i class="fas fa-images" style="color: #45bd62;"></i>
                                        <span>صورة/فيديو</span>
                                    </div>
                                    <div class="post-action">
                                        <i class="fas fa-smile" style="color: #f7b928;"></i>
                                        <span>مشاعر/نشاط</span>
                                    </div>
                                </div>
                            </div>

                            <!-- المنشورات -->
                            <div class="post">
                                <div class="post-header">
                                    <div class="post-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="post-info">
                                        <div class="post-author">Hussein Nihad</div>
                                        <div class="post-time">منذ ساعتين • <i class="fas fa-globe-americas"></i></div>
                                    </div>
                                    <div class="post-menu">
                                        <i class="fas fa-ellipsis-h"></i>
                                    </div>
                                </div>
                                <div class="post-content">
                                    عرض خاص اليوم! خصم 30% على جميع المنتجات الإلكترونية. العرض محدود لمدة 24 ساعة فقط. تواصلوا معنا للحصول على أفضل الأسعار! 🔥📱💻
                                </div>
                                <img src="https://via.placeholder.com/600x300/16CCC8/FFFFFF?text=عرض+خاص" alt="عرض خاص" class="post-image">
                                <div class="post-stats">
                                    <div>
                                        <i class="fas fa-thumbs-up" style="color: #1877f2;"></i>
                                        <i class="fas fa-heart" style="color: #f33e58;"></i>
                                        89 إعجاب
                                    </div>
                                    <div>23 تعليق • 12 مشاركة</div>
                                </div>
                                <div class="post-actions">
                                    <button class="post-action-btn">
                                        <i class="fas fa-thumbs-up"></i>
                                        <span>إعجاب</span>
                                    </button>
                                    <button class="post-action-btn">
                                        <i class="fas fa-comment"></i>
                                        <span>تعليق</span>
                                    </button>
                                    <button class="post-action-btn">
                                        <i class="fas fa-share"></i>
                                        <span>مشاركة</span>
                                    </button>
                                </div>
                            </div>

                            <div class="post">
                                <div class="post-header">
                                    <div class="post-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="post-info">
                                        <div class="post-author">Hussein Nihad</div>
                                        <div class="post-time">منذ 5 ساعات • <i class="fas fa-globe-americas"></i></div>
                                    </div>
                                    <div class="post-menu">
                                        <i class="fas fa-ellipsis-h"></i>
                                    </div>
                                </div>
                                <div class="post-content">
                                    شكراً لجميع العملاء الكرام على ثقتكم بنا! وصلنا إلى 1000 عميل راضٍ عن خدماتنا. نعدكم بالاستمرار في تقديم أفضل الخدمات والمنتجات بأعلى جودة وأفضل الأسعار. 🙏✨
                                </div>
                                <div class="post-stats">
                                    <div>
                                        <i class="fas fa-thumbs-up" style="color: #1877f2;"></i>
                                        <i class="fas fa-heart" style="color: #f33e58;"></i>
                                        156 إعجاب
                                    </div>
                                    <div>45 تعليق • 8 مشاركات</div>
                                </div>
                                <div class="post-actions">
                                    <button class="post-action-btn liked">
                                        <i class="fas fa-thumbs-up"></i>
                                        <span>إعجاب</span>
                                    </button>
                                    <button class="post-action-btn">
                                        <i class="fas fa-comment"></i>
                                        <span>تعليق</span>
                                    </button>
                                    <button class="post-action-btn">
                                        <i class="fas fa-share"></i>
                                        <span>مشاركة</span>
                                    </button>
                                </div>
                            </div>

                            <div class="post">
                                <div class="post-header">
                                    <div class="post-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="post-info">
                                        <div class="post-author">Hussein Nihad</div>
                                        <div class="post-time">منذ يوم واحد • <i class="fas fa-globe-americas"></i></div>
                                    </div>
                                    <div class="post-menu">
                                        <i class="fas fa-ellipsis-h"></i>
                                    </div>
                                </div>
                                <div class="post-content">
                                    وصلت شحنة جديدة من أحدث الهواتف الذكية! 📱

                                    ✅ أحدث الموديلات
                                    ✅ ضمان سنة كاملة
                                    ✅ أسعار منافسة
                                    ✅ توصيل مجاني

                                    للاستفسار والطلب، تواصلوا معنا عبر الرسائل الخاصة.
                                </div>
                                <img src="https://via.placeholder.com/600x400/227FCC/FFFFFF?text=هواتف+ذكية+جديدة" alt="هواتف ذكية" class="post-image">
                                <div class="post-stats">
                                    <div>
                                        <i class="fas fa-thumbs-up" style="color: #1877f2;"></i>
                                        <i class="fas fa-heart" style="color: #f33e58;"></i>
                                        234 إعجاب
                                    </div>
                                    <div>67 تعليق • 25 مشاركة</div>
                                </div>
                                <div class="post-actions">
                                    <button class="post-action-btn">
                                        <i class="fas fa-thumbs-up"></i>
                                        <span>إعجاب</span>
                                    </button>
                                    <button class="post-action-btn">
                                        <i class="fas fa-comment"></i>
                                        <span>تعليق</span>
                                    </button>
                                    <button class="post-action-btn">
                                        <i class="fas fa-share"></i>
                                        <span>مشاركة</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `;

            const newWindow = window.open('', '_blank');
            newWindow.document.write(profilePageHTML);
            newWindow.document.close();
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function openServicePage(serviceName) {
            // إنشاء صفحة المنشورات للخدمة
            const servicePageHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>${serviceName} - Get Me</title>
                    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                    <style>
                        * { margin: 0; padding: 0; box-sizing: border-box; }
                        body { font-family: 'Cairo', sans-serif; background: #f0f2f5; direction: rtl; }

                        /* الشريط العلوي */
                        .service-header {
                            background: white;
                            padding: 15px 20px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            position: sticky;
                            top: 0;
                            z-index: 1000;
                        }

                        .back-btn {
                            background: none;
                            border: none;
                            font-size: 24px;
                            cursor: pointer;
                            color: #227FCC;
                            padding: 8px;
                            border-radius: 50%;
                            transition: background 0.3s;
                        }

                        .back-btn:hover {
                            background: #f5f5f5;
                        }

                        .service-title {
                            flex: 1;
                            font-size: 24px;
                            font-weight: bold;
                            color: #333;
                        }

                        .logo-small {
                            width: 40px;
                            height: 40px;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            border-radius: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                        }

                        /* شريط القصص */
                        .stories-section {
                            background: white;
                            padding: 15px 0;
                            margin-bottom: 10px;
                            position: relative;
                        }

                        .stories-container {
                            display: flex;
                            gap: 15px;
                            overflow-x: auto;
                            padding: 0 20px;
                            scroll-behavior: smooth;
                        }

                        .stories-container::-webkit-scrollbar {
                            display: none;
                        }

                        .story-item {
                            flex-shrink: 0;
                            text-align: center;
                            cursor: pointer;
                        }

                        .story-avatar {
                            width: 60px;
                            height: 60px;
                            border-radius: 50%;
                            margin: 0 auto 8px;
                            position: relative;
                            background: #f0f0f0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }

                        .story-avatar.has-story {
                            border: 3px solid #16CCC8;
                        }

                        .story-avatar.no-story {
                            border: 3px solid #ccc;
                        }

                        .story-avatar img {
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                            object-fit: cover;
                        }

                        .story-name {
                            font-size: 12px;
                            color: #333;
                            max-width: 70px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        /* شريط البث المباشر */
                        .live-section {
                            background: white;
                            padding: 15px 0;
                            margin-bottom: 10px;
                            position: relative;
                        }

                        .live-container {
                            display: flex;
                            gap: 15px;
                            overflow-x: auto;
                            padding: 0 20px;
                            scroll-behavior: smooth;
                        }

                        .live-container::-webkit-scrollbar {
                            display: none;
                        }

                        .live-item {
                            flex-shrink: 0;
                            text-align: center;
                            cursor: pointer;
                        }

                        .live-avatar {
                            width: 60px;
                            height: 60px;
                            border-radius: 50%;
                            margin: 0 auto 8px;
                            position: relative;
                            background: #f0f0f0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }

                        .live-avatar.is-live {
                            border: 3px solid;
                            border-image: linear-gradient(45deg, #ff0000, #ff8c00, #ffd700, #9932cc) 1;
                            animation: live-pulse 2s ease-in-out infinite;
                        }

                        .live-avatar.no-live {
                            border: 3px solid #ccc;
                        }

                        @keyframes live-pulse {
                            0%, 100% { transform: scale(1); }
                            50% { transform: scale(1.05); }
                        }

                        .live-avatar img {
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                            object-fit: cover;
                        }

                        .live-indicator {
                            position: absolute;
                            bottom: -2px;
                            right: -2px;
                            background: #ff0000;
                            color: white;
                            font-size: 8px;
                            padding: 2px 4px;
                            border-radius: 8px;
                            font-weight: bold;
                        }

                        /* منطقة إنشاء المنشور */
                        .create-post {
                            background: white;
                            border-radius: 8px;
                            padding: 15px;
                            margin: 0 20px 15px;
                            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                        }

                        .create-post-top {
                            display: flex;
                            align-items: center;
                            gap: 12px;
                            margin-bottom: 15px;
                        }

                        .create-post-avatar {
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                        }

                        .create-post-input {
                            flex: 1;
                            background: #f0f2f5;
                            border: none;
                            border-radius: 25px;
                            padding: 12px 16px;
                            font-family: 'Cairo', sans-serif;
                            font-size: 16px;
                            cursor: pointer;
                        }

                        .create-post-actions {
                            display: flex;
                            justify-content: space-around;
                            border-top: 1px solid #dadde1;
                            padding-top: 10px;
                        }

                        .post-action {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            padding: 8px 12px;
                            border-radius: 6px;
                            cursor: pointer;
                            color: #65676b;
                            font-weight: 600;
                            transition: background 0.3s;
                        }

                        .post-action:hover {
                            background: #f0f2f5;
                        }

                        /* المنشورات */
                        .posts-container {
                            max-width: 680px;
                            margin: 0 auto;
                            padding: 0 20px;
                        }

                        .post {
                            background: white;
                            border-radius: 8px;
                            margin-bottom: 20px;
                            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                        }

                        .post-header {
                            padding: 15px 15px 0;
                            display: flex;
                            align-items: center;
                            gap: 12px;
                        }

                        .post-avatar {
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                        }

                        .post-info {
                            flex: 1;
                        }

                        .post-author {
                            font-weight: 600;
                            color: #1c1e21;
                            font-size: 15px;
                        }

                        .post-time {
                            color: #65676b;
                            font-size: 13px;
                        }

                        .post-menu {
                            color: #65676b;
                            cursor: pointer;
                            padding: 8px;
                            border-radius: 50%;
                            transition: background 0.3s;
                        }

                        .post-menu:hover {
                            background: #f0f2f5;
                        }

                        .post-content {
                            padding: 15px;
                            color: #1c1e21;
                            line-height: 1.5;
                        }

                        .post-image {
                            width: 100%;
                            max-height: 400px;
                            object-fit: cover;
                        }

                        .post-stats {
                            padding: 0 15px;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            color: #65676b;
                            font-size: 15px;
                            border-bottom: 1px solid #dadde1;
                            padding-bottom: 10px;
                        }

                        .post-actions {
                            display: flex;
                            justify-content: space-around;
                            padding: 8px 0;
                        }

                        .post-action-btn {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            padding: 8px 12px;
                            border-radius: 6px;
                            cursor: pointer;
                            color: #65676b;
                            font-weight: 600;
                            transition: background 0.3s;
                            border: none;
                            background: none;
                            font-family: 'Cairo', sans-serif;
                        }

                        .post-action-btn:hover {
                            background: #f0f2f5;
                        }

                        .post-action-btn.liked {
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            background-clip: text;
                        }

                        /* نافذة طلب السعر */
                        .price-request-modal {
                            display: none;
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0,0,0,0.5);
                            z-index: 2000;
                        }

                        .price-request-content {
                            background: white;
                            width: 90%;
                            max-width: 400px;
                            margin: 50px auto;
                            border-radius: 15px;
                            padding: 20px;
                        }

                        .price-options {
                            display: flex;
                            flex-direction: column;
                            gap: 10px;
                        }

                        .price-option {
                            padding: 12px;
                            border: 1px solid #ddd;
                            border-radius: 8px;
                            cursor: pointer;
                            transition: all 0.3s;
                            text-align: center;
                        }

                        .price-option:hover {
                            background: #f0f2f5;
                            border-color: #16CCC8;
                        }

                        /* نافذة المشاركة */
                        .share-modal {
                            display: none;
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0,0,0,0.5);
                            z-index: 2000;
                        }

                        .share-content {
                            background: white;
                            width: 90%;
                            max-width: 400px;
                            margin: 50px auto;
                            border-radius: 15px;
                            padding: 20px;
                        }

                        .share-options {
                            display: grid;
                            grid-template-columns: repeat(3, 1fr);
                            gap: 15px;
                            margin-top: 15px;
                        }

                        .share-option {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            gap: 8px;
                            padding: 15px;
                            border-radius: 10px;
                            cursor: pointer;
                            transition: background 0.3s;
                        }

                        .share-option:hover {
                            background: #f0f2f5;
                        }

                        .share-icon {
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 18px;
                        }

                        .whatsapp { background: #25d366; }
                        .facebook { background: #1877f2; }
                        .instagram { background: #e4405f; }
                        .viber { background: #665cac; }
                        .message { background: #007aff; }
                        .profile { background: linear-gradient(135deg, #16CCC8, #227FCC); }
                    </style>
                </head>
                <body>
                    <div class="service-header">
                        <button class="back-btn" onclick="history.back()">
                            <i class="fas fa-arrow-right"></i>
                        </button>
                        <div class="service-title">${serviceName}</div>
                        <div class="logo-small">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                    </div>

                    <!-- شريط القصص -->
                    <div class="stories-section">
                        <div class="stories-container" id="storiesContainer">
                            <div class="story-item">
                                <div class="story-avatar has-story">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                </div>
                                <div class="story-name">أحمد محمد</div>
                            </div>
                            <div class="story-item">
                                <div class="story-avatar no-story">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                </div>
                                <div class="story-name">فاطمة علي</div>
                            </div>
                            <div class="story-item">
                                <div class="story-avatar has-story">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                </div>
                                <div class="story-name">محمد حسن</div>
                            </div>
                            <div class="story-item">
                                <div class="story-avatar no-story">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                </div>
                                <div class="story-name">سارة أحمد</div>
                            </div>
                            <div class="story-item">
                                <div class="story-avatar has-story">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                </div>
                                <div class="story-name">علي حسين</div>
                            </div>
                        </div>
                    </div>

                    <!-- شريط البث المباشر -->
                    <div class="live-section">
                        <div class="live-container" id="liveContainer">
                            <div class="live-item">
                                <div class="live-avatar is-live">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                    <div class="live-indicator">LIVE</div>
                                </div>
                                <div class="story-name">متجر الإلكترونيات</div>
                            </div>
                            <div class="live-item">
                                <div class="live-avatar no-live">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                </div>
                                <div class="story-name">مطعم الأصالة</div>
                            </div>
                            <div class="live-item">
                                <div class="live-avatar is-live">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                    <div class="live-indicator">LIVE</div>
                                </div>
                                <div class="story-name">صيدلية النور</div>
                            </div>
                            <div class="live-item">
                                <div class="live-avatar no-live">
                                    <i class="fas fa-user" style="color: #666;"></i>
                                </div>
                                <div class="story-name">معرض السيارات</div>
                            </div>
                        </div>
                    </div>

                    <div class="posts-container">
                        <!-- منطقة إنشاء المنشور -->
                        <div class="create-post">
                            <div class="create-post-top">
                                <div class="create-post-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <input type="text" class="create-post-input" placeholder="ماذا تريد أن تشارك في ${serviceName}؟" onclick="openCreatePost()">
                            </div>
                            <div class="create-post-actions">
                                <div class="post-action" onclick="openCreatePost('photo')">
                                    <i class="fas fa-images" style="color: #45bd62;"></i>
                                    <span>صورة/فيديو</span>
                                </div>
                                <div class="post-action" onclick="openCreatePost('location')">
                                    <i class="fas fa-map-marker-alt" style="color: #f02849;"></i>
                                    <span>موقع</span>
                                </div>
                                <div class="post-action" onclick="openCreatePost('offer')">
                                    <i class="fas fa-tag" style="color: #f7b928;"></i>
                                    <span>عرض خاص</span>
                                </div>
                            </div>
                        </div>

                        <!-- المنشورات -->
                        <div class="post">
                            <div class="post-header">
                                <div class="post-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="post-info">
                                    <div class="post-author">شركة العقارات المميزة</div>
                                    <div class="post-time">منذ ساعتين • <i class="fas fa-globe-americas"></i></div>
                                </div>
                                <div class="post-menu">
                                    <i class="fas fa-ellipsis-h"></i>
                                </div>
                            </div>
                            <div class="post-content">
                                🏠 شقة للبيع في منطقة الكرادة

                                📍 الموقع: الكرادة - شارع الرئيسي
                                🏢 الطابق: الثالث
                                🛏️ غرف النوم: 3
                                🚿 الحمامات: 2
                                📐 المساحة: 120 متر مربع
                                🚗 كراج خاص

                                ✨ مواصفات مميزة:
                                - تشطيب حديث
                                - إطلالة رائعة
                                - قريب من الخدمات

                                للاستفسار والمعاينة تواصل معنا
                            </div>
                            <img src="https://via.placeholder.com/600x300/16CCC8/FFFFFF?text=شقة+للبيع" alt="شقة للبيع" class="post-image">
                            <div class="post-stats">
                                <div>
                                    <i class="fas fa-heart" style="color: #16CCC8;"></i>
                                    45 إعجاب
                                </div>
                                <div>12 طلب سعر • 5 مشاركات</div>
                            </div>
                            <div class="post-actions">
                                <button class="post-action-btn" onclick="toggleLike(this)">
                                    <i class="fas fa-heart"></i>
                                    <span>إعجاب</span>
                                </button>
                                <button class="post-action-btn" onclick="requestPrice()">
                                    <i class="fas fa-comment-dollar"></i>
                                    <span>طلب السعر</span>
                                </button>
                                <button class="post-action-btn" onclick="sharePost()">
                                    <i class="fas fa-share"></i>
                                    <span>مشاركة</span>
                                </button>
                            </div>
                        </div>

                        <div class="post">
                            <div class="post-header">
                                <div class="post-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="post-info">
                                    <div class="post-author">مكتب الأحلام العقاري</div>
                                    <div class="post-time">منذ 4 ساعات • <i class="fas fa-globe-americas"></i></div>
                                </div>
                                <div class="post-menu">
                                    <i class="fas fa-ellipsis-h"></i>
                                </div>
                            </div>
                            <div class="post-content">
                                🏡 فيلا فاخرة للإيجار

                                📍 حي الجادرية المميز
                                🏠 مساحة الأرض: 300 متر
                                🏢 مساحة البناء: 250 متر
                                🛏️ 4 غرف نوم رئيسية
                                🚿 3 حمامات
                                🍽️ صالة استقبال واسعة
                                🌳 حديقة خاصة
                                🚗 كراج لسيارتين

                                💎 مواصفات VIP:
                                - تكييف مركزي
                                - نظام أمان متطور
                                - تشطيب سوبر ديلوكس

                                #فيلا_للإيجار #الجادرية #عقارات_فاخرة
                            </div>
                            <img src="https://via.placeholder.com/600x400/227FCC/FFFFFF?text=فيلا+فاخرة" alt="فيلا فاخرة" class="post-image">
                            <div class="post-stats">
                                <div>
                                    <i class="fas fa-heart" style="color: #16CCC8;"></i>
                                    78 إعجاب
                                </div>
                                <div>23 طلب سعر • 8 مشاركات</div>
                            </div>
                            <div class="post-actions">
                                <button class="post-action-btn liked" onclick="toggleLike(this)">
                                    <i class="fas fa-heart"></i>
                                    <span>إعجاب</span>
                                </button>
                                <button class="post-action-btn" onclick="requestPrice()">
                                    <i class="fas fa-comment-dollar"></i>
                                    <span>طلب السعر</span>
                                </button>
                                <button class="post-action-btn" onclick="sharePost()">
                                    <i class="fas fa-share"></i>
                                    <span>مشاركة</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- نافذة طلب السعر -->
                    <div class="price-request-modal" id="priceRequestModal">
                        <div class="price-request-content">
                            <h3 style="text-align: center; margin-bottom: 20px;">طلب السعر</h3>
                            <div class="price-options">
                                <div class="price-option" onclick="sendPriceRequest('ما هو سعر هذا العقار؟')">
                                    ما هو سعر هذا العقار؟
                                </div>
                                <div class="price-option" onclick="sendPriceRequest('هل يمكن التفاوض على السعر؟')">
                                    هل يمكن التفاوض على السعر؟
                                </div>
                                <div class="price-option" onclick="sendPriceRequest('ما هي شروط الدفع؟')">
                                    ما هي شروط الدفع؟
                                </div>
                                <div class="price-option" onclick="sendPriceRequest('هل يشمل السعر الخدمات؟')">
                                    هل يشمل السعر الخدمات؟
                                </div>
                                <div class="price-option" onclick="sendPriceRequest('أريد معاينة العقار')">
                                    أريد معاينة العقار
                                </div>
                            </div>
                            <button onclick="closePriceRequest()" style="width: 100%; margin-top: 15px; padding: 10px; background: #ccc; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                        </div>
                    </div>

                    <!-- نافذة المشاركة -->
                    <div class="share-modal" id="shareModal">
                        <div class="share-content">
                            <h3 style="text-align: center; margin-bottom: 20px;">مشاركة المنشور</h3>
                            <div class="share-options">
                                <div class="share-option" onclick="shareToApp('whatsapp')">
                                    <div class="share-icon whatsapp">
                                        <i class="fab fa-whatsapp"></i>
                                    </div>
                                    <span>واتساب</span>
                                </div>
                                <div class="share-option" onclick="shareToApp('facebook')">
                                    <div class="share-icon facebook">
                                        <i class="fab fa-facebook-f"></i>
                                    </div>
                                    <span>فيسبوك</span>
                                </div>
                                <div class="share-option" onclick="shareToApp('instagram')">
                                    <div class="share-icon instagram">
                                        <i class="fab fa-instagram"></i>
                                    </div>
                                    <span>انستجرام</span>
                                </div>
                                <div class="share-option" onclick="shareToApp('viber')">
                                    <div class="share-icon viber">
                                        <i class="fab fa-viber"></i>
                                    </div>
                                    <span>فايبر</span>
                                </div>
                                <div class="share-option" onclick="shareToApp('message')">
                                    <div class="share-icon message">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <span>رسالة خاصة</span>
                                </div>
                                <div class="share-option" onclick="shareToApp('profile')">
                                    <div class="share-icon profile">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <span>الملف الشخصي</span>
                                </div>
                            </div>
                            <button onclick="closeShareModal()" style="width: 100%; margin-top: 15px; padding: 10px; background: #ccc; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                        </div>
                    </div>

                    <script>
                        // تبديل الإعجاب
                        function toggleLike(button) {
                            button.classList.toggle('liked');
                            const icon = button.querySelector('i');
                            if (button.classList.contains('liked')) {
                                icon.style.color = '#16CCC8';
                            } else {
                                icon.style.color = '#65676b';
                            }
                        }

                        // طلب السعر
                        function requestPrice() {
                            document.getElementById('priceRequestModal').style.display = 'block';
                        }

                        function closePriceRequest() {
                            document.getElementById('priceRequestModal').style.display = 'none';
                        }

                        function sendPriceRequest(message) {
                            alert('تم إرسال طلبك: "' + message + '"\\n\\nتم إجابتك على طلبك في رسالة خاصة');
                            closePriceRequest();
                        }

                        // مشاركة المنشور
                        function sharePost() {
                            document.getElementById('shareModal').style.display = 'block';
                        }

                        function closeShareModal() {
                            document.getElementById('shareModal').style.display = 'none';
                        }

                        function shareToApp(app) {
                            const appNames = {
                                'whatsapp': 'واتساب',
                                'facebook': 'فيسبوك',
                                'instagram': 'انستجرام',
                                'viber': 'فايبر',
                                'message': 'رسالة خاصة',
                                'profile': 'الملف الشخصي'
                            };
                            alert('تم مشاركة المنشور عبر ' + appNames[app]);
                            closeShareModal();
                        }

                        // إنشاء منشور جديد
                        function openCreatePost(type = '') {
                            let message = 'فتح نافذة إنشاء منشور جديد';
                            if (type === 'photo') message += ' - إضافة صورة/فيديو';
                            else if (type === 'location') message += ' - إضافة موقع';
                            else if (type === 'offer') message += ' - إنشاء عرض خاص';

                            alert(message + '\\n\\nهذه الميزة متاحة للبائعين فقط');
                        }

                        // إغلاق النوافذ عند النقر خارجها
                        window.onclick = function(event) {
                            const priceModal = document.getElementById('priceRequestModal');
                            const shareModal = document.getElementById('shareModal');

                            if (event.target === priceModal) {
                                priceModal.style.display = 'none';
                            }
                            if (event.target === shareModal) {
                                shareModal.style.display = 'none';
                            }
                        }
                    </script>
                </body>
                </html>
            `;

            // فتح الصفحة في نافذة جديدة
            const newWindow = window.open('', '_blank');
            newWindow.document.write(servicePageHTML);
            newWindow.document.close();
        }

        function openStory(userName) {
            showNotification(`فتح قصة ${userName}`);
        }

        function openLiveStream(userName) {
            showNotification(`فتح بث ${userName} المباشر`);
        }

        function watchAd(index) {
            showNotification(`مشاهدة الإعلان رقم ${index + 1} - تم إضافة نقاط إلى حسابك`);
        }

        // تدوير الإعلانات كل دقيقة
        function startAdRotation() {
            setInterval(() => {
                const firstAd = ads.shift();
                ads.push(firstAd);
                populateAds();
            }, 60000);
        }

        // وظائف مساعدة
        function getTabName(tab) {
            const names = {
                'home': 'الرئيسية',
                'profile': 'الملف الشخصي'
            };
            return names[tab] || tab;
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: linear-gradient(135deg, #16CCC8, #227FCC);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 5px 20px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: 'Cairo', sans-serif;
                max-width: 300px;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // إغلاق القوائم عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.menu-dropdown')) {
                document.getElementById('menuDropdown').classList.remove('show');
            }

            if (!event.target.closest('.modal-menu')) {
                const dropdown = document.getElementById('notificationMenuDropdown');
                if (dropdown) {
                    dropdown.classList.remove('show');
                }
            }
        });
    </script>
</body>
</html>
