import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import '../models/subscription_plan.dart';

class IdentityVerificationScreen extends StatefulWidget {
  final UserRegistrationData registrationData;
  final VoidCallback onNext;
  final VoidCallback onBack;

  const IdentityVerificationScreen({
    super.key,
    required this.registrationData,
    required this.onNext,
    required this.onBack,
  });

  @override
  State<IdentityVerificationScreen> createState() => _IdentityVerificationScreenState();
}

class _IdentityVerificationScreenState extends State<IdentityVerificationScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  bool _isImageCaptured = false;
  bool _isVerifying = false;
  bool _isVerified = false;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 32),
              Expanded(
                child: _buildCameraSection(),
              ),
              _buildButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    widget.registrationData.selectedPlan?.primaryColor ??
                        const Color(0xFF667eea),
                    widget.registrationData.selectedPlan?.secondaryColor ??
                        const Color(0xFF764ba2),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.verified_user,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'التحقق من الهوية',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1C1E21),
                    ),
                  ),
                  Text(
                    'تصوير الهوية المدنية',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          'يرجى تصوير الهوية المدنية بوضوح للتحقق من هويتك',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }

  Widget _buildCameraSection() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Camera Preview Area
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isImageCaptured ? 1.0 : _pulseAnimation.value,
                child: Container(
                  width: 300,
                  height: 200,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _isVerified
                          ? Colors.green
                          : _isImageCaptured
                              ? Colors.orange
                              : Colors.grey,
                      width: 3,
                    ),
                    color: Colors.grey[100],
                  ),
                  child: _buildCameraContent(),
                ),
              );
            },
          ),
          const SizedBox(height: 32),
          
          // Status Text
          Text(
            _getStatusText(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: _isVerified
                  ? Colors.green
                  : _isImageCaptured
                      ? Colors.orange
                      : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          
          // Capture Button
          if (!_isImageCaptured)
            GFButton(
              onPressed: _captureImage,
              text: 'التقط صورة الهوية',
              icon: const Icon(Icons.camera_alt, color: Colors.white),
              shape: GFButtonShape.pills,
              size: GFSize.LARGE,
              color: widget.registrationData.selectedPlan?.primaryColor ??
                  const Color(0xFF667eea),
            ),
          
          // Verify Button
          if (_isImageCaptured && !_isVerified)
            Column(
              children: [
                GFButton(
                  onPressed: _isVerifying ? null : _verifyImage,
                  text: _isVerifying ? 'جاري التحقق...' : 'تحقق من الصورة',
                  icon: _isVerifying
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Icon(Icons.check_circle, color: Colors.white),
                  shape: GFButtonShape.pills,
                  size: GFSize.LARGE,
                  color: Colors.orange,
                ),
                const SizedBox(height: 16),
                GFButton(
                  onPressed: _retakeImage,
                  text: 'إعادة التقاط',
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  shape: GFButtonShape.pills,
                  size: GFSize.MEDIUM,
                  color: Colors.grey,
                ),
              ],
            ),
          
          // Success Animation
          if (_isVerified)
            Column(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'تم التحقق بنجاح!',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildCameraContent() {
    if (_isVerified) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.verified_user,
              size: 60,
              color: Colors.green,
            ),
            SizedBox(height: 8),
            Text(
              'تم التحقق',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
      );
    }
    
    if (_isImageCaptured) {
      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(17),
          color: Colors.grey[300],
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.credit_card,
                size: 60,
                color: Colors.grey,
              ),
              SizedBox(height: 8),
              Text(
                'صورة الهوية',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.camera_alt,
            size: 60,
            color: Colors.grey,
          ),
          SizedBox(height: 8),
          Text(
            'اضغط لالتقاط صورة الهوية',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: widget.onBack,
            style: OutlinedButton.styleFrom(
              foregroundColor: widget.registrationData.selectedPlan?.primaryColor ??
                  const Color(0xFF667eea),
              side: BorderSide(
                color: widget.registrationData.selectedPlan?.primaryColor ??
                    const Color(0xFF667eea),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'رجوع',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isVerified ? _handleContinue : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _isVerified
                  ? (widget.registrationData.selectedPlan?.primaryColor ??
                      const Color(0xFF667eea))
                  : Colors.grey,
              foregroundColor: Colors.white,
              elevation: _isVerified ? 8 : 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'متابعة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _getStatusText() {
    if (_isVerified) {
      return 'تم التحقق من الهوية بنجاح';
    } else if (_isVerifying) {
      return 'جاري التحقق من الصورة...';
    } else if (_isImageCaptured) {
      return 'تم التقاط الصورة - يرجى التحقق';
    } else {
      return 'يرجى التقاط صورة واضحة للهوية المدنية';
    }
  }

  void _captureImage() {
    setState(() {
      _isImageCaptured = true;
    });
    _pulseController.stop();
  }

  void _retakeImage() {
    setState(() {
      _isImageCaptured = false;
      _isVerifying = false;
      _isVerified = false;
    });
    _pulseController.repeat(reverse: true);
  }

  Future<void> _verifyImage() async {
    setState(() {
      _isVerifying = true;
    });

    // Simulate verification process
    await Future.delayed(const Duration(seconds: 3));

    setState(() {
      _isVerifying = false;
      _isVerified = true;
    });

    // Save verification status
    widget.registrationData.isIdentityVerified = true;
    widget.registrationData.identityImagePath = 'captured_identity.jpg';
  }

  void _handleContinue() {
    if (_isVerified) {
      widget.onNext();
    }
  }
}
