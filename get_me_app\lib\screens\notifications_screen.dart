import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final List<NotificationData> _notifications = _getDemoNotifications();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: Text(
          'الإشعارات',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.done_all, color: Colors.black87),
            onPressed: () {
              _markAllAsRead();
            },
          ),
        ],
      ),
      body: ListView.builder(
        itemCount: _notifications.length,
        itemBuilder: (context, index) {
          final notification = _notifications[index];
          return _buildNotificationItem(notification, index);
        },
      ),
    );
  }

  Widget _buildNotificationItem(NotificationData notification, int index) {
    return Container(
      color: notification.isRead ? Colors.white : const Color(0xFFF3F8FF),
      margin: const EdgeInsets.only(bottom: 1),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        leading: Stack(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: notification.type.color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                notification.type.icon,
                color: notification.type.color,
                size: 24,
              ),
            ),
            if (!notification.isRead)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: 12,
                  height: 12,
                  decoration: const BoxDecoration(
                    color: Color(0xFF2196F3),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
          ],
        ),
        title: Text(
          notification.title,
          style: TextStyle(
            fontSize: 15,
            fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              notification.message,
              style: const TextStyle(
                fontSize: 13,
                color: Colors.grey,
                height: 1.3,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              notification.time,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        onTap: () {
          _markAsRead(index);
          _handleNotificationTap(notification);
        },
      ),
    );
  }

  void _markAsRead(int index) {
    setState(() {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
    });
  }

  void _markAllAsRead() {
    setState(() {
      for (int i = 0; i < _notifications.length; i++) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم وضع علامة مقروء على جميع الإشعارات',
          style: const TextStyle(),
        ),
        backgroundColor: const Color(0xFF4CAF50),
      ),
    );
  }

  void _handleNotificationTap(NotificationData notification) {
    // TODO: Navigate based on notification type
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم النقر على: ${notification.title}',
          style: const TextStyle(),
        ),
        backgroundColor: notification.type.color,
      ),
    );
  }

  static List<NotificationData> _getDemoNotifications() {
    return [
      NotificationData(
        title: 'إعجاب جديد',
        message: 'أعجب أحمد محمد بمنشورك حول الهواتف الذكية',
        time: 'منذ 5 دقائق',
        type: NotificationType.like,
        isRead: false,
      ),
      NotificationData(
        title: 'تعليق جديد',
        message: 'علقت فاطمة أحمد على منشورك: "هل المنتج متوفر؟"',
        time: 'منذ 15 دقيقة',
        type: NotificationType.comment,
        isRead: false,
      ),
      NotificationData(
        title: 'رسالة جديدة',
        message: 'أرسل لك محمد علي رسالة جديدة',
        time: 'منذ 30 دقيقة',
        type: NotificationType.message,
        isRead: false,
      ),
      NotificationData(
        title: 'طلب جديد',
        message: 'طلب جديد من سارة حسن لشراء منتجك',
        time: 'منذ ساعة',
        type: NotificationType.order,
        isRead: true,
      ),
      NotificationData(
        title: 'متابع جديد',
        message: 'بدأ عبدالله أحمد بمتابعتك',
        time: 'منذ ساعتين',
        type: NotificationType.follow,
        isRead: true,
      ),
      NotificationData(
        title: 'عرض مميز',
        message: 'عرض خاص لك! خصم 20% على الاشتراك المميز',
        time: 'منذ 3 ساعات',
        type: NotificationType.promotion,
        isRead: true,
      ),
      NotificationData(
        title: 'تحديث التطبيق',
        message: 'إصدار جديد من التطبيق متوفر الآن مع مميزات جديدة',
        time: 'أمس',
        type: NotificationType.update,
        isRead: true,
      ),
    ];
  }
}

class NotificationData {
  final String title;
  final String message;
  final String time;
  final NotificationType type;
  final bool isRead;

  NotificationData({
    required this.title,
    required this.message,
    required this.time,
    required this.type,
    this.isRead = false,
  });

  NotificationData copyWith({
    String? title,
    String? message,
    String? time,
    NotificationType? type,
    bool? isRead,
  }) {
    return NotificationData(
      title: title ?? this.title,
      message: message ?? this.message,
      time: time ?? this.time,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
    );
  }
}

enum NotificationType {
  like,
  comment,
  message,
  order,
  follow,
  promotion,
  update,
}

extension NotificationTypeExtension on NotificationType {
  IconData get icon {
    switch (this) {
      case NotificationType.like:
        return Icons.favorite;
      case NotificationType.comment:
        return Icons.chat_bubble;
      case NotificationType.message:
        return Icons.mail;
      case NotificationType.order:
        return Icons.shopping_cart;
      case NotificationType.follow:
        return Icons.person_add;
      case NotificationType.promotion:
        return Icons.local_offer;
      case NotificationType.update:
        return Icons.system_update;
    }
  }

  Color get color {
    switch (this) {
      case NotificationType.like:
        return const Color(0xFFF44336);
      case NotificationType.comment:
        return const Color(0xFF2196F3);
      case NotificationType.message:
        return const Color(0xFF4CAF50);
      case NotificationType.order:
        return const Color(0xFFFF9800);
      case NotificationType.follow:
        return const Color(0xFF9C27B0);
      case NotificationType.promotion:
        return const Color(0xFFE91E63);
      case NotificationType.update:
        return const Color(0xFF607D8B);
    }
  }
}
