import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/booking_request.dart';

class BookingRequestsWidget extends StatefulWidget {
  const BookingRequestsWidget({super.key});

  @override
  State<BookingRequestsWidget> createState() => _BookingRequestsWidgetState();
}

class _BookingRequestsWidgetState extends State<BookingRequestsWidget> {
  final List<BookingRequest> _requests = BookingRequest.getDemoRequests();
  BookingStatus? _selectedStatus;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildStatusFilter(),
          const SizedBox(height: 16),
          _buildRequestsList(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Row(
          children: [
            Icon(Icons.list_alt, color: Color(0xFF667eea), size: 24),
            SizedBox(width: 8),
            Text(
              'طلبات الحجز',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_getFilteredRequests().length}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _exportRequests,
              icon: const Icon(Icons.print, size: 20),
              tooltip: 'طباعة القائمة',
            ),
            IconButton(
              onPressed: _saveRequests,
              icon: const Icon(Icons.save, size: 20),
              tooltip: 'حفظ القائمة',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusFilter() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip('الكل', null),
          const SizedBox(width: 8),
          _buildFilterChip('في الانتظار', BookingStatus.pending),
          const SizedBox(width: 8),
          _buildFilterChip('مؤكد', BookingStatus.confirmed),
          const SizedBox(width: 8),
          _buildFilterChip('مرفوض', BookingStatus.rejected),
          const SizedBox(width: 8),
          _buildFilterChip('مكتمل', BookingStatus.completed),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, BookingStatus? status) {
    final isSelected = _selectedStatus == status;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedStatus = selected ? status : null;
        });
      },
      selectedColor: const Color(0xFF667eea).withOpacity(0.2),
      checkmarkColor: const Color(0xFF667eea),
      labelStyle: TextStyle(
        color: isSelected ? const Color(0xFF667eea) : Colors.grey[600],
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
      backgroundColor: Colors.grey[100],
      side: BorderSide(
        color: isSelected ? const Color(0xFF667eea) : Colors.grey[300]!,
      ),
    );
  }

  Widget _buildRequestsList() {
    final filteredRequests = _getFilteredRequests();

    if (filteredRequests.isEmpty) {
      return Container(
        height: 200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.inbox,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد طلبات حجز',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: filteredRequests.map((request) => _buildRequestItem(request)).toList(),
    );
  }

  Widget _buildRequestItem(BookingRequest request) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: request.status.color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس الطلب
          Row(
            children: [
              // صورة العميل
              CircleAvatar(
                radius: 20,
                backgroundImage: AssetImage(request.customerProfileImage),
                onBackgroundImageError: (exception, stackTrace) {
                  // في حالة عدم وجود الصورة
                },
                child: request.customerProfileImage.isEmpty
                    ? const Icon(Icons.person, size: 20)
                    : null,
              ),
              const SizedBox(width: 12),
              
              // معلومات العميل
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      request.customerName,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      request.customerPhone,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              
              // حالة الطلب
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: request.status.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      request.status.icon,
                      size: 12,
                      color: request.status.color,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      request.status.displayName,
                      style: TextStyle(
                        fontSize: 10,
                        color: request.status.color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // عنوان المنشور
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF667eea).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.business,
                  size: 16,
                  color: Color(0xFF667eea),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    request.postTitle,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF667eea),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          
          // رسالة العميل
          if (request.message.isNotEmpty) ...[
            Text(
              'الرسالة:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              request.message,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                height: 1.3,
              ),
            ),
            const SizedBox(height: 8),
          ],
          
          // السعر المعروض والتاريخ
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (request.offeredPrice != null)
                Text(
                  'السعر المعروض: ${request.offeredPrice!.toStringAsFixed(0)} د.ع',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.green,
                  ),
                ),
              Text(
                _formatDate(request.requestDate),
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // أزرار الإجراءات
          _buildActionButtons(request),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BookingRequest request) {
    return Row(
      children: [
        // زر الاتصال
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _callCustomer(request.customerPhone),
            icon: const Icon(Icons.phone, size: 16),
            label: const Text('اتصال'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        
        // زر الرسائل
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _messageCustomer(request),
            icon: const Icon(Icons.message, size: 16),
            label: const Text('رسالة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF667eea),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        
        // زر تغيير الحالة
        if (request.status == BookingStatus.pending)
          Expanded(
            child: PopupMenuButton<BookingStatus>(
              onSelected: (status) => _updateRequestStatus(request, status),
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: BookingStatus.confirmed,
                  child: Row(
                    children: [
                      Icon(BookingStatus.confirmed.icon, size: 16, color: BookingStatus.confirmed.color),
                      const SizedBox(width: 8),
                      Text(BookingStatus.confirmed.displayName),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: BookingStatus.rejected,
                  child: Row(
                    children: [
                      Icon(BookingStatus.rejected.icon, size: 16, color: BookingStatus.rejected.color),
                      const SizedBox(width: 8),
                      Text(BookingStatus.rejected.displayName),
                    ],
                  ),
                ),
              ],
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.edit, size: 16, color: Colors.white),
                    SizedBox(width: 4),
                    Text(
                      'تحديث',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  List<BookingRequest> _getFilteredRequests() {
    if (_selectedStatus == null) {
      return _requests;
    }
    return _requests.where((request) => request.status == _selectedStatus).toList();
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _callCustomer(String phoneNumber) {
    // نسخ رقم الهاتف إلى الحافظة
    Clipboard.setData(ClipboardData(text: phoneNumber));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم نسخ رقم الهاتف: $phoneNumber'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _messageCustomer(BookingRequest request) {
    // فتح شاشة الرسائل مع العميل
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح محادثة مع ${request.customerName}'),
        backgroundColor: const Color(0xFF667eea),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _updateRequestStatus(BookingRequest request, BookingStatus newStatus) {
    setState(() {
      final index = _requests.indexWhere((r) => r.id == request.id);
      if (index != -1) {
        _requests[index] = BookingRequest(
          id: request.id,
          postId: request.postId,
          postTitle: request.postTitle,
          customerName: request.customerName,
          customerPhone: request.customerPhone,
          customerEmail: request.customerEmail,
          customerProfileImage: request.customerProfileImage,
          message: request.message,
          requestDate: request.requestDate,
          status: newStatus,
          offeredPrice: request.offeredPrice,
        );
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحديث حالة طلب ${request.customerName} إلى ${newStatus.displayName}'),
        backgroundColor: newStatus.color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _exportRequests() {
    // تنفيذ طباعة القائمة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تحضير القائمة للطباعة...'),
        backgroundColor: Color(0xFF667eea),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _saveRequests() {
    // تنفيذ حفظ القائمة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ القائمة بنجاح'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
