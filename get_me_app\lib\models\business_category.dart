import 'package:flutter/material.dart';

class BusinessCategory {
  final String id;
  final String nameAr;
  final String nameEn;
  final String nameKu;
  final String imagePath;
  final Color color;
  final IconData icon;

  BusinessCategory({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.nameKu,
    required this.imagePath,
    required this.color,
    required this.icon,
  });

  String getName(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return nameAr;
      case 'ku':
        return nameKu;
      default:
        return nameEn;
    }
  }

  static List<BusinessCategory> getAllCategories() {
    return [
      BusinessCategory(
        id: 'restaurants',
        nameAr: 'مطاعم',
        nameEn: 'Restaurants',
        nameKu: 'چێشتخانەکان',
        imagePath: 'assets/images/مطعم.jpg',
        color: const Color(0xFFFF9800),
        icon: Icons.restaurant,
      ),
      BusinessCategory(
        id: 'pharmacies',
        nameAr: 'صيدليات',
        nameEn: 'Pharmacies',
        nameKu: 'دەرمانخانەکان',
        imagePath: 'assets/images/طبية.jpg',
        color: const Color(0xFF2196F3),
        icon: Icons.local_pharmacy,
      ),
      BusinessCategory(
        id: 'beauty',
        nameAr: 'مراكز تجميل',
        nameEn: 'Beauty Centers',
        nameKu: 'ناوەندەکانی جوانکاری',
        imagePath: 'assets/images/مستحظرات تجميل.jpg',
        color: const Color(0xFFE91E63),
        icon: Icons.face,
      ),
      BusinessCategory(
        id: 'clothing',
        nameAr: 'ملابس',
        nameEn: 'Clothing',
        nameKu: 'جلوبەرگ',
        imagePath: 'assets/images/محلات.jpg',
        color: const Color(0xFF9C27B0),
        icon: Icons.checkroom,
      ),
      BusinessCategory(
        id: 'electronics',
        nameAr: 'إلكترونيات',
        nameEn: 'Electronics',
        nameKu: 'ئەلیکترۆنیات',
        imagePath: 'assets/images/pp.png',
        color: const Color(0xFF4CAF50),
        icon: Icons.devices,
      ),
      BusinessCategory(
        id: 'doctors',
        nameAr: 'أطباء',
        nameEn: 'Doctors',
        nameKu: 'پزیشکان',
        imagePath: 'assets/images/الطبية.jpg',
        color: const Color(0xFFF44336),
        icon: Icons.medical_services,
      ),
      BusinessCategory(
        id: 'education',
        nameAr: 'تعليم',
        nameEn: 'Education',
        nameKu: 'پەروەردە',
        imagePath: 'assets/images/تعليم.jpg',
        color: const Color(0xFF3F51B5),
        icon: Icons.school,
      ),
      BusinessCategory(
        id: 'cars',
        nameAr: 'سيارات',
        nameEn: 'Cars',
        nameKu: 'ئۆتۆمبێل',
        imagePath: 'assets/images/سيارات.jpg',
        color: const Color(0xFF607D8B),
        icon: Icons.directions_car,
      ),
      BusinessCategory(
        id: 'real_estate',
        nameAr: 'عقارات',
        nameEn: 'Real Estate',
        nameKu: 'خانووبەرە',
        imagePath: 'assets/images/عقارات.jpg',
        color: const Color(0xFF795548),
        icon: Icons.home,
      ),
      BusinessCategory(
        id: 'sports',
        nameAr: 'رياضة',
        nameEn: 'Sports',
        nameKu: 'وەرزش',
        imagePath: 'assets/images/pp.png',
        color: const Color(0xFFFF5722),
        icon: Icons.sports_soccer,
      ),
      BusinessCategory(
        id: 'travel',
        nameAr: 'سفر وسياحة',
        nameEn: 'Travel & Tourism',
        nameKu: 'گەشت و گەشتیاری',
        imagePath: 'assets/images/شركات السياحية.jpg',
        color: const Color(0xFF00BCD4),
        icon: Icons.flight,
      ),
      BusinessCategory(
        id: 'jewelry',
        nameAr: 'مجوهرات',
        nameEn: 'Jewelry',
        nameKu: 'زێڕەوزیو',
        imagePath: 'assets/images/ذهب.jpg',
        color: const Color(0xFFFFD700),
        icon: Icons.diamond,
      ),
      BusinessCategory(
        id: 'furniture',
        nameAr: 'أثاث',
        nameEn: 'Furniture',
        nameKu: 'کەلوپەل',
        imagePath: 'assets/images/pp.png',
        color: const Color(0xFF8BC34A),
        icon: Icons.chair,
      ),
      BusinessCategory(
        id: 'books',
        nameAr: 'كتب ومكتبات',
        nameEn: 'Books & Libraries',
        nameKu: 'کتێب و کتێبخانە',
        imagePath: 'assets/images/قرطاسية.jpg',
        color: const Color(0xFF673AB7),
        icon: Icons.book,
      ),
      BusinessCategory(
        id: 'cafes',
        nameAr: 'كافيهات',
        nameEn: 'Cafes',
        nameKu: 'چایخانەکان',
        imagePath: 'assets/images/كافيهات.jpg',
        color: const Color(0xFF795548),
        icon: Icons.local_cafe,
      ),
      BusinessCategory(
        id: 'banking',
        nameAr: 'مصارف وبنوك',
        nameEn: 'Banks',
        nameKu: 'بانک و پارەخانە',
        imagePath: 'assets/images/مصارف وبنوك.jpg',
        color: const Color(0xFF607D8B),
        icon: Icons.account_balance,
      ),
      BusinessCategory(
        id: 'plants',
        nameAr: 'نباتات وحدائق',
        nameEn: 'Plants & Gardens',
        nameKu: 'ڕووەک و باخچە',
        imagePath: 'assets/images/زراعية.jpg',
        color: const Color(0xFF4CAF50),
        icon: Icons.local_florist,
      ),
      BusinessCategory(
        id: 'delivery',
        nameAr: 'خدمات توصيل',
        nameEn: 'Delivery Services',
        nameKu: 'خزمەتگوزاری گەیاندن',
        imagePath: 'assets/images/توصيل.jpg',
        color: const Color(0xFFFF5722),
        icon: Icons.delivery_dining,
      ),
      BusinessCategory(
        id: 'groceries',
        nameAr: 'مواد غذائية',
        nameEn: 'Groceries',
        nameKu: 'کەلوپەلی خۆراک',
        imagePath: 'assets/images/السوبرماركت.jpg',
        color: const Color(0xFF8BC34A),
        icon: Icons.shopping_cart,
      ),
      BusinessCategory(
        id: 'industry',
        nameAr: 'الصناعة',
        nameEn: 'Industry',
        nameKu: 'پیشەسازی',
        imagePath: 'assets/images/الصناعة.jpg',
        color: const Color(0xFF9C27B0),
        icon: Icons.precision_manufacturing,
      ),
    ];
  }
}
