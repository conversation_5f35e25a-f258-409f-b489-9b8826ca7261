<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - تسجيل الدخول</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            overflow: hidden;
        }

        /* Login Screen */
        .login-screen {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6), rgba(0,0,0,0.8)),
                        url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            animation: fadeIn 2s ease-in-out;
            padding: 40px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .login-content {
            animation: slideUp 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            max-width: 390px;
            width: 90%;
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
            text-align: center;
        }



        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(100px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .app-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .app-logo img {
            width: 110%;
            height: 110%;
            object-fit: cover;
            border-radius: 20px;
        }

        .app-logo i {
            color: white;
            font-size: 50px;
        }

        .app-title {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin-bottom: 18px;
            letter-spacing: 1px;
        }

        .form-group {
            margin-bottom: 12px;
            text-align: right;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
        }

        .login-button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            margin-bottom: 15px;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .social-login {
            margin: 12px 0;
        }

        .social-title {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
        }

        .social-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .social-btn {
            width: 50px;
            height: 50px;
            border: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 10px;
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .social-btn:hover {
            border-color: rgba(102, 126, 234, 0.8);
            box-shadow: 0 0 15px rgba(102, 126, 234, 0.4);
            transform: translateY(-2px);
        }

        .social-btn i {
            font-size: 18px;
        }



        .form-links {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            font-size: 14px;
        }

        .form-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .form-link:hover {
            color: #667eea;
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 15px 0;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: rgba(255, 255, 255, 0.3);
        }

        .divider span {
            padding: 0 15px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-content {
                max-width: 90%;
                padding: 30px;
            }
            
            .app-title {
                font-size: 28px;
            }
            
            .social-buttons {
                gap: 8px;
            }
            
            .social-btn {
                min-width: 75px;
                padding: 12px 6px;
                font-size: 10px;
            }

            .social-btn i {
                font-size: 16px;
                margin-bottom: 4px;
            }
        }

        /* Forgot Password Modal */
        .forgot-password-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            backdrop-filter: blur(10px);
        }

        .forgot-password-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .forgot-password-content {
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            width: 90%;
            max-width: 450px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.4s ease-out;
        }

        .forgot-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .forgot-header h3 {
            color: white;
            font-size: 18px;
            font-weight: bold;
            font-family: 'Cairo', sans-serif;
        }

        .close-btn {
            width: 35px;
            height: 35px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .forgot-body {
            padding: 25px;
        }

        .forgot-description {
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            margin-bottom: 20px;
            font-size: 14px;
        }

        /* Method Selection */
        .method-selection {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .method-option {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .method-option:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(102, 126, 234, 0.5);
            transform: translateY(-2px);
        }

        .method-icon {
            width: 45px;
            height: 45px;
            background: rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .method-text {
            flex: 1;
        }

        .method-title {
            color: white;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 3px;
        }

        .method-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }

        /* Recovery Input */
        .recovery-input {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .recovery-input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.15);
        }

        .recovery-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        /* Verification Boxes */
        .verification-message {
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            margin-bottom: 20px;
            font-size: 13px;
        }

        .verification-boxes {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .verification-input {
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(102, 126, 234, 0.5);
            border-radius: 10px;
            color: white;
            font-family: 'Cairo', sans-serif;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .verification-input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.15);
            transform: scale(1.05);
        }

        .verification-status {
            text-align: center;
            margin-bottom: 20px;
            min-height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .verification-success {
            color: #4CAF50;
            font-size: 24px;
            animation: successPop 0.6s ease-out;
        }

        /* Buttons */
        .send-code-btn, .verify-code-btn, .save-password-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            font-family: 'Cairo', sans-serif;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            margin-top: 10px;
        }

        .send-code-btn:hover, .verify-code-btn:hover, .save-password-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a6fd8, #6a42a0);
        }

        @keyframes successPop {
            0% { transform: scale(0); opacity: 0; }
            50% { transform: scale(1.2); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }

        @media (max-width: 480px) {
            .login-content {
                padding: 25px;
            }

            .form-links {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .verification-boxes {
                gap: 8px;
            }

            .verification-input {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div class="login-screen">
        <div class="login-content">
            <!-- App Logo -->
            <div class="app-logo">
                <img src="assets/logo.png" alt="Get Me Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <i class="fas fa-shopping-bag" style="display: none;"></i>
            </div>

            <!-- App Title -->
            <h1 class="app-title">Get Me</h1>

            <!-- Login Form -->
            <form id="loginForm">
                <div class="form-group">
                    <input type="email" class="form-input" placeholder="البريد الإلكتروني" required>
                </div>

                <div class="form-group">
                    <input type="password" class="form-input" placeholder="كلمة المرور" required>
                </div>

                <button type="submit" class="login-button">
                    تسجيل الدخول
                </button>
            </form>
            
            <!-- Divider -->
            <div class="divider">
                <span>أو</span>
            </div>
            
            <!-- Social Login -->
            <div class="social-login">
                <div class="social-title">تسجيل الدخول بواسطة</div>
                <div class="social-buttons">
                    <div class="social-btn email" onclick="socialLogin('email')">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="social-btn facebook" onclick="socialLogin('facebook')">
                        <i class="fab fa-facebook-f"></i>
                    </div>
                    <div class="social-btn instagram" onclick="socialLogin('instagram')">
                        <i class="fab fa-instagram"></i>
                    </div>
                    <div class="social-btn tiktok" onclick="socialLogin('tiktok')">
                        <i class="fab fa-tiktok"></i>
                    </div>
                </div>
            </div>
            


            <!-- Form Links -->
            <div class="form-links">
                <a href="#" class="form-link" onclick="forgotPassword()">نسيت كلمة المرور؟</a>
                <a href="#" class="form-link" onclick="createAccount()">إنشاء حساب جديد</a>
            </div>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div class="forgot-password-modal" id="forgotPasswordModal">
        <div class="forgot-password-content">
            <!-- Step 1: Choose Method -->
            <div class="forgot-step" id="forgotStep1">
                <div class="forgot-header">
                    <h3>استعادة كلمة المرور</h3>
                    <button class="close-btn" onclick="closeForgotModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="forgot-body">
                    <p class="forgot-description">اختر الطريقة المناسبة لاستعادة كلمة المرور</p>

                    <div class="method-selection">
                        <div class="method-option" onclick="selectMethod('email')">
                            <div class="method-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="method-text">
                                <div class="method-title">البريد الإلكتروني</div>
                                <div class="method-subtitle">إرسال رمز التحقق للإيميل</div>
                            </div>
                        </div>

                        <div class="method-option" onclick="selectMethod('phone')">
                            <div class="method-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="method-text">
                                <div class="method-title">رقم الهاتف</div>
                                <div class="method-subtitle">إرسال رمز التحقق برسالة نصية</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Enter Email/Phone -->
            <div class="forgot-step" id="forgotStep2" style="display: none;">
                <div class="forgot-header">
                    <h3 id="step2Title">إدخال البريد الإلكتروني</h3>
                    <button class="close-btn" onclick="closeForgotModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="forgot-body">
                    <div class="input-group">
                        <input type="text" id="recoveryInput" class="recovery-input" placeholder="أدخل بريدك الإلكتروني">
                    </div>

                    <button class="send-code-btn" onclick="sendVerificationCode()">
                        إرسال رمز التحقق
                    </button>
                </div>
            </div>

            <!-- Step 3: Verification Code -->
            <div class="forgot-step" id="forgotStep3" style="display: none;">
                <div class="forgot-header">
                    <h3>إدخال رمز التحقق</h3>
                    <button class="close-btn" onclick="closeForgotModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="forgot-body">
                    <p class="verification-message" id="verificationMessage">تم إرسال رمز التحقق إلى بريدك الإلكتروني</p>

                    <div class="verification-boxes">
                        <input type="text" class="verification-input" maxlength="1" oninput="moveToNext(this, 0)">
                        <input type="text" class="verification-input" maxlength="1" oninput="moveToNext(this, 1)">
                        <input type="text" class="verification-input" maxlength="1" oninput="moveToNext(this, 2)">
                        <input type="text" class="verification-input" maxlength="1" oninput="moveToNext(this, 3)">
                        <input type="text" class="verification-input" maxlength="1" oninput="moveToNext(this, 4)">
                        <input type="text" class="verification-input" maxlength="1" oninput="moveToNext(this, 5)">
                    </div>

                    <div class="verification-status" id="verificationStatus"></div>

                    <button class="verify-code-btn" onclick="verifyCode()">
                        تحقق من الرمز
                    </button>
                </div>
            </div>

            <!-- Step 4: New Password -->
            <div class="forgot-step" id="forgotStep4" style="display: none;">
                <div class="forgot-header">
                    <h3>كلمة مرور جديدة</h3>
                    <button class="close-btn" onclick="closeForgotModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="forgot-body">
                    <div class="input-group">
                        <input type="password" id="newPassword" class="recovery-input" placeholder="كلمة المرور الجديدة">
                    </div>

                    <div class="input-group">
                        <input type="password" id="confirmPassword" class="recovery-input" placeholder="إعادة كتابة كلمة المرور">
                    </div>

                    <button class="save-password-btn" onclick="saveNewPassword()">
                        حفظ كلمة المرور
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Demo Login Credentials
        const DEMO_EMAIL = '<EMAIL>';
        const DEMO_PASSWORD = '123456';

        // Login Form Handler
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const email = this.querySelector('input[type="email"]').value;
            const password = this.querySelector('input[type="password"]').value;

            if (email && password) {
                const button = this.querySelector('.login-button');
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
                button.disabled = true;

                setTimeout(() => {
                    // Check demo credentials
                    if (email === DEMO_EMAIL && password === DEMO_PASSWORD) {
                        // Successful login - redirect immediately to main app
                        window.location.href = 'get_me_updated.html';
                    } else {
                        // Invalid credentials
                        alert('بيانات تسجيل الدخول غير صحيحة!\n\n🎭 بيانات العرض:\n📧 البريد: <EMAIL>\n🔑 كلمة المرور: 123456');
                        button.innerHTML = 'تسجيل الدخول';
                        button.disabled = false;
                    }
                }, 1000);
            }
        });

        // Social Login Handler
        function socialLogin(platform) {
            const platformNames = {
                'email': 'البريد الإلكتروني',
                'facebook': 'فيسبوك',
                'instagram': 'انستجرام',
                'tiktok': 'تيكتوك'
            };
            
            alert(`سيتم تسجيل الدخول عبر ${platformNames[platform]}\n\nسيتم توجيهك لصفحة ${platformNames[platform]}...`);
        }

        // Forgot Password System
        let selectedMethod = '';
        let verificationCode = '';

        function forgotPassword() {
            const modal = document.getElementById('forgotPasswordModal');
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
            showForgotStep(1);
        }

        function closeForgotModal() {
            const modal = document.getElementById('forgotPasswordModal');
            modal.classList.remove('active');
            document.body.style.overflow = '';
            resetForgotProcess();
        }

        function showForgotStep(stepNumber) {
            // Hide all steps
            for (let i = 1; i <= 4; i++) {
                document.getElementById(`forgotStep${i}`).style.display = 'none';
            }
            // Show current step
            document.getElementById(`forgotStep${stepNumber}`).style.display = 'block';
        }

        function selectMethod(method) {
            selectedMethod = method;

            if (method === 'email') {
                document.getElementById('step2Title').textContent = 'إدخال البريد الإلكتروني';
                document.getElementById('recoveryInput').placeholder = 'أدخل بريدك الإلكتروني';
                document.getElementById('recoveryInput').type = 'email';
            } else {
                document.getElementById('step2Title').textContent = 'إدخال رقم الهاتف';
                document.getElementById('recoveryInput').placeholder = 'أدخل رقم هاتفك';
                document.getElementById('recoveryInput').type = 'tel';
            }

            showForgotStep(2);
        }

        function sendVerificationCode() {
            const input = document.getElementById('recoveryInput').value;

            if (!input) {
                alert('يرجى إدخال ' + (selectedMethod === 'email' ? 'البريد الإلكتروني' : 'رقم الهاتف'));
                return;
            }

            // Generate random 6-digit code for demo
            verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

            // Update verification message
            const message = selectedMethod === 'email' ?
                `تم إرسال رمز التحقق إلى: ${input}` :
                `تم إرسال رمز التحقق إلى: ${input}`;
            document.getElementById('verificationMessage').textContent = message;

            // Show verification step
            showForgotStep(3);

            // For demo purposes, show the code
            setTimeout(() => {
                alert(`رمز التحقق للعرض: ${verificationCode}\n\n(في التطبيق الحقيقي سيتم إرساله ${selectedMethod === 'email' ? 'للإيميل' : 'برسالة نصية'})`);
            }, 1000);
        }

        function moveToNext(input, index) {
            if (input.value.length === 1) {
                const inputs = document.querySelectorAll('.verification-input');
                if (index < inputs.length - 1) {
                    inputs[index + 1].focus();
                }

                // Check if all boxes are filled
                checkVerificationComplete();
            }
        }

        function checkVerificationComplete() {
            const inputs = document.querySelectorAll('.verification-input');
            const enteredCode = Array.from(inputs).map(input => input.value).join('');

            if (enteredCode.length === 6) {
                // Auto verify when all boxes are filled
                setTimeout(() => {
                    verifyCode();
                }, 500);
            }
        }

        function verifyCode() {
            const inputs = document.querySelectorAll('.verification-input');
            const enteredCode = Array.from(inputs).map(input => input.value).join('');

            if (enteredCode === verificationCode) {
                // Show success
                document.getElementById('verificationStatus').innerHTML =
                    '<div class="verification-success"><i class="fas fa-check-circle"></i></div>';

                setTimeout(() => {
                    showForgotStep(4);
                }, 1500);
            } else {
                // Show error
                alert('رمز التحقق غير صحيح!\n\nرمز العرض: ' + verificationCode);

                // Clear inputs
                inputs.forEach(input => {
                    input.value = '';
                });
                inputs[0].focus();
            }
        }

        function saveNewPassword() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (!newPassword || !confirmPassword) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            if (newPassword !== confirmPassword) {
                alert('كلمة المرور غير متطابقة!');
                return;
            }

            if (newPassword.length < 6) {
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }

            // Success
            alert('تم حفظ كلمة المرور الجديدة بنجاح!\n\nيمكنك الآن تسجيل الدخول بكلمة المرور الجديدة.');
            closeForgotModal();
        }

        function resetForgotProcess() {
            selectedMethod = '';
            verificationCode = '';
            document.getElementById('recoveryInput').value = '';
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
            document.getElementById('verificationStatus').innerHTML = '';

            const inputs = document.querySelectorAll('.verification-input');
            inputs.forEach(input => {
                input.value = '';
            });
        }

        // Create Account Handler
        function createAccount() {
            // الانتقال إلى صفحة إنشاء حساب
            window.location.href = 'stage4_register.html';
        }

        // Animation on load
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach((input, index) => {
                input.style.opacity = '0';
                input.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    input.style.transition = 'all 0.5s ease-out';
                    input.style.opacity = '1';
                    input.style.transform = 'translateY(0)';
                }, 300 + (index * 100));
            });
        });
    </script>
</body>
</html>
