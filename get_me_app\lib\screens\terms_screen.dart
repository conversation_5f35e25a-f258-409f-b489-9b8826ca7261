import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import '../models/subscription_plan.dart';

class TermsScreen extends StatefulWidget {
  final UserRegistrationData registrationData;
  final VoidCallback onNext;
  final VoidCallback onBack;

  const TermsScreen({
    super.key,
    required this.registrationData,
    required this.onNext,
    required this.onBack,
  });

  @override
  State<TermsScreen> createState() => _TermsScreenState();
}

class _TermsScreenState extends State<TermsScreen> {
  bool _hasAcceptedTerms = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 32),
              Expanded(
                child: _buildTermsContent(),
              ),
              _buildAcceptanceSection(),
              const SizedBox(height: 16),
              _buildButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    widget.registrationData.selectedPlan?.primaryColor ??
                        const Color(0xFF667eea),
                    widget.registrationData.selectedPlan?.secondaryColor ??
                        const Color(0xFF764ba2),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.gavel,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الشروط والأحكام',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1C1E21),
                    ),
                  ),
                  Text(
                    'يرجى قراءة الشروط بعناية',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          'يرجى قراءة الشروط والأحكام والموافقة عليها للمتابعة',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }

  Widget _buildTermsContent() {
    return GFCard(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      content: Padding(
        padding: const EdgeInsets.all(20),
        child: Scrollbar(
          controller: _scrollController,
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTermsSection(
                  'شروط الاستخدام العامة',
                  [
                    'يجب على المستخدم تقديم بيانات صحيحة ودقيقة عند التسجيل',
                    'يحظر استخدام المنصة لأي أنشطة غير قانونية أو مخالفة للآداب العامة',
                    'يحق للإدارة تعليق أو إلغاء أي حساب في حالة مخالفة الشروط',
                    'المستخدم مسؤول عن الحفاظ على سرية بيانات حسابه',
                  ],
                ),
                const SizedBox(height: 24),
                _buildTermsSection(
                  'شروط البيع والشراء',
                  [
                    'يجب على البائعين تقديم وصف دقيق للمنتجات والخدمات',
                    'يحظر بيع المنتجات المحظورة قانونياً أو المخالفة للأنظمة',
                    'المنصة غير مسؤولة عن جودة المنتجات أو الخدمات المقدمة',
                    'يجب حل النزاعات بين البائع والمشتري بطريقة ودية',
                  ],
                ),
                const SizedBox(height: 24),
                _buildTermsSection(
                  'سياسة الخصوصية',
                  [
                    'نحن نحترم خصوصية المستخدمين ونحمي بياناتهم الشخصية',
                    'لا نشارك البيانات الشخصية مع أطراف ثالثة دون موافقة',
                    'نستخدم البيانات لتحسين الخدمة وتقديم تجربة أفضل',
                    'يحق للمستخدم طلب حذف بياناته في أي وقت',
                  ],
                ),
                const SizedBox(height: 24),
                _buildTermsSection(
                  'المسؤولية القانونية',
                  [
                    'يحق للإدارة اتخاذ إجراء قانوني ضد أي مستخدم يخالف الشروط',
                    'المستخدم مسؤول قانونياً عن أي محتوى ينشره على المنصة',
                    'في حالة النزاعات، تطبق قوانين المملكة العربية السعودية',
                    'المحاكم السعودية هي المختصة بالنظر في أي نزاعات',
                  ],
                ),
                const SizedBox(height: 24),
                _buildTermsSection(
                  'تحديث الشروط',
                  [
                    'يحق للإدارة تحديث هذه الشروط في أي وقت',
                    'سيتم إشعار المستخدمين بأي تغييرات مهمة',
                    'استمرار استخدام المنصة يعني الموافقة على الشروط المحدثة',
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTermsSection(String title, List<String> points) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1C1E21),
          ),
        ),
        const SizedBox(height: 12),
        ...points.map((point) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 6,
                height: 6,
                margin: const EdgeInsets.only(top: 8, left: 8),
                decoration: BoxDecoration(
                  color: widget.registrationData.selectedPlan?.primaryColor ??
                      const Color(0xFF667eea),
                  shape: BoxShape.circle,
                ),
              ),
              Expanded(
                child: Text(
                  point,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                    height: 1.5,
                  ),
                ),
              ),
            ],
          ),
        )).toList(),
      ],
    );
  }

  Widget _buildAcceptanceSection() {
    return GFCard(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      content: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            GFCheckbox(
              size: GFSize.MEDIUM,
              activeBgColor: widget.registrationData.selectedPlan?.primaryColor ??
                  const Color(0xFF667eea),
              inactiveBorderColor: Colors.grey,
              activeBorderColor: widget.registrationData.selectedPlan?.primaryColor ??
                  const Color(0xFF667eea),
              type: GFCheckboxType.basic,
              value: _hasAcceptedTerms,
              onChanged: (value) {
                setState(() {
                  _hasAcceptedTerms = value;
                });
              },
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'أوافق على جميع الشروط والأحكام المذكورة أعلاه وأتعهد بالالتزام بها',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: widget.onBack,
            style: OutlinedButton.styleFrom(
              foregroundColor: widget.registrationData.selectedPlan?.primaryColor ??
                  const Color(0xFF667eea),
              side: BorderSide(
                color: widget.registrationData.selectedPlan?.primaryColor ??
                    const Color(0xFF667eea),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'رجوع',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _hasAcceptedTerms ? _handleContinue : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _hasAcceptedTerms
                  ? (widget.registrationData.selectedPlan?.primaryColor ??
                      const Color(0xFF667eea))
                  : Colors.grey,
              foregroundColor: Colors.white,
              elevation: _hasAcceptedTerms ? 8 : 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'الموافقة والمتابعة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _handleContinue() {
    if (_hasAcceptedTerms) {
      widget.registrationData.hasAcceptedTerms = true;
      widget.onNext();
    }
  }
}
