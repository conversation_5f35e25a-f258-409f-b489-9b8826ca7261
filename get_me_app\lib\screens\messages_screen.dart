import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';

class MessagesScreen extends StatefulWidget {
  const MessagesScreen({super.key});

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen> {
  final List<ChatData> _chats = _getDemoChats();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: Text(
          'الرسائل',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.black87),
            onPressed: () {
              // TODO: Implement search
            },
          ),
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.black87),
            onPressed: () {
              // TODO: Start new chat
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Online Users
          Container(
            color: Colors.white,
            height: 100,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'متصل الآن',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: 6,
                    itemBuilder: (context, index) {
                      return Container(
                        margin: const EdgeInsets.only(left: 12),
                        child: Column(
                          children: [
                            Stack(
                              children: [
                                CircleAvatar(
                                  radius: 20,
                                  backgroundImage: NetworkImage(
                                    'https://via.placeholder.com/150',
                                  ),
                                ),
                                Positioned(
                                  bottom: 0,
                                  right: 0,
                                  child: Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF4CAF50),
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: Colors.white,
                                        width: 2,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // Chats List
          Expanded(
            child: ListView.builder(
              itemCount: _chats.length,
              itemBuilder: (context, index) {
                final chat = _chats[index];
                return _buildChatItem(chat);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Start new chat
        },
        backgroundColor: const Color(0xFF2196F3),
        child: const Icon(
          Icons.chat,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildChatItem(ChatData chat) {
    return Container(
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: 1),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        leading: Stack(
          children: [
            CircleAvatar(
              radius: 25,
              backgroundImage: NetworkImage(chat.userImage),
            ),
            if (chat.isOnline)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 14,
                  height: 14,
                  decoration: BoxDecoration(
                    color: const Color(0xFF4CAF50),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 2,
                    ),
                  ),
                ),
              ),
          ],
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                chat.userName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ),
            Text(
              chat.time,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        subtitle: Row(
          children: [
            if (chat.isRead)
              const Icon(
                Icons.done_all,
                size: 16,
                color: Color(0xFF2196F3),
              )
            else
              const Icon(
                Icons.done,
                size: 16,
                color: Colors.grey,
              ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                chat.lastMessage,
                style: TextStyle(
                  fontSize: 14,
                  color: chat.unreadCount > 0 ? Colors.black87 : Colors.grey,
                  fontWeight: chat.unreadCount > 0
                      ? FontWeight.w600
                      : FontWeight.normal,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (chat.unreadCount > 0)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2,
                ),
                decoration: const BoxDecoration(
                  color: Color(0xFF2196F3),
                  shape: BoxShape.circle,
                ),
                child: Text(
                  chat.unreadCount.toString(),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        onTap: () {
          _openChat(chat);
        },
      ),
    );
  }

  void _openChat(ChatData chat) {
    // TODO: Navigate to chat screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'فتح محادثة مع ${chat.userName}',
          style: const TextStyle(),
        ),
        backgroundColor: const Color(0xFF2196F3),
      ),
    );
  }

  static List<ChatData> _getDemoChats() {
    return [
      ChatData(
        userName: 'أحمد محمد',
        userImage: 'https://via.placeholder.com/150',
        lastMessage: 'مرحباً، هل المنتج متوفر؟',
        time: '10:30 ص',
        unreadCount: 2,
        isOnline: true,
        isRead: false,
      ),
      ChatData(
        userName: 'فاطمة أحمد',
        userImage: 'https://via.placeholder.com/150',
        lastMessage: 'شكراً لك على الخدمة الممتازة',
        time: '9:15 ص',
        unreadCount: 0,
        isOnline: false,
        isRead: true,
      ),
      ChatData(
        userName: 'محمد علي',
        userImage: 'https://via.placeholder.com/150',
        lastMessage: 'متى يمكنني استلام الطلب؟',
        time: 'أمس',
        unreadCount: 1,
        isOnline: true,
        isRead: false,
      ),
      ChatData(
        userName: 'سارة حسن',
        userImage: 'https://via.placeholder.com/150',
        lastMessage: 'ممتاز، سأقوم بالطلب',
        time: 'أمس',
        unreadCount: 0,
        isOnline: false,
        isRead: true,
      ),
      ChatData(
        userName: 'عبدالله أحمد',
        userImage: 'https://via.placeholder.com/150',
        lastMessage: 'هل يمكن التفاوض على السعر؟',
        time: 'الأحد',
        unreadCount: 0,
        isOnline: true,
        isRead: true,
      ),
    ];
  }
}

class ChatData {
  final String userName;
  final String userImage;
  final String lastMessage;
  final String time;
  final int unreadCount;
  final bool isOnline;
  final bool isRead;

  ChatData({
    required this.userName,
    required this.userImage,
    required this.lastMessage,
    required this.time,
    this.unreadCount = 0,
    this.isOnline = false,
    this.isRead = true,
  });
}
