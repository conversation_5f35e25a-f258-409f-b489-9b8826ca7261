import 'package:flutter/material.dart';

// نموذج بيانات التحليلات التجارية
class BusinessAnalytics {
  final String id;
  final DateTime date;
  final double sales;
  final double profit;
  final double expenses;
  final int orders;
  final int views;
  final int interactions;

  BusinessAnalytics({
    required this.id,
    required this.date,
    required this.sales,
    required this.profit,
    required this.expenses,
    required this.orders,
    required this.views,
    required this.interactions,
  });

  // حساب صافي الربح
  double get netProfit => sales - expenses;

  // حساب معدل التحويل
  double get conversionRate => views > 0 ? (orders / views) * 100 : 0;

  // بيانات تجريبية للتحليلات اليومية
  static List<BusinessAnalytics> getDailyAnalytics() {
    final now = DateTime.now();
    return List.generate(7, (index) {
      final date = now.subtract(Duration(days: 6 - index));
      return BusinessAnalytics(
        id: 'daily_$index',
        date: date,
        sales: 1000 + (index * 200) + (index % 2 == 0 ? 300 : -100),
        profit: 300 + (index * 50),
        expenses: 200 + (index * 30),
        orders: 5 + index,
        views: 100 + (index * 20),
        interactions: 20 + (index * 5),
      );
    });
  }

  // بيانات تجريبية للتحليلات الأسبوعية
  static List<BusinessAnalytics> getWeeklyAnalytics() {
    final now = DateTime.now();
    return List.generate(4, (index) {
      final date = now.subtract(Duration(days: (3 - index) * 7));
      return BusinessAnalytics(
        id: 'weekly_$index',
        date: date,
        sales: 5000 + (index * 1000),
        profit: 1500 + (index * 300),
        expenses: 1000 + (index * 200),
        orders: 25 + (index * 5),
        views: 500 + (index * 100),
        interactions: 100 + (index * 25),
      );
    });
  }

  // بيانات تجريبية للتحليلات الشهرية
  static List<BusinessAnalytics> getMonthlyAnalytics() {
    final now = DateTime.now();
    return List.generate(6, (index) {
      final date = DateTime(now.year, now.month - (5 - index), 1);
      return BusinessAnalytics(
        id: 'monthly_$index',
        date: date,
        sales: 20000 + (index * 5000),
        profit: 6000 + (index * 1500),
        expenses: 4000 + (index * 1000),
        orders: 100 + (index * 20),
        views: 2000 + (index * 400),
        interactions: 400 + (index * 100),
      );
    });
  }
}

// نموذج أخبار السوق
class MarketNews {
  final String id;
  final String title;
  final String content;
  final String category;
  final DateTime publishDate;
  final String imageUrl;
  final bool isImportant;

  MarketNews({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.publishDate,
    required this.imageUrl,
    required this.isImportant,
  });

  static List<MarketNews> getMarketNews() {
    return [
      MarketNews(
        id: '1',
        title: 'ارتفاع أسعار العقارات في المنطقة الشرقية',
        content: 'شهدت أسعار العقارات في المنطقة الشرقية ارتفاعاً ملحوظاً بنسبة 15% خلال الشهر الماضي...',
        category: 'عقارات',
        publishDate: DateTime.now().subtract(const Duration(hours: 2)),
        imageUrl: 'assets/images/عقارات.jpg',
        isImportant: true,
      ),
      MarketNews(
        id: '2',
        title: 'افتتاح مجمع تجاري جديد في وسط المدينة',
        content: 'تم افتتاح مجمع تجاري جديد يضم أكثر من 200 محل تجاري ومطعم...',
        category: 'تجارة',
        publishDate: DateTime.now().subtract(const Duration(hours: 5)),
        imageUrl: 'assets/images/محلات.jpg',
        isImportant: false,
      ),
      MarketNews(
        id: '3',
        title: 'تطبيق خصومات جديدة على المنتجات الطبية',
        content: 'أعلنت وزارة الصحة عن تطبيق خصومات تصل إلى 30% على المنتجات الطبية...',
        category: 'طبية',
        publishDate: DateTime.now().subtract(const Duration(days: 1)),
        imageUrl: 'assets/images/طبية.jpg',
        isImportant: true,
      ),
    ];
  }
}

// نموذج المذكرة الشخصية
class PersonalNote {
  final String id;
  final String title;
  final String content;
  final DateTime createdDate;
  final DateTime? lastModified;
  final List<String> tags;
  final Color color;

  PersonalNote({
    required this.id,
    required this.title,
    required this.content,
    required this.createdDate,
    this.lastModified,
    required this.tags,
    required this.color,
  });

  static List<PersonalNote> getDemoNotes() {
    return [
      PersonalNote(
        id: '1',
        title: 'اجتماع مع العملاء الجدد',
        content: 'مناقشة العروض الجديدة للشقق في المنطقة الشمالية\nالموعد: الأحد 3 مساءً\nالمكان: المكتب الرئيسي',
        createdDate: DateTime.now().subtract(const Duration(days: 2)),
        lastModified: DateTime.now().subtract(const Duration(hours: 3)),
        tags: ['اجتماع', 'عملاء', 'عقارات'],
        color: Colors.blue,
      ),
      PersonalNote(
        id: '2',
        title: 'قائمة المهام الأسبوعية',
        content: '1. متابعة طلبات الإيجار\n2. تحديث أسعار العقارات\n3. التواصل مع العملاء المهتمين\n4. إعداد التقرير الشهري',
        createdDate: DateTime.now().subtract(const Duration(days: 1)),
        tags: ['مهام', 'أسبوعي'],
        color: Colors.green,
      ),
    ];
  }
}
