import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/business_analytics.dart';

class AnalyticsChartWidget extends StatefulWidget {
  const AnalyticsChartWidget({super.key});

  @override
  State<AnalyticsChartWidget> createState() => _AnalyticsChartWidgetState();
}

class _AnalyticsChartWidgetState extends State<AnalyticsChartWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedPeriod = 0; // 0: يومي, 1: أسبوعي, 2: شهري

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildPeriodSelector(),
          const SizedBox(height: 20),
          _buildChart(),
          const SizedBox(height: 16),
          _buildSummaryCards(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return const Row(
      children: [
        Icon(Icons.analytics, color: Color(0xFF667eea), size: 24),
        SizedBox(width: 8),
        Text(
          'تحليل الأداء',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(child: _buildPeriodButton('يومي', 0)),
          Expanded(child: _buildPeriodButton('أسبوعي', 1)),
          Expanded(child: _buildPeriodButton('شهري', 2)),
        ],
      ),
    );
  }

  Widget _buildPeriodButton(String title, int index) {
    final isSelected = _selectedPeriod == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPeriod = index;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF667eea) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: isSelected ? Colors.white : Colors.grey[600],
          ),
        ),
      ),
    );
  }

  Widget _buildChart() {
    List<BusinessAnalytics> data;
    switch (_selectedPeriod) {
      case 0:
        data = BusinessAnalytics.getDailyAnalytics();
        break;
      case 1:
        data = BusinessAnalytics.getWeeklyAnalytics();
        break;
      case 2:
        data = BusinessAnalytics.getMonthlyAnalytics();
        break;
      default:
        data = BusinessAnalytics.getDailyAnalytics();
    }

    return SizedBox(
      height: 200,
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: 1000,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey[300]!,
                strokeWidth: 1,
              );
            },
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: 1,
                getTitlesWidget: (double value, TitleMeta meta) {
                  if (value.toInt() >= 0 && value.toInt() < data.length) {
                    final date = data[value.toInt()].date;
                    return SideTitleWidget(
                      axisSide: meta.axisSide,
                      child: Text(
                        _selectedPeriod == 0
                            ? '${date.day}/${date.month}'
                            : _selectedPeriod == 1
                                ? 'أسبوع ${value.toInt() + 1}'
                                : '${date.month}/${date.year}',
                        style: const TextStyle(
                          color: Colors.grey,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    );
                  }
                  return Container();
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                interval: 1000,
                getTitlesWidget: (double value, TitleMeta meta) {
                  return Text(
                    '${(value / 1000).toStringAsFixed(0)}k',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  );
                },
                reservedSize: 42,
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          minX: 0,
          maxX: data.length.toDouble() - 1,
          minY: 0,
          maxY: data.map((e) => e.sales).reduce((a, b) => a > b ? a : b) * 1.2,
          lineBarsData: [
            // خط المبيعات
            LineChartBarData(
              spots: data.asMap().entries.map((entry) {
                return FlSpot(entry.key.toDouble(), entry.value.sales);
              }).toList(),
              isCurved: true,
              gradient: const LinearGradient(
                colors: [Color(0xFF667eea), Color(0xFF764ba2)],
              ),
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: const FlDotData(show: true),
              belowBarData: BarAreaData(
                show: true,
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF667eea).withOpacity(0.3),
                    const Color(0xFF764ba2).withOpacity(0.1),
                  ],
                ),
              ),
            ),
            // خط الأرباح
            LineChartBarData(
              spots: data.asMap().entries.map((entry) {
                return FlSpot(entry.key.toDouble(), entry.value.profit);
              }).toList(),
              isCurved: true,
              color: Colors.green,
              barWidth: 2,
              isStrokeCapRound: true,
              dotData: const FlDotData(show: false),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    List<BusinessAnalytics> data;
    switch (_selectedPeriod) {
      case 0:
        data = BusinessAnalytics.getDailyAnalytics();
        break;
      case 1:
        data = BusinessAnalytics.getWeeklyAnalytics();
        break;
      case 2:
        data = BusinessAnalytics.getMonthlyAnalytics();
        break;
      default:
        data = BusinessAnalytics.getDailyAnalytics();
    }

    final totalSales = data.fold(0.0, (sum, item) => sum + item.sales);
    final totalProfit = data.fold(0.0, (sum, item) => sum + item.profit);
    final totalOrders = data.fold(0, (sum, item) => sum + item.orders);
    final avgConversion = data.fold(0.0, (sum, item) => sum + item.conversionRate) / data.length;

    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'إجمالي المبيعات',
            '${totalSales.toStringAsFixed(0)} د.ع',
            Icons.attach_money,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الأرباح',
            '${totalProfit.toStringAsFixed(0)} د.ع',
            Icons.trending_up,
            Colors.green,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildSummaryCard(
            'الطلبات',
            totalOrders.toString(),
            Icons.shopping_cart,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildSummaryCard(
            'معدل التحويل',
            '${avgConversion.toStringAsFixed(1)}%',
            Icons.percent,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 10,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
