# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\dev\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\New folder (2)\\Get Me\\get_me_app\\get_me_app" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\dev\\flutter"
  "PROJECT_DIR=D:\\New folder (2)\\Get Me\\get_me_app\\get_me_app"
  "FLUTTER_ROOT=C:\\dev\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\New folder (2)\\Get Me\\get_me_app\\get_me_app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\New folder (2)\\Get Me\\get_me_app\\get_me_app"
  "FLUTTER_TARGET=D:\\New folder (2)\\Get Me\\get_me_app\\get_me_app\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC8xYWM2MTFjNjRlYWRiZDkzYzVmNWFiYTU0OTRiOGZjM2IzNWVlOTUyLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\New folder (2)\\Get Me\\get_me_app\\get_me_app\\.dart_tool\\package_config.json"
)
