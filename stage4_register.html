<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - إنشاء حساب</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            overflow: hidden;
        }

        /* Register Screen */
        .register-screen {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6), rgba(0,0,0,0.8)), 
                        url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            animation: fadeIn 2s ease-in-out;
            padding: 40px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .register-content {
            animation: slideUp 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            max-width: 390px;
            width: 90%;
            min-height: 500px;
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 20px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
            text-align: center;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(100px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }



        .app-title {
            font-size: 24px;
            font-weight: 300;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 18px;
            letter-spacing: 1px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 10px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 15px;
            gap: 10px;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .step.completed {
            background: #28a745;
            color: white;
        }

        .step-line {
            width: 40px;
            height: 2px;
            background: rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .step-line.completed {
            background: #28a745;
        }

        .form-group {
            margin-bottom: 10px;
            text-align: right;
        }

        .form-input {
            width: 100%;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
        }

        .form-select {
            width: 100%;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
        }

        .form-select option {
            background: #333;
            color: white;
        }

        .gender-group {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .gender-option {
            flex: 1;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .gender-option.selected {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-color: #667eea;
        }

        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            gap: 10px;
        }

        .nav-button {
            flex: 1;
            padding: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .nav-button:disabled {
            background: rgba(255, 255, 255, 0.2);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .step-title {
            font-size: 18px;
            color: white;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .camera-container {
            margin: 20px 0;
        }

        .camera-preview {
            width: 100%;
            height: 200px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            position: relative;
            transition: all 0.3s ease;
        }

        .camera-preview.success {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .camera-preview.error {
            border-color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }

        .camera-preview video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 12px;
        }

        .camera-placeholder {
            color: rgba(255, 255, 255, 0.5);
            font-size: 16px;
            text-align: center;
        }

        .camera-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .camera-btn {
            width: 60px;
            height: 60px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .camera-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: #667eea;
        }

        .camera-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-color: #667eea;
        }

        /* Map Styles */
        .map-container {
            margin: 15px 0;
        }

        .map-preview {
            width: 100%;
            height: 120px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .map-preview:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .map-preview.active {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .map-placeholder {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            text-align: center;
        }

        .map-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6), rgba(0,0,0,0.8)),
                        url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80');
            background-size: cover;
            background-position: center;
            z-index: 1000;
            backdrop-filter: blur(20px);
            animation: fadeIn 0.5s ease;
        }

        .map-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.5s ease;
        }

        .map-modal-content {
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 20px;
            width: 85%;
            max-width: 550px;
            max-height: 85vh;
            position: relative;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .map-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            color: white;
        }

        .map-modal-title {
            font-size: 20px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .map-close-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 18px;
            cursor: pointer;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .map-close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .map-display {
            width: 100%;
            height: 280px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            margin-bottom: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            transition: height 0.3s ease;
        }

        .map-display.expanded {
            height: 400px;
        }

        .map-display.collapsed {
            height: 200px;
        }

        .map-marker {
            position: absolute;
            width: 30px;
            height: 30px;
            background: #007bff;
            border: 3px solid white;
            border-radius: 50%;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .map-marker:hover {
            transform: translate(-50%, -50%) scale(1.2);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.6);
        }

        .map-marker::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }

        .map-controls {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            margin-top: 15px;
        }

        .map-btn {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: 13px;
            min-height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .map-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: rgba(102, 126, 234, 0.5);
        }

        .map-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a6fd8, #6a42a0);
        }

        .map-btn.primary:disabled {
            background: rgba(128, 128, 128, 0.3) !important;
            color: rgba(255, 255, 255, 0.5) !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: none !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
        }

        .location-info {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 10px;
            margin-top: 10px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            text-align: center;
        }

        .location-info.success {
            background: rgba(40, 167, 69, 0.2);
            border-color: #28a745;
            color: #d4edda;
        }

        /* Additional Map Styles */
        .map-btn:disabled {
            background: rgba(128, 128, 128, 0.3) !important;
            color: rgba(255, 255, 255, 0.5) !important;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
        }

        @keyframes slideUp {
            0% {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        @keyframes markerPulse {
            0% {
                box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 4px 25px rgba(0, 123, 255, 0.8);
                transform: scale(1.05);
            }
            100% {
                box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
                transform: scale(1);
            }
        }

        /* Enhanced Google-like Marker Styles */
        .custom-location-marker {
            background: transparent !important;
            border: none !important;
        }

        .marker-pin {
            position: relative;
            width: 40px;
            height: 40px;
        }

        .marker-pulse {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 60px;
            height: 60px;
            background: rgba(66, 133, 244, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: pulse 2s infinite;
        }

        .marker-dot {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            background: #4285f4;
            border: 3px solid white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            z-index: 1000;
        }

        @keyframes pulse {
            0% {
                transform: translate(-50%, -50%) scale(0.8);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(1.5);
                opacity: 0;
            }
        }

        /* Override Leaflet default styles for Google-like appearance */
        .leaflet-container {
            font-family: 'Cairo', sans-serif !important;
            background: #e5e3df !important;
        }



        .leaflet-popup-content-wrapper {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border: none;
        }

        .leaflet-popup-tip {
            background: white;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .leaflet-popup-content {
            margin: 15px !important;
            font-family: 'Cairo', sans-serif !important;
        }

        /* Map container enhancements */
        #leafletMap {
            border-radius: 15px;
            overflow: hidden;
        }

        /* ID Capture Styles */
        .id-capture-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .id-camera-preview {
            position: relative;
            width: 100%;
            max-width: 280px;
            height: 180px;
            border-radius: 10px;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        #idCameraVideo {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .id-frame-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
        }

        .id-frame {
            position: relative;
            width: 80%;
            height: 60%;
            border: 2px dashed rgba(255, 255, 255, 0.8);
            border-radius: 10px;
        }

        .id-corner {
            position: absolute;
            width: 20px;
            height: 20px;
            border: 3px solid #4CAF50;
        }

        .id-corner.top-left {
            top: -3px;
            left: -3px;
            border-right: none;
            border-bottom: none;
        }

        .id-corner.top-right {
            top: -3px;
            right: -3px;
            border-left: none;
            border-bottom: none;
        }

        .id-corner.bottom-left {
            bottom: -3px;
            left: -3px;
            border-right: none;
            border-top: none;
        }

        .id-corner.bottom-right {
            bottom: -3px;
            right: -3px;
            border-left: none;
            border-top: none;
        }

        .capture-button-container {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
        }

        .capture-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border: 3px solid white;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .capture-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6);
        }

        .capture-button:active {
            transform: scale(0.95);
        }

        .capture-success {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
        }

        .success-balloon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            animation: successPop 0.6s ease-out;
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.5);
        }

        @keyframes successPop {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .id-instructions {
            text-align: center;
            color: white;
            font-family: 'Cairo', sans-serif;
        }

        .instruction-text {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .instruction-subtext {
            font-size: 12px;
            opacity: 0.8;
        }

        .optional-notice {
            margin-top: 10px;
            padding: 8px 12px;
            background: rgba(102, 126, 234, 0.2);
            border: 1px solid rgba(102, 126, 234, 0.4);
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 11px;
            text-align: center;
            font-family: 'Cairo', sans-serif;
        }

        .optional-notice i {
            margin-left: 5px;
            color: #667eea;
        }

        /* Step 5: Data Summary Styles */
        .summary-container {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
            height: 280px;
            overflow-y: auto;
            position: relative;
            transition: height 0.3s ease;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        .summary-container::-webkit-scrollbar {
            width: 6px;
        }

        .summary-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .summary-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .summary-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .summary-container.expanded {
            height: 500px;
        }

        .summary-control-panel {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 8px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 1000;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
        }

        .summary-section {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .summary-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .summary-title {
            color: #667eea;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 8px;
            font-family: 'Cairo', sans-serif;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 13px;
        }

        .summary-label {
            color: rgba(255, 255, 255, 0.8);
            font-weight: normal;
        }

        .summary-value {
            color: white;
            font-weight: bold;
            text-align: left;
        }

        .summary-control-panel {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 8px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 1000;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
        }

        .download-section {
            text-align: center;
            margin-bottom: 20px;
        }

        .download-text {
            color: rgba(255, 255, 255, 0.95);
            font-family: 'Cairo', sans-serif;
            font-weight: 100;
            font-stretch: expanded;
            letter-spacing: 2px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-size: 18px;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            text-decoration: underline;
            text-underline-offset: 5px;
            text-decoration-thickness: 1px;
            padding: 10px 15px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            text-transform: uppercase;
        }

        .download-text:hover {
            color: #4CAF50;
            transform: translateY(-3px);
            letter-spacing: 3px;
            background: rgba(76, 175, 80, 0.15);
            text-decoration-color: #4CAF50;
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
            text-shadow: 0 2px 10px rgba(76, 175, 80, 0.5);
        }

        .download-text i {
            font-size: 16px;
            transition: all 0.4s ease;
        }

        .download-text:hover i {
            transform: scale(1.3) rotate(360deg);
            color: #4CAF50;
        }



        .login-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 12px 25px;
            border-radius: 10px;
            font-family: 'Cairo', sans-serif;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a6fd8, #6a42a0);
        }

        .capture-progress {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .progress-step.active {
            opacity: 1;
        }

        .progress-step.completed {
            opacity: 1;
        }

        .progress-icon {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .progress-step.active .progress-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-color: #667eea;
        }

        .progress-step.completed .progress-icon {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border-color: #4CAF50;
        }

        .progress-text {
            font-size: 12px;
            color: white;
            font-family: 'Cairo', sans-serif;
        }

        .captured-images {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .captured-image {
            position: relative;
            width: 120px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #4CAF50;
        }

        .captured-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-label {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            text-align: center;
            font-family: 'Cairo', sans-serif;
        }

        /* Fullscreen Map Modal */
        .fullscreen-map-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            z-index: 10000;
            backdrop-filter: blur(10px);
        }

        .fullscreen-map-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fullscreen-map-content {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            background: rgba(128, 128, 128, 0.1);
            backdrop-filter: blur(20px);
        }

        .fullscreen-map-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: rgba(128, 128, 128, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .fullscreen-map-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            font-family: 'Cairo', sans-serif;
        }

        .fullscreen-close-btn {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fullscreen-close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .fullscreen-map-display {
            flex: 1;
            position: relative;
            margin: 0 20px;
            border-radius: 15px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .fullscreen-control-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 8px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 1000;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
            cursor: move;
            user-select: none;
        }

        .fullscreen-control-panel:hover {
            background: rgba(128, 128, 128, 0.2);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        }

        .fullscreen-control-panel.dragging {
            opacity: 0.8;
            transform: scale(1.05);
            z-index: 1001;
        }

        .fullscreen-map-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            padding: 20px;
            background: rgba(128, 128, 128, 0.1);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .fullscreen-gps-btn {
            width: 45px;
            height: 45px;
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fullscreen-gps-btn:hover {
            background: rgba(128, 128, 128, 0.25);
            color: white;
            transform: translateY(-2px);
        }

        .fullscreen-gps-btn.active .fa-map-marker-alt {
            color: #ff6b6b;
            text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
        }

        .fullscreen-map-btn {
            padding: 12px 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            font-family: 'Cairo', sans-serif;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: 14px;
            min-height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .fullscreen-map-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: rgba(102, 126, 234, 0.5);
        }

        .fullscreen-map-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a6fd8, #6a42a0);
        }

        .fullscreen-map-btn.primary:disabled {
            background: rgba(128, 128, 128, 0.3) !important;
            color: rgba(255, 255, 255, 0.5) !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: none !important;
        }

        .fullscreen-map-btn.large {
            padding: 12px 25px;
            font-size: 15px;
            min-height: 48px;
            min-width: 140px;
        }

        /* GPS Location Button */
        .gps-location-btn {
            width: 36px;
            height: 36px;
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
        }

        .gps-location-btn:hover {
            background: rgba(128, 128, 128, 0.25);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }

        .gps-location-btn.active .fa-map-marker-alt {
            color: #ff6b6b;
            text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
        }

        .gps-location-btn.loading {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Map Control Panel */
        .map-control-panel {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(128, 128, 128, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 8px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 1000;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
            cursor: move;
            user-select: none;
        }

        .map-control-panel:hover {
            background: rgba(128, 128, 128, 0.2);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        }

        .map-control-panel.dragging {
            opacity: 0.8;
            transform: scale(1.05);
            z-index: 1001;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
            position: relative;
        }

        .control-group:not(:last-child)::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 1px;
            background: rgba(255, 255, 255, 0.3);
        }

        .control-btn {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: #555;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 1);
            color: #333;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .control-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
        }

        /* Search Bar Styles */
        .map-search-container {
            margin-bottom: 12px;
            position: relative;
        }

        .search-input-wrapper {
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            z-index: 10;
        }

        .map-search-input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: none;
            background: transparent;
            color: white;
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            outline: none;
            direction: rtl;
        }

        .map-search-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .map-search-input:focus {
            background: rgba(255, 255, 255, 0.15);
        }

        .search-clear-btn {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .search-clear-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) scale(1.1);
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 0 0 12px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-top: none;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .search-result-item {
            padding: 12px 15px;
            cursor: pointer;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            color: #333;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            direction: rtl;
        }

        .search-result-item:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .search-result-main {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .search-result-details {
            font-size: 12px;
            color: #666;
        }

        .search-loading {
            padding: 15px;
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .search-no-results {
            padding: 15px;
            text-align: center;
            color: #999;
            font-style: italic;
        }

        /* Responsive Map Modal */
        @media (max-width: 768px) {
            .map-modal-content {
                width: 95%;
                padding: 20px;
                max-height: 90vh;
            }

            .map-display {
                height: 280px;
            }

            .map-controls {
                flex-direction: column;
                gap: 8px;
            }

            .map-btn {
                padding: 10px;
                font-size: 13px;
                min-height: 40px;
            }
        }

        @media (max-width: 480px) {
            .map-modal-content {
                width: 95%;
                padding: 15px;
                border-radius: 20px;
                max-height: 85vh;
            }

            .map-display {
                height: 250px;
            }

            .map-search-input {
                font-size: 16px; /* Prevent zoom on iOS */
            }

            .map-btn {
                min-height: 38px;
                font-size: 12px;
            }
        }

        .card-instruction {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
        }

        .status-message {
            font-size: 14px;
            margin-top: 10px;
            padding: 8px;
            border-radius: 8px;
            display: none;
        }

        .status-message.success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }

        .status-message.error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }

        .gps-container {
            margin: 15px 0;
        }

        .gps-button {
            width: 100%;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .gps-button:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: #667eea;
        }

        .gps-button.loading {
            background: rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .gps-button.success {
            background: rgba(40, 167, 69, 0.2);
            border-color: #28a745;
            color: #28a745;
        }

        .location-info {
            margin-top: 10px;
            padding: 10px;
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid #28a745;
            border-radius: 8px;
            font-size: 12px;
            color: #28a745;
            display: none;
        }

        .terms-container {
            text-align: right;
            font-size: 14px;
            line-height: 1.6;
        }

        .terms-scroll {
            max-height: 250px;
            overflow-y: auto;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            margin-bottom: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .terms-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .terms-scroll::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .terms-scroll::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .terms-section {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .terms-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .terms-section h5 {
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: 600;
        }

        .terms-section p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
            line-height: 1.5;
        }

        .agreement-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .agreement-checkbox {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            cursor: pointer;
            position: relative;
        }

        .agreement-checkbox input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            position: relative;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .agreement-checkbox input[type="checkbox"]:checked + .checkmark {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-color: #667eea;
        }

        .agreement-checkbox input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 14px;
            font-weight: bold;
        }

        .agreement-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            line-height: 1.4;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .register-content {
                max-width: 90%;
                padding: 25px;
            }
            
            .app-title {
                font-size: 28px;
            }
            
            .step {
                width: 25px;
                height: 25px;
                font-size: 12px;
            }
            
            .step-line {
                width: 30px;
            }
        }

        @media (max-width: 480px) {
            .register-content {
                padding: 20px;
            }
            
            .navigation-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Register Screen -->
    <div class="register-screen">
        <div class="register-content">
            <!-- App Title -->
            <h1 class="app-title">إنشاء حساب</h1>
            
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step-line" id="line1"></div>
                <div class="step" id="step2">2</div>
                <div class="step-line" id="line2"></div>
                <div class="step" id="step3">3</div>
                <div class="step-line" id="line3"></div>
                <div class="step" id="step4">4</div>
                <div class="step-line" id="line4"></div>
                <div class="step" id="step5">5</div>
            </div>
            
            <!-- Step 1: Personal Information -->
            <div class="step-content active" id="content1">
                <div class="step-title">المعلومات الشخصية</div>
                
                <div class="form-group">
                    <input type="text" class="form-input" placeholder="الاسم الأول (كما في الهوية)">
                </div>

                <div class="form-group">
                    <input type="text" class="form-input" placeholder="الاسم الثاني (كما في الهوية)">
                </div>

                <div class="form-group">
                    <input type="email" class="form-input" placeholder="البريد الإلكتروني">
                </div>

                <div class="form-group">
                    <input type="password" class="form-input" placeholder="كلمة المرور">
                </div>

                <div class="form-group">
                    <input type="date" class="form-input" placeholder="تاريخ الميلاد (كما في الهوية)">
                </div>
                
                <div class="form-group">
                    <div class="gender-group">
                        <div class="gender-option" onclick="selectGender('male')">
                            <i class="fas fa-mars"></i> ذكر
                        </div>
                        <div class="gender-option" onclick="selectGender('female')">
                            <i class="fas fa-venus"></i> أنثى
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Additional Information -->
            <div class="step-content" id="content2">
                <div class="step-title">معلومات إضافية</div>

                <div class="form-group">
                    <select class="form-select">
                        <option value="">نوع المستخدم</option>
                        <option value="consumer">مستهلك (مشتري)</option>
                        <option value="seller">بائع</option>
                        <option value="company">شركة</option>
                        <option value="office">مكتب</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="map-container">
                        <div class="map-preview" id="mapPreview" onclick="openMapModal()">
                            <div class="map-placeholder" id="mapPlaceholder">
                                <i class="fas fa-map-marker-alt" style="font-size: 24px; margin-bottom: 6px; display: block;"></i>
                                اضغط لتحديد موقعك على الخريطة
                                <br><small style="opacity: 0.7; font-size: 12px;">(اختياري - يمكن إضافته لاحقاً)</small>
                            </div>
                        </div>
                        <div class="location-info" id="locationInfo" style="display: none;">
                            <i class="fas fa-check-circle"></i>
                            تم تحديد موقعك بنجاح
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <input type="email" class="form-input" placeholder="بريد إلكتروني إضافي (اختياري)">
                </div>

                <div class="form-group">
                    <input type="text" class="form-input" placeholder="حساب اجتماعي إضافي (اختياري)">
                </div>
            </div>

            <!-- Step 3: Photo Capture -->
            <div class="step-content" id="content3">
                <div class="step-title">التقاط صورة الهوية</div>

                <div class="id-capture-container">
                    <!-- Camera Preview -->
                    <div class="id-camera-preview" id="idCameraPreview">
                        <video id="idCameraVideo" autoplay playsinline></video>
                        <canvas id="idCameraCanvas" style="display: none;"></canvas>

                        <!-- ID Frame Overlay -->
                        <div class="id-frame-overlay">
                            <div class="id-frame">
                                <div class="id-corner top-left"></div>
                                <div class="id-corner top-right"></div>
                                <div class="id-corner bottom-left"></div>
                                <div class="id-corner bottom-right"></div>
                            </div>
                        </div>

                        <!-- Capture Button -->
                        <div class="capture-button-container" id="captureButtonContainer" style="display: none;">
                            <button class="capture-button" id="captureButton" onclick="captureIdPhoto()">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>

                        <!-- Success Indicator -->
                        <div class="capture-success" id="captureSuccess" style="display: none;">
                            <div class="success-balloon">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="id-instructions" id="idInstructions">
                        <div class="instruction-text" id="instructionText">
                            ضع الوجه الأمامي للهوية داخل الإطار
                        </div>
                        <div class="instruction-subtext" id="instructionSubtext">
                            تأكد من وضوح جميع البيانات والصورة
                        </div>

                    </div>

                    <!-- Progress Indicator -->
                    <div class="capture-progress" id="captureProgress">
                        <div class="progress-step active" id="frontStep">
                            <div class="progress-icon">
                                <i class="fas fa-id-card"></i>
                            </div>
                            <div class="progress-text">الوجه الأمامي</div>
                        </div>
                        <div class="progress-step" id="backStep">
                            <div class="progress-icon">
                                <i class="fas fa-id-card-alt"></i>
                            </div>
                            <div class="progress-text">الوجه الخلفي</div>
                        </div>
                    </div>

                    <!-- Captured Images Preview -->
                    <div class="captured-images" id="capturedImages" style="display: none;">
                        <div class="captured-image" id="frontImagePreview">
                            <img id="frontImage" src="" alt="الوجه الأمامي">
                            <div class="image-label">الوجه الأمامي</div>
                        </div>
                        <div class="captured-image" id="backImagePreview">
                            <img id="backImage" src="" alt="الوجه الخلفي">
                            <div class="image-label">الوجه الخلفي</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Terms & Agreement -->
            <div class="step-content" id="content4">
                <div class="step-title">الشروط والأحكام</div>

                <div class="terms-container">
                    <div class="terms-scroll">
                        <h4 style="color: rgba(255, 255, 255, 0.9); margin-bottom: 10px; text-align: center;">سياسة البيانات والشروط</h4>

                        <div class="terms-section">
                            <h5>حماية البيانات</h5>
                            <p>نحن نحترم خصوصيتك ونلتزم بحماية بياناتك الشخصية وفقاً لأعلى معايير الأمان.</p>
                        </div>

                        <div class="terms-section">
                            <h5>منع البيانات الخاطئة</h5>
                            <p>يُمنع منعاً باتاً إدخال بيانات خاطئة أو مضللة. سيتم إلغاء الحساب فوراً في حالة اكتشاف ذلك.</p>
                        </div>

                        <div class="terms-section">
                            <h5>منع الاحتيال</h5>
                            <p>أي محاولة احتيال أو خداع للمستخدمين ستؤدي إلى الحظر الدائم واتخاذ الإجراءات القانونية.</p>
                        </div>

                        <div class="terms-section">
                            <h5>الإجراءات القانونية</h5>
                            <p>يحق للإدارة اتخاذ أي إجراء قانوني ضد المخالفين لشروط الاستخدام.</p>
                        </div>

                        <div class="terms-section">
                            <h5>التزامات المستخدم</h5>
                            <p>يلتزم المستخدم بالتعامل بأخلاق عالية واحترام الآخرين وعدم نشر محتوى غير لائق.</p>
                        </div>
                    </div>

                    <div class="agreement-section">
                        <label class="agreement-checkbox">
                            <input type="checkbox" id="agreeTerms">
                            <span class="checkmark"></span>
                            <span class="agreement-text">أوافق على جميع الشروط والأحكام المذكورة أعلاه</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Step 5: Data Summary -->
            <div class="step-content" id="content5">
                <div class="step-title">مراجعة البيانات المدخلة</div>

                <div class="summary-container" id="summaryContainer">
                    <!-- Data will be populated here -->

                    <!-- Summary Control Panel -->
                    <div class="summary-control-panel">
                        <div class="control-group">
                            <button class="control-btn" id="summaryExpandBtn" onclick="toggleSummarySize()" title="تكبير/تصغير المعلومات">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="download-section">
                    <span class="download-text" onclick="downloadSummary()">
                        <i class="fas fa-download"></i>
                        تحميل البيانات
                    </span>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="navigation-buttons">
                <button class="nav-button" id="prevBtn" onclick="prevStep()" disabled>
                    <i class="fas fa-arrow-right"></i> السابق
                </button>
                <button class="nav-button" id="nextBtn" onclick="nextStep()">
                    التالي <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Fullscreen Map Modal -->
    <div class="fullscreen-map-modal" id="fullscreenMapModal">
        <div class="fullscreen-map-content">
            <div class="fullscreen-map-header">
                <div class="fullscreen-map-title">
                    <i class="fas fa-map-marker-alt"></i>
                    تحديد موقعك على الخريطة
                </div>
                <button class="fullscreen-close-btn" onclick="closeFullscreenMap()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="fullscreen-map-display" id="fullscreenMapDisplay">
                <div id="fullscreenLeafletMap" style="width: 100%; height: 100%;"></div>

                <!-- Fullscreen Map Control Panel -->
                <div class="fullscreen-control-panel">
                    <div class="control-group">
                        <button class="control-btn" id="fullscreenMapTypeBtn" onclick="toggleFullscreenMapType()" title="تغيير نوع الخريطة">
                            <i class="fas fa-layer-group"></i>
                        </button>
                    </div>

                    <div class="control-group">
                        <button class="control-btn" onclick="fullscreenZoomIn()" title="تكبير">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="control-btn" onclick="fullscreenZoomOut()" title="تصغير">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="fullscreen-map-controls">
                <button class="fullscreen-gps-btn" id="fullscreenGpsBtn" onclick="getFullscreenCurrentLocation()" title="موقعي الحالي">
                    <i class="fas fa-map-marker-alt"></i>
                </button>
                <button class="fullscreen-map-btn primary large" onclick="saveFullscreenLocation()" id="saveFullscreenLocationBtn" disabled>
                    حفظ الموقع
                </button>
            </div>
        </div>
    </div>



    <!-- Map Modal -->
    <div class="map-modal" id="mapModal">
        <div class="map-modal-content">
            <div class="map-modal-header">
                <div class="map-modal-title">
                    <i class="fas fa-map-marker-alt"></i>
                    تحديد موقعك على الخريطة
                </div>
                <button class="map-close-btn" onclick="closeMapModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Search Bar -->
            <div class="map-search-container">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="mapSearchInput" class="map-search-input" placeholder="ابحث عن مدينة، دولة، أو منطقة..." autocomplete="off">
                    <button class="search-clear-btn" id="searchClearBtn" onclick="clearSearch()" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="search-results" id="searchResults" style="display: none;"></div>
            </div>

            <div class="map-display" id="mapDisplay">
                <!-- Leaflet Map will be loaded here -->
                <div id="leafletMap" style="width: 100%; height: 100%; border-radius: 15px;"></div>

                <!-- Map Control Panel -->
                <div class="map-control-panel">
                    <!-- Map Type Selector -->
                    <div class="control-group">
                        <button class="control-btn" id="mapTypeBtn" onclick="toggleMapType()" title="تغيير نوع الخريطة">
                            <i class="fas fa-layer-group"></i>
                        </button>
                    </div>

                    <!-- Zoom Controls -->
                    <div class="control-group">
                        <button class="control-btn" onclick="zoomIn()" title="تكبير">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="control-btn" onclick="zoomOut()" title="تصغير">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>

                    <!-- Fullscreen Controls -->
                    <div class="control-group">
                        <button class="control-btn" id="fullscreenBtn" onclick="toggleFullscreen()" title="ملء الشاشة">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>

                <!-- Loading indicator -->
                <div id="mapLoading" style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.1);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 16px;
                    backdrop-filter: blur(10px);
                    z-index: 1000;
                ">
                    <div style="text-align: center;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>
                        جاري تحميل الخريطة...
                    </div>
                </div>
            </div>





            <div class="map-controls">
                <button class="gps-location-btn" id="gpsLocationBtn" onclick="getCurrentLocationAndSave()" title="موقعي الحالي">
                    <i class="fas fa-map-marker-alt"></i>
                </button>
                <button class="map-btn primary" onclick="saveLocation()" id="saveLocationBtn" disabled>
                    حفظ
                </button>
                <button class="map-btn primary" onclick="skipToNextStep()">
                    التالي <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let selectedGender = '';
        let userLocation = null;
        let cameraStream = null;
        let currentCameraMode = null;
        let selectedMapLocation = null;

        // Leaflet Map variables
        let map = null;
        let marker = null;
        let isMapInitialized = true; // Leaflet doesn't need callback
        let searchTimeout = null;
        let streetLayer = null;
        let satelliteLayer = null;

        // Fullscreen Map variables
        let fullscreenMap = null;
        let fullscreenMarker = null;
        let fullscreenStreetLayer = null;
        let fullscreenSatelliteLayer = null;
        let fullscreenCurrentMapType = 'streets';

        function selectGender(gender) {
            selectedGender = gender;
            document.querySelectorAll('.gender-option').forEach(option => {
                option.classList.remove('selected');
            });
            event.target.closest('.gender-option').classList.add('selected');
        }

        function getLocationGPS() {
            const button = document.getElementById('gpsButton');
            const icon = document.getElementById('gpsIcon');
            const text = document.getElementById('gpsText');
            const locationInfo = document.getElementById('locationInfo');

            if (userLocation) {
                // Reset location
                userLocation = null;
                button.className = 'gps-button';
                icon.className = 'fas fa-map-marker-alt';
                text.textContent = 'تحديد الموقع من GPS';
                locationInfo.style.display = 'none';
                return;
            }

            if (!navigator.geolocation) {
                text.textContent = 'المتصفح لا يدعم GPS';
                return;
            }

            // Show loading state
            button.className = 'gps-button loading';
            icon.className = 'fas fa-spinner fa-spin';
            text.textContent = 'جاري تحديد الموقع...';

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    userLocation = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy
                    };

                    // Show success state
                    button.className = 'gps-button success';
                    icon.className = 'fas fa-check-circle';
                    text.textContent = 'تم تحديد الموقع بنجاح';

                    // Show location info
                    locationInfo.innerHTML = `
                        <strong>الإحداثيات:</strong><br>
                        خط العرض: ${userLocation.latitude.toFixed(6)}<br>
                        خط الطول: ${userLocation.longitude.toFixed(6)}<br>
                        دقة التحديد: ${Math.round(userLocation.accuracy)} متر
                    `;
                    locationInfo.style.display = 'block';

                    // Open Google Maps in new tab
                    const mapsUrl = `https://www.google.com/maps?q=${userLocation.latitude},${userLocation.longitude}`;
                    window.open(mapsUrl, '_blank');
                },
                function(error) {
                    // Show error state
                    button.className = 'gps-button';
                    icon.className = 'fas fa-exclamation-triangle';

                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            text.textContent = 'تم رفض الوصول للموقع';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            text.textContent = 'الموقع غير متاح';
                            break;
                        case error.TIMEOUT:
                            text.textContent = 'انتهت مهلة تحديد الموقع';
                            break;
                        default:
                            text.textContent = 'خطأ في تحديد الموقع';
                            break;
                    }

                    // Reset after 3 seconds
                    setTimeout(() => {
                        button.className = 'gps-button';
                        icon.className = 'fas fa-map-marker-alt';
                        text.textContent = 'تحديد الموقع من GPS';
                    }, 3000);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 60000
                }
            );
        }

        function getLocation() {
            // Legacy function - redirect to new function
            getLocationGPS();
        }

        // Enhanced Map Initialization with Google-like appearance
        function loadLeafletMap() {
            const mapContainer = document.getElementById('leafletMap');
            const loadingIndicator = document.getElementById('mapLoading');

            try {
                // Clear any existing map
                if (map) {
                    map.remove();
                }

                // Default location (Baghdad, Iraq)
                const defaultLocation = [33.3152, 44.3661];

                // Initialize map with enhanced options
                map = L.map(mapContainer, {
                    center: defaultLocation,
                    zoom: 12,
                    zoomControl: false, // Remove default zoom control
                    attributionControl: false, // Remove attribution for cleaner look
                    scrollWheelZoom: true,
                    doubleClickZoom: true,
                    boxZoom: true,
                    keyboard: true,
                    dragging: true,
                    touchZoom: true
                });

                // Add Google-like satellite/hybrid tile layer
                satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: '',
                    maxZoom: 19,
                    id: 'satellite'
                });

                // Add street map layer (Google-like)
                streetLayer = L.tileLayer('https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png', {
                    attribution: '',
                    maxZoom: 19,
                    id: 'streets'
                });

                // Add default layer (streets)
                streetLayer.addTo(map);

                // Create enhanced custom marker
                const customIcon = L.divIcon({
                    className: 'custom-location-marker',
                    html: `
                        <div class="marker-pin">
                            <div class="marker-pulse"></div>
                            <div class="marker-dot"></div>
                        </div>
                    `,
                    iconSize: [40, 40],
                    iconAnchor: [20, 40]
                });

                // Create marker
                marker = L.marker(defaultLocation, {
                    icon: customIcon,
                    draggable: true,
                    title: 'موقعك المحدد'
                }).addTo(map);

                // Add popup to marker
                marker.bindPopup(`
                    <div style="text-align: center; font-family: 'Cairo', sans-serif; direction: rtl;">
                        <strong>📍 موقعك المحدد</strong><br>
                        <small>يمكنك سحب العلامة لتغيير الموقع</small>
                    </div>
                `);

                // Add click listener to map
                map.on('click', function(e) {
                    setMarkerPosition(e.latlng);
                });

                // Add drag listener to marker
                marker.on('dragend', function(e) {
                    setMarkerPosition(e.target.getLatLng());
                });

                // Add zoom listener for better UX
                map.on('zoomend', function() {
                    console.log('Zoom level:', map.getZoom());
                });

                // Force map to resize and refresh multiple times for reliability
                setTimeout(() => {
                    if (map) {
                        map.invalidateSize();
                        map.setView(defaultLocation, 12);
                    }
                }, 100);

                setTimeout(() => {
                    if (map) {
                        map.invalidateSize();
                    }
                    loadingIndicator.style.display = 'none';
                }, 800);

                // Enable save button
                document.getElementById('saveLocationBtn').disabled = false;

                // Set initial location
                selectedMapLocation = {
                    coordinates: {
                        lat: defaultLocation[0],
                        lng: defaultLocation[1]
                    }
                };

                console.log('خريطة محسنة تم تحميلها بنجاح');

                // Initialize search functionality
                initializeSearch();

                // Initialize draggable control panel
                initializeDraggablePanel();

            } catch (error) {
                console.error('خطأ في تحميل الخريطة:', error);
                loadingIndicator.innerHTML = `
                    <div style="text-align: center; color: white;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px; display: block; color: #ff6b6b;"></i>
                        <div style="margin-bottom: 15px;">خطأ في تحميل الخريطة</div>
                        <button onclick="retryLoadMap()" style="
                            background: linear-gradient(135deg, #667eea, #764ba2);
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-family: 'Cairo', sans-serif;
                            font-weight: bold;
                        ">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        }

        // Retry function
        function retryLoadMap() {
            const loadingIndicator = document.getElementById('mapLoading');
            loadingIndicator.innerHTML = `
                <div style="text-align: center;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>
                    جاري تحميل الخريطة...
                </div>
            `;
            loadingIndicator.style.display = 'flex';

            setTimeout(() => {
                loadLeafletMap();
            }, 1000);
        }

        // Search Functions
        function initializeSearch() {
            const searchInput = document.getElementById('mapSearchInput');
            const searchResults = document.getElementById('searchResults');
            const clearBtn = document.getElementById('searchClearBtn');

            searchInput.addEventListener('input', function(e) {
                const query = e.target.value.trim();

                if (query.length > 0) {
                    clearBtn.style.display = 'flex';

                    // Clear previous timeout
                    if (searchTimeout) {
                        clearTimeout(searchTimeout);
                    }

                    // Search after 500ms delay
                    searchTimeout = setTimeout(() => {
                        performSearch(query);
                    }, 500);
                } else {
                    clearBtn.style.display = 'none';
                    hideSearchResults();
                }
            });

            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const query = e.target.value.trim();
                    if (query.length > 0) {
                        performSearch(query);
                    }
                }
            });

            // Hide results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.map-search-container')) {
                    hideSearchResults();
                }
            });
        }

        function performSearch(query) {
            const searchResults = document.getElementById('searchResults');

            // Show loading
            searchResults.innerHTML = `
                <div class="search-loading">
                    <i class="fas fa-spinner fa-spin"></i> جاري البحث...
                </div>
            `;
            searchResults.style.display = 'block';

            // Use Nominatim API for geocoding (free OpenStreetMap service)
            const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=5&accept-language=ar,en`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    displaySearchResults(data);
                })
                .catch(error => {
                    console.error('خطأ في البحث:', error);
                    searchResults.innerHTML = `
                        <div class="search-no-results">
                            خطأ في البحث. يرجى المحاولة مرة أخرى.
                        </div>
                    `;
                });
        }

        function displaySearchResults(results) {
            const searchResults = document.getElementById('searchResults');

            if (results.length === 0) {
                searchResults.innerHTML = `
                    <div class="search-no-results">
                        لم يتم العثور على نتائج
                    </div>
                `;
                return;
            }

            let html = '';
            results.forEach(result => {
                const displayName = result.display_name;
                const parts = displayName.split(',');
                const mainName = parts[0];
                const details = parts.slice(1, 3).join(', ');

                html += `
                    <div class="search-result-item" onclick="selectSearchResult(${result.lat}, ${result.lon}, '${mainName.replace(/'/g, "\\'")}')">
                        <div class="search-result-main">${mainName}</div>
                        <div class="search-result-details">${details}</div>
                    </div>
                `;
            });

            searchResults.innerHTML = html;
        }

        function selectSearchResult(lat, lon, name) {
            if (map && marker) {
                const newLocation = [lat, lon];

                // Update map and marker
                map.setView(newLocation, 14);
                marker.setLatLng(newLocation);

                // Update popup
                marker.bindPopup(`
                    <div style="text-align: center; font-family: 'Cairo', sans-serif; direction: rtl;">
                        <strong>📍 ${name}</strong><br>
                        <small>موقعك المحدد</small>
                    </div>
                `).openPopup();

                // Store location
                selectedMapLocation = {
                    coordinates: {
                        lat: lat,
                        lng: lon
                    },
                    name: name
                };

                // Enable save button
                document.getElementById('saveLocationBtn').disabled = false;

                // Hide search results
                hideSearchResults();

                console.log('تم تحديد الموقع:', name, lat, lon);
            }
        }

        function clearSearch() {
            const searchInput = document.getElementById('mapSearchInput');
            const clearBtn = document.getElementById('searchClearBtn');

            searchInput.value = '';
            clearBtn.style.display = 'none';
            hideSearchResults();
            searchInput.focus();
        }

        function hideSearchResults() {
            const searchResults = document.getElementById('searchResults');
            searchResults.style.display = 'none';
        }

        // Map Control Functions
        let currentMapType = 'streets';
        let isMapExpanded = false;

        function toggleMapType() {
            const mapTypeBtn = document.getElementById('mapTypeBtn');

            if (currentMapType === 'streets') {
                // Switch to satellite
                if (map && satelliteLayer) {
                    map.removeLayer(streetLayer);
                    map.addLayer(satelliteLayer);
                    currentMapType = 'satellite';
                    mapTypeBtn.classList.add('active');
                    mapTypeBtn.innerHTML = '<i class="fas fa-satellite"></i>';
                }
            } else {
                // Switch to streets
                if (map && streetLayer) {
                    map.removeLayer(satelliteLayer);
                    map.addLayer(streetLayer);
                    currentMapType = 'streets';
                    mapTypeBtn.classList.remove('active');
                    mapTypeBtn.innerHTML = '<i class="fas fa-layer-group"></i>';
                }
            }
        }

        function zoomIn() {
            if (map) {
                map.zoomIn();
            }
        }

        function zoomOut() {
            if (map) {
                map.zoomOut();
            }
        }

        function toggleFullscreen() {
            const fullscreenBtn = document.getElementById('fullscreenBtn');
            const fullscreenModal = document.getElementById('fullscreenMapModal');

            // Open fullscreen map
            fullscreenModal.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Update button state
            fullscreenBtn.classList.add('active');
            fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';

            // Initialize fullscreen map
            setTimeout(() => {
                initializeFullscreenMap();
            }, 300);
        }

        function closeFullscreenMap() {
            const fullscreenBtn = document.getElementById('fullscreenBtn');
            const fullscreenModal = document.getElementById('fullscreenMapModal');

            // Close fullscreen map
            fullscreenModal.classList.remove('active');
            document.body.style.overflow = '';

            // Reset button state
            fullscreenBtn.classList.remove('active');
            fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';

            // Clean up fullscreen map
            if (fullscreenMap) {
                fullscreenMap.remove();
                fullscreenMap = null;
            }
        }

        // Fullscreen Map Functions
        function initializeFullscreenMap() {
            const mapContainer = document.getElementById('fullscreenLeafletMap');

            try {
                // Get current location from main map or default
                const currentLocation = selectedMapLocation ?
                    [selectedMapLocation.coordinates.lat, selectedMapLocation.coordinates.lng] :
                    [33.3152, 44.3661];

                // Initialize fullscreen map
                fullscreenMap = L.map(mapContainer, {
                    center: currentLocation,
                    zoom: 12,
                    zoomControl: false,
                    attributionControl: false,
                    scrollWheelZoom: true,
                    doubleClickZoom: true,
                    boxZoom: true,
                    keyboard: true,
                    dragging: true,
                    touchZoom: true
                });

                // Add layers
                fullscreenStreetLayer = L.tileLayer('https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png', {
                    attribution: '',
                    maxZoom: 19,
                    id: 'streets'
                });

                fullscreenSatelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: '',
                    maxZoom: 19,
                    id: 'satellite'
                });

                // Add default layer
                fullscreenStreetLayer.addTo(fullscreenMap);

                // Create marker
                const customIcon = L.divIcon({
                    className: 'custom-location-marker',
                    html: `
                        <div class="marker-pin">
                            <div class="marker-pulse"></div>
                            <div class="marker-dot"></div>
                        </div>
                    `,
                    iconSize: [40, 40],
                    iconAnchor: [20, 40]
                });

                fullscreenMarker = L.marker(currentLocation, {
                    icon: customIcon,
                    draggable: true,
                    title: 'موقعك المحدد'
                }).addTo(fullscreenMap);

                // Add click listener
                fullscreenMap.on('click', function(e) {
                    setFullscreenMarkerPosition(e.latlng);
                });

                // Add drag listener
                fullscreenMarker.on('dragend', function(e) {
                    setFullscreenMarkerPosition(e.target.getLatLng());
                });

                // Enable save button
                document.getElementById('saveFullscreenLocationBtn').disabled = false;

                console.log('خريطة ملء الشاشة تم تحميلها بنجاح');

                // Initialize draggable panel for fullscreen
                setTimeout(() => {
                    initializeDraggablePanel();
                }, 100);

            } catch (error) {
                console.error('خطأ في تحميل خريطة ملء الشاشة:', error);
            }
        }

        function setFullscreenMarkerPosition(latlng) {
            if (fullscreenMarker) {
                fullscreenMarker.setLatLng(latlng);

                selectedMapLocation = {
                    coordinates: {
                        lat: latlng.lat,
                        lng: latlng.lng
                    }
                };

                document.getElementById('saveFullscreenLocationBtn').disabled = false;
            }
        }

        function toggleFullscreenMapType() {
            const mapTypeBtn = document.getElementById('fullscreenMapTypeBtn');

            if (fullscreenCurrentMapType === 'streets') {
                if (fullscreenMap && fullscreenSatelliteLayer) {
                    fullscreenMap.removeLayer(fullscreenStreetLayer);
                    fullscreenMap.addLayer(fullscreenSatelliteLayer);
                    fullscreenCurrentMapType = 'satellite';
                    mapTypeBtn.classList.add('active');
                    mapTypeBtn.innerHTML = '<i class="fas fa-satellite"></i>';
                }
            } else {
                if (fullscreenMap && fullscreenStreetLayer) {
                    fullscreenMap.removeLayer(fullscreenSatelliteLayer);
                    fullscreenMap.addLayer(fullscreenStreetLayer);
                    fullscreenCurrentMapType = 'streets';
                    mapTypeBtn.classList.remove('active');
                    mapTypeBtn.innerHTML = '<i class="fas fa-layer-group"></i>';
                }
            }
        }

        function fullscreenZoomIn() {
            if (fullscreenMap) {
                fullscreenMap.zoomIn();
            }
        }

        function fullscreenZoomOut() {
            if (fullscreenMap) {
                fullscreenMap.zoomOut();
            }
        }

        function getFullscreenCurrentLocation() {
            const gpsBtn = document.getElementById('fullscreenGpsBtn');

            if (!navigator.geolocation) {
                alert('متصفحك لا يدعم تحديد الموقع الجغرافي.');
                return;
            }

            gpsBtn.classList.add('loading');
            gpsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const currentLocation = [position.coords.latitude, position.coords.longitude];

                    if (fullscreenMap && fullscreenMarker) {
                        fullscreenMap.flyTo(currentLocation, 16, {
                            animate: true,
                            duration: 1.5
                        });
                        fullscreenMarker.setLatLng(currentLocation);

                        selectedMapLocation = {
                            coordinates: {
                                lat: position.coords.latitude,
                                lng: position.coords.longitude
                            },
                            isCurrentLocation: true
                        };

                        gpsBtn.classList.remove('loading');
                        gpsBtn.classList.add('active');
                        gpsBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i>';

                        document.getElementById('saveFullscreenLocationBtn').disabled = false;
                    }
                },
                function(error) {
                    gpsBtn.classList.remove('loading');
                    gpsBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i>';
                    alert('لا يمكن تحديد موقعك الحالي.');
                },
                {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 30000
                }
            );
        }

        function saveFullscreenLocation() {
            if (selectedMapLocation) {
                const mapPreview = document.getElementById('mapPreview');
                const mapPlaceholder = document.getElementById('mapPlaceholder');
                const locationInfo = document.getElementById('locationInfo');

                mapPlaceholder.style.display = 'none';
                mapPreview.classList.add('active');
                locationInfo.style.display = 'block';
                locationInfo.classList.add('success');

                userLocation = selectedMapLocation;

                closeFullscreenMap();
                closeMapModal();

                const successToast = document.createElement('div');
                successToast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(40, 167, 69, 0.9);
                    color: white;
                    padding: 12px 24px;
                    border-radius: 25px;
                    font-family: 'Cairo', sans-serif;
                    z-index: 10000;
                    backdrop-filter: blur(10px);
                `;
                successToast.innerHTML = '<i class="fas fa-check-circle"></i> تم حفظ موقعك بنجاح!';
                document.body.appendChild(successToast);

                setTimeout(() => {
                    if (document.body.contains(successToast)) {
                        document.body.removeChild(successToast);
                    }
                }, 3000);
            }
        }

        // Map Functions
        function openMapModal() {
            const modal = document.getElementById('mapModal');
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Ensure Leaflet is loaded before initializing map
            if (typeof L !== 'undefined') {
                // Load map after modal animation
                setTimeout(() => {
                    loadLeafletMap();
                }, 500);
            } else {
                // Fallback: reload Leaflet if not loaded
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
                script.onload = () => {
                    setTimeout(() => {
                        loadLeafletMap();
                    }, 500);
                };
                document.head.appendChild(script);
            }
        }

        function closeMapModal() {
            const modal = document.getElementById('mapModal');
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        function setMarkerPosition(latLng) {
            if (!map || !marker) return;

            // Update marker position
            marker.setLatLng(latLng);

            // Center map on new position
            map.panTo(latLng);

            // Enable save button
            document.getElementById('saveLocationBtn').disabled = false;

            // Store location
            selectedMapLocation = {
                coordinates: {
                    lat: latLng.lat,
                    lng: latLng.lng
                }
            };

            console.log('موقع محدد:', selectedMapLocation.coordinates);
        }

        function getCurrentLocationAndSave() {
            const gpsBtn = document.getElementById('gpsLocationBtn');

            if (!navigator.geolocation) {
                alert('متصفحك لا يدعم تحديد الموقع الجغرافي.');
                return;
            }

            // Update button state
            gpsBtn.classList.add('loading');
            gpsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const currentLocation = [position.coords.latitude, position.coords.longitude];

                    if (map && marker) {
                        // Update map and marker with smooth animation
                        map.flyTo(currentLocation, 16, {
                            animate: true,
                            duration: 1.5
                        });
                        marker.setLatLng(currentLocation);

                        // Get address for the location
                        getAddressFromCoordinates(position.coords.latitude, position.coords.longitude)
                            .then(address => {
                                // Update popup with address
                                marker.bindPopup(`
                                    <div style="text-align: center; font-family: 'Cairo', sans-serif; direction: rtl;">
                                        <strong>📍 موقعك الحالي</strong><br>
                                        <small>${address}</small><br>
                                        <div style="margin-top: 5px; color: #28a745;">
                                            <i class="fas fa-check-circle"></i> تم التحديد بدقة GPS
                                        </div>
                                    </div>
                                `).openPopup();

                                // Store location with address
                                selectedMapLocation = {
                                    coordinates: {
                                        lat: position.coords.latitude,
                                        lng: position.coords.longitude
                                    },
                                    address: address,
                                    accuracy: position.coords.accuracy,
                                    isCurrentLocation: true
                                };

                                // Update button state
                                gpsBtn.classList.remove('loading');
                                gpsBtn.classList.add('active');
                                gpsBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i>';

                                // Auto save and proceed
                                setTimeout(() => {
                                    autoSaveAndProceed();
                                }, 1500);
                            });
                    }
                },
                function(error) {
                    // Reset button state
                    gpsBtn.classList.remove('loading');
                    gpsBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i>';

                    let errorMessage = 'لا يمكن تحديد موقعك الحالي. ';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage += 'يرجى السماح بالوصول للموقع.';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage += 'معلومات الموقع غير متاحة.';
                            break;
                        case error.TIMEOUT:
                            errorMessage += 'انتهت مهلة تحديد الموقع.';
                            break;
                    }

                    // Show error toast
                    const errorToast = document.createElement('div');
                    errorToast.style.cssText = `
                        position: fixed;
                        top: 20px;
                        left: 50%;
                        transform: translateX(-50%);
                        background: rgba(220, 53, 69, 0.9);
                        color: white;
                        padding: 12px 24px;
                        border-radius: 25px;
                        font-family: 'Cairo', sans-serif;
                        z-index: 10000;
                        backdrop-filter: blur(10px);
                    `;
                    errorToast.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${errorMessage}`;
                    document.body.appendChild(errorToast);

                    setTimeout(() => {
                        if (document.body.contains(errorToast)) {
                            document.body.removeChild(errorToast);
                        }
                    }, 4000);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 30000
                }
            );
        }

        function autoSaveAndProceed() {
            if (selectedMapLocation) {
                // Update UI
                const mapPreview = document.getElementById('mapPreview');
                const mapPlaceholder = document.getElementById('mapPlaceholder');
                const locationInfo = document.getElementById('locationInfo');

                // Hide placeholder and show success
                mapPlaceholder.style.display = 'none';
                mapPreview.classList.add('active');
                locationInfo.style.display = 'block';
                locationInfo.classList.add('success');

                // Store location data
                userLocation = selectedMapLocation;

                // Close modal
                closeMapModal();

                // Show success toast
                const successToast = document.createElement('div');
                successToast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(40, 167, 69, 0.9);
                    color: white;
                    padding: 12px 24px;
                    border-radius: 25px;
                    font-family: 'Cairo', sans-serif;
                    z-index: 10000;
                    backdrop-filter: blur(10px);
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                `;
                successToast.innerHTML = '<i class="fas fa-check-circle"></i> تم حفظ موقعك الحالي وسيتم الانتقال للخطوة التالية';
                document.body.appendChild(successToast);

                setTimeout(() => {
                    if (document.body.contains(successToast)) {
                        document.body.removeChild(successToast);
                    }
                }, 3000);

                // Auto proceed to next step
                setTimeout(() => {
                    nextStep();
                }, 2000);
            }
        }

        function getCurrentLocation() {
            if (!navigator.geolocation) {
                alert('متصفحك لا يدعم تحديد الموقع الجغرافي.\n\nيمكنك تحديد موقعك يدوياً بالبحث أو الضغط على الخريطة.');
                return;
            }

            const saveBtn = document.getElementById('saveLocationBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تحديد موقعك...';
            saveBtn.disabled = true;

            // Show loading message
            const loadingToast = document.createElement('div');
            loadingToast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px 20px;
                border-radius: 25px;
                font-family: 'Cairo', sans-serif;
                z-index: 10000;
                backdrop-filter: blur(10px);
            `;
            loadingToast.innerHTML = '<i class="fas fa-crosshairs fa-spin"></i> جاري تحديد موقعك الحالي...';
            document.body.appendChild(loadingToast);

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const currentLocation = [position.coords.latitude, position.coords.longitude];

                    if (map && marker) {
                        // Update map and marker with smooth animation
                        map.flyTo(currentLocation, 16, {
                            animate: true,
                            duration: 1.5
                        });
                        marker.setLatLng(currentLocation);

                        // Get address for the location
                        getAddressFromCoordinates(position.coords.latitude, position.coords.longitude)
                            .then(address => {
                                // Update popup with address
                                marker.bindPopup(`
                                    <div style="text-align: center; font-family: 'Cairo', sans-serif; direction: rtl;">
                                        <strong>📍 موقعك الحالي</strong><br>
                                        <small>${address}</small><br>
                                        <div style="margin-top: 5px; color: #28a745;">
                                            <i class="fas fa-check-circle"></i> تم التحديد بدقة GPS
                                        </div>
                                    </div>
                                `).openPopup();

                                // Store location with address
                                selectedMapLocation = {
                                    coordinates: {
                                        lat: position.coords.latitude,
                                        lng: position.coords.longitude
                                    },
                                    address: address,
                                    accuracy: position.coords.accuracy,
                                    isCurrentLocation: true
                                };
                            });

                        saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ الموقع';
                        saveBtn.disabled = false;

                        // Remove loading toast
                        document.body.removeChild(loadingToast);

                        // Show success toast
                        const successToast = document.createElement('div');
                        successToast.style.cssText = loadingToast.style.cssText.replace('rgba(0, 0, 0, 0.8)', 'rgba(40, 167, 69, 0.9)');
                        successToast.innerHTML = '<i class="fas fa-check-circle"></i> تم تحديد موقعك بنجاح!';
                        document.body.appendChild(successToast);

                        setTimeout(() => {
                            if (document.body.contains(successToast)) {
                                document.body.removeChild(successToast);
                            }
                        }, 3000);
                    }
                },
                function(error) {
                    saveBtn.innerHTML = originalText;
                    saveBtn.disabled = false;

                    // Remove loading toast
                    if (document.body.contains(loadingToast)) {
                        document.body.removeChild(loadingToast);
                    }

                    let errorMessage = 'لا يمكن تحديد موقعك الحالي. ';
                    let suggestion = '';

                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage += 'تم رفض الإذن للوصول للموقع.';
                            suggestion = 'يرجى السماح بالوصول للموقع في إعدادات المتصفح والمحاولة مرة أخرى.';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage += 'معلومات الموقع غير متاحة.';
                            suggestion = 'تأكد من تفعيل خدمات الموقع في جهازك.';
                            break;
                        case error.TIMEOUT:
                            errorMessage += 'انتهت مهلة تحديد الموقع.';
                            suggestion = 'تأكد من اتصالك بالإنترنت والمحاولة مرة أخرى.';
                            break;
                    }

                    // Show error toast
                    const errorToast = document.createElement('div');
                    errorToast.style.cssText = loadingToast.style.cssText.replace('rgba(0, 0, 0, 0.8)', 'rgba(220, 53, 69, 0.9)');
                    errorToast.innerHTML = `
                        <div><i class="fas fa-exclamation-triangle"></i> ${errorMessage}</div>
                        <small style="display: block; margin-top: 5px; opacity: 0.9;">${suggestion}</small>
                        <small style="display: block; margin-top: 5px; opacity: 0.8;">يمكنك البحث عن موقعك أو تحديده يدوياً على الخريطة.</small>
                    `;
                    document.body.appendChild(errorToast);

                    setTimeout(() => {
                        if (document.body.contains(errorToast)) {
                            document.body.removeChild(errorToast);
                        }
                    }, 5000);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 20000,
                    maximumAge: 30000
                }
            );
        }

        // Get address from coordinates using reverse geocoding
        async function getAddressFromCoordinates(lat, lng) {
            try {
                const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=ar,en`);
                const data = await response.json();

                if (data && data.display_name) {
                    const parts = data.display_name.split(',');
                    return parts.slice(0, 3).join(', ');
                }
                return 'موقعك الحالي';
            } catch (error) {
                console.error('خطأ في الحصول على العنوان:', error);
                return 'موقعك الحالي';
            }
        }

        function saveLocation() {
            if (selectedMapLocation) {
                // Update UI
                const mapPreview = document.getElementById('mapPreview');
                const mapPlaceholder = document.getElementById('mapPlaceholder');
                const locationInfo = document.getElementById('locationInfo');

                // Hide placeholder and show success
                mapPlaceholder.style.display = 'none';
                mapPreview.classList.add('active');
                locationInfo.style.display = 'block';
                locationInfo.classList.add('success');

                // Store location data
                userLocation = selectedMapLocation;

                // Close modal
                closeMapModal();

                // Show confirmation
                const locationText = selectedMapLocation.isCurrentLocation ?
                    'تم حفظ موقعك الحالي' : 'تم حفظ موقعك المحدد';

                // Show success toast
                const successToast = document.createElement('div');
                successToast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(40, 167, 69, 0.9);
                    color: white;
                    padding: 12px 24px;
                    border-radius: 25px;
                    font-family: 'Cairo', sans-serif;
                    z-index: 10000;
                    backdrop-filter: blur(10px);
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                `;
                successToast.innerHTML = `<i class="fas fa-check-circle"></i> ${locationText} بنجاح!`;
                document.body.appendChild(successToast);

                setTimeout(() => {
                    if (document.body.contains(successToast)) {
                        document.body.removeChild(successToast);
                    }
                }, 3000);
            }
        }

        function skipToNextStep() {
            // Close modal without saving location
            closeMapModal();

            // Show info message
            const infoToast = document.createElement('div');
            infoToast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(102, 126, 234, 0.9);
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                font-family: 'Cairo', sans-serif;
                z-index: 10000;
                backdrop-filter: blur(10px);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            `;
            infoToast.innerHTML = '<i class="fas fa-info-circle"></i> يمكنك إضافة موقعك لاحقاً من البروفايل';
            document.body.appendChild(infoToast);

            setTimeout(() => {
                if (document.body.contains(infoToast)) {
                    document.body.removeChild(infoToast);
                }
            }, 4000);

            // Move to next step automatically
            setTimeout(() => {
                nextStep();
            }, 1000);
        }

        function toggleCamera(mode) {
            currentCameraMode = mode;
            const video = document.getElementById('cameraVideo');
            const placeholder = document.getElementById('cameraPlaceholder');
            const preview = document.getElementById('cameraPreview');
            const instruction = document.getElementById('cardInstruction');
            const statusMessage = document.getElementById('statusMessage');

            // Update active button
            document.querySelectorAll('.camera-btn').forEach(btn => btn.classList.remove('active'));
            event.target.closest('.camera-btn').classList.add('active');

            if (cameraStream) {
                // Stop current stream
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
                video.style.display = 'none';
                placeholder.style.display = 'block';
                preview.className = 'camera-preview';
                statusMessage.style.display = 'none';
                instruction.textContent = 'اضغط على أيقونة الكاميرا لبدء التصوير';
                return;
            }

            // Start camera
            navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: mode === 'back' ? 'environment' : 'user',
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            })
            .then(stream => {
                cameraStream = stream;
                video.srcObject = stream;
                video.style.display = 'block';
                placeholder.style.display = 'none';

                const cardType = mode === 'front' ? 'الوجه الأمامي' : 'الوجه الخلفي';
                instruction.textContent = `ضع ${cardType} للهوية داخل الإطار`;

                // Simulate card detection after 3 seconds
                setTimeout(() => {
                    simulateCardDetection();
                }, 3000);
            })
            .catch(err => {
                console.error('خطأ في الوصول للكاميرا:', err);
                alert('لا يمكن الوصول للكاميرا. يرجى التأكد من السماح بالوصول للكاميرا في إعدادات المتصفح.');
            });
        }

        function simulateCardDetection() {
            const preview = document.getElementById('cameraPreview');
            const statusMessage = document.getElementById('statusMessage');
            const instruction = document.getElementById('cardInstruction');

            // Simulate random success/error (70% success rate)
            const isCorrect = Math.random() > 0.3;

            if (isCorrect) {
                // Success
                preview.className = 'camera-preview success';
                statusMessage.className = 'status-message success';
                statusMessage.textContent = '✅ تم وضع الكارت بصورة صحيحة';
                statusMessage.style.display = 'block';
                instruction.textContent = 'ممتاز! يمكنك الضغط على الكاميرا مرة أخرى لإعادة التصوير أو المتابعة للخطوة التالية';
            } else {
                // Error
                preview.className = 'camera-preview error';
                statusMessage.className = 'status-message error';
                statusMessage.textContent = '❌ يرجى وضع الكارت بصورة صحيحة';
                statusMessage.style.display = 'block';
                instruction.textContent = 'تأكد من وضع الهوية بشكل مستقيم وواضح داخل الإطار';

                // Auto retry after 2 seconds
                setTimeout(() => {
                    simulateCardDetection();
                }, 2000);
            }
        }

        function openCamera() {
            // Legacy function - kept for compatibility
            alert('استخدم أيقونات الكاميرا الجديدة لالتقاط الصور');
        }

        function nextStep() {
            if (currentStep < 4) {
                if (validateCurrentStep()) {
                    // Hide current step
                    document.getElementById(`content${currentStep}`).classList.remove('active');
                    document.getElementById(`step${currentStep}`).classList.remove('active');
                    document.getElementById(`step${currentStep}`).classList.add('completed');

                    if (currentStep < 4) {
                        document.getElementById(`line${currentStep}`).classList.add('completed');
                    }

                    // Show next step
                    currentStep++;
                    document.getElementById(`content${currentStep}`).classList.add('active');
                    document.getElementById(`step${currentStep}`).classList.add('active');

                    // Update buttons
                    updateButtons();
                }
            } else {
                // Final step - show summary and complete registration
                showSummary();
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                // Hide current step
                document.getElementById(`content${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');

                // Show previous step
                currentStep--;
                document.getElementById(`content${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.remove('completed');
                document.getElementById(`step${currentStep}`).classList.add('active');

                if (currentStep < 4) {
                    document.getElementById(`line${currentStep}`).classList.remove('completed');
                }

                // Update buttons
                updateButtons();
            }
        }

        function updateButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            // Previous button disabled only on first step
            prevBtn.disabled = currentStep === 1;

            // Next button always enabled for demo purposes
            nextBtn.disabled = false;
            nextBtn.style.opacity = '1';
            nextBtn.title = '';

            if (currentStep === 5) {
                nextBtn.innerHTML = 'تسجيل الدخول <i class="fas fa-sign-in-alt"></i>';
                nextBtn.onclick = function() { goToLogin(); };
            } else {
                nextBtn.innerHTML = 'التالي <i class="fas fa-arrow-left"></i>';
                nextBtn.onclick = function() { nextStep(); };
            }
        }

        function validateCurrentStep() {
            // للعرض فقط - لا يوجد تحقق من البيانات
            return true;
        }

        function showSummary() {
            alert('🎉 عرض توضيحي للتطبيق\n\nتم إكمال جميع مراحل إنشاء الحساب بنجاح!\n\n✅ المرحلة الأولى: المعلومات الشخصية\n✅ المرحلة الثانية: معلومات إضافية\n✅ المرحلة الثالثة: التقاط صورة الهوية\n✅ المرحلة الرابعة: الشروط والأحكام\n\nسيتم توجيهك إلى شاشة تسجيل الدخول...');
        }

        function collectFormData() {
            const inputs = document.querySelectorAll('.form-input, .form-select');
            const data = {};

            inputs.forEach((input, index) => {
                if (input.value) {
                    switch(index) {
                        case 0: data.firstName = input.value; break;
                        case 1: data.lastName = input.value; break;
                        case 2: data.email = input.value; break;
                        case 3: data.password = input.value; break;
                        case 4: data.birthDate = input.value; break;
                        case 5: data.userType = input.value; break;
                        case 6: data.additionalEmail = input.value; break;
                        case 7: data.socialAccount = input.value; break;
                    }
                }
            });

            data.gender = selectedGender;
            data.location = userLocation;
            data.mapLocation = selectedMapLocation;

            return data;
        }

        // Draggable Panel Functions
        function initializeDraggablePanel() {
            const panel = document.querySelector('.map-control-panel');
            const fullscreenPanel = document.querySelector('.fullscreen-control-panel');

            if (panel) {
                makeDraggable(panel, document.getElementById('mapDisplay'));
            }

            if (fullscreenPanel) {
                makeDraggable(fullscreenPanel, document.getElementById('fullscreenMapDisplay'));
            }
        }

        function makeDraggable(element, container) {
            let isDragging = false;
            let startX, startY, startLeft, startTop;

            element.addEventListener('mousedown', function(e) {
                isDragging = true;
                element.classList.add('dragging');

                startX = e.clientX;
                startY = e.clientY;

                const rect = element.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();

                startLeft = rect.left - containerRect.left;
                startTop = rect.top - containerRect.top;

                e.preventDefault();
            });

            document.addEventListener('mousemove', function(e) {
                if (!isDragging) return;

                const containerRect = container.getBoundingClientRect();
                const elementRect = element.getBoundingClientRect();

                let newLeft = startLeft + (e.clientX - startX);
                let newTop = startTop + (e.clientY - startY);

                // Constrain to container bounds
                const maxLeft = containerRect.width - elementRect.width;
                const maxTop = containerRect.height - elementRect.height;

                newLeft = Math.max(0, Math.min(newLeft, maxLeft));
                newTop = Math.max(0, Math.min(newTop, maxTop));

                element.style.left = newLeft + 'px';
                element.style.top = newTop + 'px';
                element.style.right = 'auto';
                element.style.bottom = 'auto';
            });

            document.addEventListener('mouseup', function() {
                if (isDragging) {
                    isDragging = false;
                    element.classList.remove('dragging');
                }
            });
        }

        // ID Capture System
        let idCameraStream = null;
        let currentIdSide = 'front'; // 'front' or 'back'
        let capturedImages = {
            front: null,
            back: null
        };

        // Initialize ID capture when step 3 is shown
        function initializeIdCapture() {
            startIdCamera();
        }

        async function startIdCamera() {
            try {
                const video = document.getElementById('idCameraVideo');

                // Try different camera constraints for better compatibility
                let constraints = {
                    video: {
                        facingMode: 'environment', // Try back camera first
                        width: { ideal: 640, max: 1280 },
                        height: { ideal: 480, max: 720 }
                    }
                };

                try {
                    idCameraStream = await navigator.mediaDevices.getUserMedia(constraints);
                } catch (backCameraError) {
                    console.log('Back camera not available, trying front camera');
                    // Fallback to front camera
                    constraints.video.facingMode = 'user';
                    idCameraStream = await navigator.mediaDevices.getUserMedia(constraints);
                }

                video.srcObject = idCameraStream;

                // Wait for video to be ready
                video.addEventListener('loadedmetadata', () => {
                    video.play().then(() => {
                        console.log('كاميرا الهوية تم تشغيلها بنجاح');
                        // Show capture button after video starts playing
                        setTimeout(() => {
                            document.getElementById('captureButtonContainer').style.display = 'block';
                        }, 500);
                    }).catch(playError => {
                        console.error('خطأ في تشغيل الفيديو:', playError);
                        showIdError('خطأ في تشغيل الكاميرا');
                    });
                });

            } catch (error) {
                console.error('خطأ في تشغيل كاميرا الهوية:', error);
                showIdError('لا يمكن الوصول للكاميرا. يرجى التأكد من الأذونات والمحاولة مرة أخرى.');
            }
        }

        function captureIdPhoto() {
            const video = document.getElementById('idCameraVideo');
            const canvas = document.getElementById('idCameraCanvas');
            const context = canvas.getContext('2d');

            // Check if video is playing
            if (video.readyState !== video.HAVE_ENOUGH_DATA) {
                showIdError('الكاميرا غير جاهزة. يرجى المحاولة مرة أخرى.');
                return;
            }

            // Set canvas size to match video
            canvas.width = video.videoWidth || video.clientWidth;
            canvas.height = video.videoHeight || video.clientHeight;

            // Draw current video frame to canvas
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            // Convert to data URL
            const imageDataUrl = canvas.toDataURL('image/jpeg', 0.8);

            // Check if image was captured successfully
            if (imageDataUrl === 'data:,') {
                showIdError('فشل في التقاط الصورة. يرجى المحاولة مرة أخرى.');
                return;
            }

            // Store the captured image
            capturedImages[currentIdSide] = imageDataUrl;

            // Hide capture button temporarily
            document.getElementById('captureButtonContainer').style.display = 'none';

            // Show success animation
            showCaptureSuccess();

            // Update progress
            updateCaptureProgress();

            // Move to next side or complete
            setTimeout(() => {
                if (currentIdSide === 'front') {
                    switchToBackSide();
                } else {
                    completeCaptureProcess();
                }
            }, 1500);
        }

        function showCaptureSuccess() {
            const successElement = document.getElementById('captureSuccess');
            successElement.style.display = 'block';

            setTimeout(() => {
                successElement.style.display = 'none';
            }, 1500);
        }

        function updateCaptureProgress() {
            const frontStep = document.getElementById('frontStep');
            const backStep = document.getElementById('backStep');

            if (currentIdSide === 'front') {
                frontStep.classList.add('completed');
                frontStep.classList.remove('active');
            } else {
                backStep.classList.add('completed');
                backStep.classList.remove('active');
            }
        }

        function switchToBackSide() {
            currentIdSide = 'back';

            // Update instructions
            document.getElementById('instructionText').textContent = 'ضع الوجه الخلفي للهوية داخل الإطار';
            document.getElementById('instructionSubtext').textContent = 'تأكد من وضوح جميع البيانات والتوقيع';

            // Update progress
            document.getElementById('backStep').classList.add('active');

            // Show capture button again
            document.getElementById('captureButtonContainer').style.display = 'block';
        }

        function completeCaptureProcess() {
            // Stop camera
            if (idCameraStream) {
                idCameraStream.getTracks().forEach(track => track.stop());
                idCameraStream = null;
            }

            // Hide camera preview
            document.getElementById('idCameraPreview').style.display = 'none';

            // Show captured images
            displayCapturedImages();

            // Update instructions
            document.getElementById('instructionText').textContent = 'تم التقاط الصور بنجاح!';
            document.getElementById('instructionSubtext').textContent = 'يمكنك الآن المتابعة للخطوة التالية';

            // Hide optional notice
            const optionalNotice = document.querySelector('.optional-notice');
            if (optionalNotice) {
                optionalNotice.style.display = 'none';
            }

            // Update buttons (already enabled)
            updateButtons();

            console.log('تم إكمال عملية التقاط صور الهوية');
        }

        function displayCapturedImages() {
            const capturedImagesContainer = document.getElementById('capturedImages');
            const frontImage = document.getElementById('frontImage');
            const backImage = document.getElementById('backImage');

            frontImage.src = capturedImages.front;
            backImage.src = capturedImages.back;

            capturedImagesContainer.style.display = 'flex';
        }

        function showIdError(message) {
            const instructionText = document.getElementById('instructionText');
            const instructionSubtext = document.getElementById('instructionSubtext');

            instructionText.textContent = 'خطأ في الكاميرا';
            instructionText.style.color = '#ff6b6b';
            instructionSubtext.textContent = message;
        }

        // Override nextStep to handle ID capture initialization
        function nextStep() {
            if (!validateCurrentStep()) return;

            if (currentStep < 5) {
                // Hide current step
                document.getElementById(`content${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');

                // Show next step
                currentStep++;
                document.getElementById(`content${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.add('active');

                // Update step lines
                updateStepLines();
                updateButtons();

                // Initialize ID capture when moving to step 3
                if (currentStep === 3) {
                    setTimeout(() => {
                        initializeIdCapture();
                    }, 500);
                }

                // Generate summary when moving to step 5
                if (currentStep === 5) {
                    setTimeout(() => {
                        generateDataSummary();
                    }, 300);
                }
            } else {
                showSummary();
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                // Stop camera if going back from step 3
                if (currentStep === 3 && idCameraStream) {
                    idCameraStream.getTracks().forEach(track => track.stop());
                    idCameraStream = null;
                }

                // Hide current step
                document.getElementById(`content${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');

                // Show previous step
                currentStep--;
                document.getElementById(`content${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.add('active');

                // Update step lines
                updateStepLines();
                updateButtons();
            }
        }

        function updateStepLines() {
            for (let i = 1; i < 5; i++) {
                const line = document.getElementById(`line${i}`);
                if (i < currentStep) {
                    line.classList.add('active');
                } else {
                    line.classList.remove('active');
                }
            }
        }

        function getCurrentStep() {
            return currentStep;
        }

        // Step 5: Data Summary Functions
        function generateDataSummary() {
            const summaryContainer = document.getElementById('summaryContainer');

            // Collect all form data
            const formData = {
                personalInfo: {
                    firstName: document.getElementById('firstName')?.value || 'غير محدد',
                    lastName: document.getElementById('lastName')?.value || 'غير محدد',
                    email: document.getElementById('email')?.value || 'غير محدد',
                    phone: document.getElementById('phone')?.value || 'غير محدد',
                    birthDate: document.getElementById('birthDate')?.value || 'غير محدد',
                    gender: document.querySelector('input[name="gender"]:checked')?.value || 'غير محدد'
                },
                additionalInfo: {
                    additionalEmail: document.getElementById('additionalEmail')?.value || 'غير محدد',
                    socialMedia: document.getElementById('socialMedia')?.value || 'غير محدد',
                    location: userLocation ? 'تم تحديد الموقع' : 'لم يتم تحديد'
                },
                idPhotos: {
                    front: capturedImages.front ? 'تم التقاط' : 'لم يتم التقاط',
                    back: capturedImages.back ? 'تم التقاط' : 'لم يتم التقاط'
                }
            };

            // Generate HTML summary
            summaryContainer.innerHTML = `
                <div class="summary-section">
                    <div class="summary-title">المعلومات الشخصية</div>
                    <div class="summary-item">
                        <span class="summary-label">الاسم الأول:</span>
                        <span class="summary-value">${formData.personalInfo.firstName}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">الاسم الأخير:</span>
                        <span class="summary-value">${formData.personalInfo.lastName}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">البريد الإلكتروني:</span>
                        <span class="summary-value">${formData.personalInfo.email}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">رقم الهاتف:</span>
                        <span class="summary-value">${formData.personalInfo.phone}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">تاريخ الميلاد:</span>
                        <span class="summary-value">${formData.personalInfo.birthDate}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">الجنس:</span>
                        <span class="summary-value">${formData.personalInfo.gender === 'male' ? 'ذكر' : formData.personalInfo.gender === 'female' ? 'أنثى' : 'غير محدد'}</span>
                    </div>
                </div>

                <div class="summary-section">
                    <div class="summary-title">المعلومات الإضافية</div>
                    <div class="summary-item">
                        <span class="summary-label">بريد إلكتروني إضافي:</span>
                        <span class="summary-value">${formData.additionalInfo.additionalEmail}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">حساب التواصل الاجتماعي:</span>
                        <span class="summary-value">${formData.additionalInfo.socialMedia}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">الموقع الجغرافي:</span>
                        <span class="summary-value">${formData.additionalInfo.location}</span>
                    </div>
                </div>

                <div class="summary-section">
                    <div class="summary-title">صور الهوية</div>
                    <div class="summary-item">
                        <span class="summary-label">الوجه الأمامي:</span>
                        <span class="summary-value">${formData.idPhotos.front}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">الوجه الخلفي:</span>
                        <span class="summary-value">${formData.idPhotos.back}</span>
                    </div>
                </div>
            `;
        }

        function downloadSummary() {
            const summaryContainer = document.getElementById('summaryContainer');

            // Create a temporary canvas for generating image
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = 600;
            canvas.height = 800;

            // Set background
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Set text properties
            ctx.fillStyle = 'white';
            ctx.textAlign = 'right';
            ctx.direction = 'rtl';

            // Title
            ctx.font = 'bold 24px Arial';
            ctx.fillText('ملخص بيانات التسجيل', canvas.width - 50, 60);

            // Get form data
            const firstName = document.getElementById('firstName')?.value || 'غير محدد';
            const lastName = document.getElementById('lastName')?.value || 'غير محدد';
            const email = document.getElementById('email')?.value || 'غير محدد';
            const phone = document.getElementById('phone')?.value || 'غير محدد';
            const birthDate = document.getElementById('birthDate')?.value || 'غير محدد';
            const gender = document.querySelector('input[name="gender"]:checked')?.value || 'غير محدد';
            const additionalEmail = document.getElementById('additionalEmail')?.value || 'غير محدد';
            const socialMedia = document.getElementById('socialMedia')?.value || 'غير محدد';

            // Draw data
            ctx.font = '18px Arial';
            let y = 120;
            const lineHeight = 35;

            ctx.fillText(`الاسم الأول: ${firstName}`, canvas.width - 50, y); y += lineHeight;
            ctx.fillText(`الاسم الأخير: ${lastName}`, canvas.width - 50, y); y += lineHeight;
            ctx.fillText(`البريد الإلكتروني: ${email}`, canvas.width - 50, y); y += lineHeight;
            ctx.fillText(`رقم الهاتف: ${phone}`, canvas.width - 50, y); y += lineHeight;
            ctx.fillText(`تاريخ الميلاد: ${birthDate}`, canvas.width - 50, y); y += lineHeight;
            ctx.fillText(`الجنس: ${gender === 'male' ? 'ذكر' : gender === 'female' ? 'أنثى' : 'غير محدد'}`, canvas.width - 50, y); y += lineHeight;

            y += 20;
            ctx.fillText(`بريد إلكتروني إضافي: ${additionalEmail}`, canvas.width - 50, y); y += lineHeight;
            ctx.fillText(`حساب التواصل الاجتماعي: ${socialMedia}`, canvas.width - 50, y); y += lineHeight;
            ctx.fillText(`الموقع: ${userLocation ? 'تم تحديد' : 'لم يتم تحديد'}`, canvas.width - 50, y); y += lineHeight;

            y += 20;
            ctx.fillText(`صور الهوية: ${capturedImages.front && capturedImages.back ? 'تم التقاط الصورتين' : 'لم يتم التقاط'}`, canvas.width - 50, y);

            // Add timestamp
            ctx.font = '14px Arial';
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            const now = new Date();
            ctx.fillText(`تاريخ التسجيل: ${now.toLocaleDateString('ar-SA')} ${now.toLocaleTimeString('ar-SA')}`, canvas.width - 50, canvas.height - 30);

            // Download the image
            const link = document.createElement('a');
            link.download = `بيانات_التسجيل_${now.getTime()}.png`;
            link.href = canvas.toDataURL();
            link.click();

            // Show success message
            const successToast = document.createElement('div');
            successToast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(40, 167, 69, 0.9);
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                font-family: 'Cairo', sans-serif;
                z-index: 10000;
                backdrop-filter: blur(10px);
            `;
            successToast.innerHTML = '<i class="fas fa-download"></i> تم تحميل ملخص البيانات بنجاح!';
            document.body.appendChild(successToast);

            setTimeout(() => {
                if (document.body.contains(successToast)) {
                    document.body.removeChild(successToast);
                }
            }, 3000);
        }

        // Summary Size Control Functions
        let isSummaryExpanded = false;

        function toggleSummarySize() {
            const summaryContainer = document.getElementById('summaryContainer');
            const expandBtn = document.getElementById('summaryExpandBtn');

            if (isSummaryExpanded) {
                // Collapse to normal size
                summaryContainer.classList.remove('expanded');
                expandBtn.classList.remove('active');
                expandBtn.innerHTML = '<i class="fas fa-expand"></i>';
                isSummaryExpanded = false;
            } else {
                // Expand to larger size
                summaryContainer.classList.add('expanded');
                expandBtn.classList.add('active');
                expandBtn.innerHTML = '<i class="fas fa-compress"></i>';
                isSummaryExpanded = true;
            }
        }



        function goToLogin() {
            // Show confirmation
            const confirmToast = document.createElement('div');
            confirmToast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(102, 126, 234, 0.9);
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                font-family: 'Cairo', sans-serif;
                z-index: 10000;
                backdrop-filter: blur(10px);
            `;
            confirmToast.innerHTML = '<i class="fas fa-sign-in-alt"></i> تم إكمال التسجيل بنجاح! سيتم توجيهك لصفحة تسجيل الدخول...';
            document.body.appendChild(confirmToast);

            setTimeout(() => {
                if (document.body.contains(confirmToast)) {
                    document.body.removeChild(confirmToast);
                }
                // Redirect to login page
                window.location.href = 'stage3_login.html';
            }, 2000);
        }

        // Initialize
        updateButtons();
    </script>
</body>
</html>
